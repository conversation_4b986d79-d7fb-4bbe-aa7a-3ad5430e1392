<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Mandrill API Key
    |--------------------------------------------------------------------------
    |
    | This is the Mandrill API key used for sending emails through Mandrill.
    | In production, this should be set in your .env file.
    |
    */
    'key' => env('MANDRILL_KEY'),

    /*
    |--------------------------------------------------------------------------
    | Mandrill Webhook Secret
    |--------------------------------------------------------------------------
    |
    | If you're using Mandrill webhooks, this is the secret key used to
    | validate incoming webhook requests.
    |
    */
    'webhook_secret' => env('MANDRILL_WEBHOOK_SECRET'),

    /*
    |--------------------------------------------------------------------------
    | Default Sender
    |--------------------------------------------------------------------------
    |
    | This is the default sender email address and name used for all emails
    | sent through Mandrill.
    |
    */
    'from' => [
        'address' => env('MAIL_FROM_ADDRESS', '<EMAIL>'),
        'name' => env('MAIL_FROM_NAME', 'Example'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Subaccount
    |--------------------------------------------------------------------------
    |
    | If you're using a Mandrill subaccount, specify the subaccount name here.
    |
    */
    'subaccount' => env('MANDRILL_SUBACCOUNT'),
];
