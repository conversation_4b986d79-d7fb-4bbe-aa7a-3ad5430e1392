<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Daily Sales Summary Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration options for the daily sales summary email system.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Email Schedule
    |--------------------------------------------------------------------------
    |
    | The time at which daily sales summary emails should be sent.
    | Format: HH:MM (24-hour format)
    |
    */
    'send_time' => env('DAILY_SALES_SUMMARY_TIME', '08:00'),

    /*
    |--------------------------------------------------------------------------
    | Timezone
    |--------------------------------------------------------------------------
    |
    | The timezone to use for scheduling the daily emails.
    |
    */
    'timezone' => env('DAILY_SALES_SUMMARY_TIMEZONE', 'Europe/Amsterdam'),

    /*
    |--------------------------------------------------------------------------
    | Email Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for the email content and behavior.
    |
    */
    'email' => [
        'from_name' => env('DAILY_SALES_SUMMARY_FROM_NAME', 'Sales System'),
        'from_address' => env('DAILY_SALES_SUMMARY_FROM_ADDRESS', '<EMAIL>'),
        'subject_prefix' => env('DAILY_SALES_SUMMARY_SUBJECT_PREFIX', 'Daily Sales Summary'),
    ],

    /*
    |--------------------------------------------------------------------------
    | User Filtering
    |--------------------------------------------------------------------------
    |
    | Options for filtering which users receive the daily summary emails.
    |
    */
    'user_filters' => [
        'require_sales_history' => true,
        'require_valid_email' => true,
        'exclude_inactive_users' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging
    |--------------------------------------------------------------------------
    |
    | Enable or disable detailed logging for the daily sales summary process.
    |
    */
    'logging' => [
        'enabled' => env('DAILY_SALES_SUMMARY_LOGGING', true),
        'log_successful_sends' => true,
        'log_failed_sends' => true,
        'log_summary_stats' => true,
    ],
];
