<?php

use App\Http\Controllers\CustomerController;
use App\Http\Controllers\ManagerController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\PlanTaskController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ProspectController;
use App\Http\Controllers\PurchaseController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\SupplierController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Storage;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

//Route::get('/', function () {
//    $jsonContent = Storage::disk('public')->get('Relaties.json');
//    $jsonContent = str_replace("\xEF\xBB\xBF", '', $jsonContent);
//    $jsonContent = preg_replace('/[\x00-\x1F\x7F]/u', '', $jsonContent);
//    $lastId = 0;
//    $content = json_decode($jsonContent, true);
//
//    if (json_last_error() !== JSON_ERROR_NONE) {
//        dump("JSON decode error: " . json_last_error_msg());
//    } else {
//
//        foreach ($content as $relation) {
//            switch ($relation['cicntp'][0]['humres'][0]['Type']) {
//                case 'Leverancier':
//                    $model = new \App\Models\Supplier();
//                    break;
//                case 'Prospect':
//                    $model = new \App\Models\Prospect();
//                    break;
//                case 'Klant':
//                    $model = new \App\Models\Customer();
//                    break;
//            }
//            $model->exact_id = (int) $relation['cmp_code'];
//            $model->company_name = $relation['cmp_name'];
//            $model->name = $relation['cicntp'][0]['firstname'] . ' ' . $relation['cicntp'][0]['lastname'];
//            $model->country = 'NL';
//            $model->telephone = $relation['telefoon'];
//            if (!array_key_exists('adres', $relation)) {
//                continue;
//            }
//            if (preg_match('/^(?P<street>.*[^\d\s])\s*(?P<number>\d+.*)?$/', $relation['adres'], $matches)) {
//                $model->street_name = $matches['street'];
//                $model->housenumber = isset($matches['number']) ? $matches['number'] : null;
//            } else {
//                $model->street_name = $relation['adres'];
//                $model->housenumber = $relation['adres'];
//            }
//            if (!array_key_exists('postcode',$relation)) {
//                continue;
//            }
//            $model->postal_code = $relation['postcode'];
//            if (!array_key_exists('plaats', $relation)) {
//                continue;
//            }
//            $model->city = $relation['plaats'];
//            $model->save();
//            foreach($relation['cicntp'] as $contactPerson) {
//                $model->contactPersons()->create([
//                    'name' => $contactPerson['firstname'] . ' ' . $contactPerson['lastname'],
//                    'email' => $contactPerson['emailContactpersoon'],
//                ]);
//            }
//        }
//    }
//    $test = 'test';
//});

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
    'require.2fa'
])->group(function () {
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');

    // CUSTOMERS
    Route::get('customer', [CustomerController::class, 'index'])->name('customer.index');
    Route::get('customer/edit/{customer}', [CustomerController::class, 'edit'])->name('customer.edit');
    Route::post('customer/update/{customer}', [CustomerController::class, 'update'])->name('customer.update');
    Route::get('/customer/{customer}/activities', [CustomerController::class, 'showActivities'])->name('customer.activities');
    Route::get('livewire/customer-table/get-customers', [App\Livewire\CustomerTable::class, 'getCustomers'])->name('livewire.customer-table.get-customers');

    // SUPPLIERS
    Route::get('supplier', [SupplierController::class, 'index'])->name('supplier.index');
    Route::get('supplier/edit/{supplier}', [SupplierController::class, 'edit'])->name('supplier.edit');
    Route::post('supplier/update/{supplier}', [SupplierController::class, 'update'])->name('supplier.update');
    Route::get('/supplier/{supplier}/activities', [SupplierController::class, 'showActivities'])->name('supplier.activities');
    Route::get('livewire/supplier-table/get-suppliers', [App\Livewire\SupplierTable::class, 'getSuppliers'])->name('livewire.supplier-table.get-suppliers');

    // PROSPECTS
    Route::get('prospect', [ProspectController::class, 'index'])->name('prospect.index');
    Route::get('prospect/edit/{prospect}', [ProspectController::class, 'edit'])->name('prospect.edit');
    Route::post('prospect/update/{prospect}', [ProspectController::class, 'update'])->name('prospect.update');
    Route::get('/prospects/{prospect}/activities', [ProspectController::class, 'showActivities'])->name('prospects.activities');
    Route::get('livewire/prospect-table/get-prospects', [App\Livewire\ProspectTable::class, 'getProspects'])->name('livewire.prospect-table.get-prospects');

    // PLANLIJST
    Route::get('planlijst', [PlanTaskController::class, 'index'])->name('planlist.index');
    Route::get('planlijst/{planlist}', [PlanTaskController::class, 'showSharedPlanlist'])->name('planlist.shared');

    // Business
    Route::get('handel', [PurchaseController::class, 'index'])->name('business.index');
    Route::get('livewire/purchase-table/get-purchases', [App\Livewire\PurchaseTable::class, 'getPurchases'])->name('livewire.purchase-table.get-purchases');
    Route::get('handel/new', [PurchaseController::class, 'new'])->name('purchase.new');
    Route::get('handel/edit/{purchase}', [PurchaseController::class, 'edit'])->name('purchase.edit');
    Route::get('livewire/planning-table/get-purchases', [App\Livewire\PlanningTable::class, 'getPurchases'])->name('livewire.planning-table.get-purchases');
    Route::get('planning', [PurchaseController::class, 'planning'])->name('planning.index');
    Route::get('handel/afgerond', [PurchaseController::class, 'completed'])->name('completed.index');
    Route::get('livewire/completed-table/get-purchases', [App\Livewire\CompletedTable::class, 'getPurchases'])->name('livewire.completed-table.get-purchases');
    Route::get('handel/completed/{purchase}', [PurchaseController::class, 'completedPurchase'])->name('completed.purchase');
    Route::get('overview', [OrderController::class, 'puchaseSale'])->name('overview.index');

    // Proudcten
    Route::get('products', [ProductController::class, 'index'])->name('products.index');
    Route::get('products/edit/{product}', [ProductController::class, 'edit'])->name('products.edit');
    Route::get('livewire/product-table/get-products', [App\Livewire\ProductTable::class, 'getProducts'])->name('livewire.product-table.get-products');

    // SETTINGS
    Route::get('settings', [SettingsController::class, 'index'])->name('settings.index');

    // TRUCK LOCATIONS
    Route::get('truck-locations/map', [\App\Http\Controllers\TruckLocationController::class, 'map'])->name('truck-locations.map');
    Route::get('truck-locations/simple-map', [\App\Http\Controllers\TruckLocationController::class, 'simpleMap'])->name('truck-locations.simple-map');
    Route::get('truck-locations/basic-map', [\App\Http\Controllers\TruckLocationController::class, 'basicMap'])->name('truck-locations.basic-map');
    Route::get('truck-locations/latest', [\App\Http\Controllers\TruckLocationController::class, 'getLatestLocations'])->name('truck-locations.latest');
    Route::get('truck-locations/history', [\App\Http\Controllers\TruckLocationController::class, 'getLocationHistory'])->name('truck-locations.history');

    // MANAGER
    Route::get('manager/tasks', [ManagerController::class, 'allTasks'])->name('manager.tasks');
    Route::get('manager/performance', [ManagerController::class, 'employeePerformance'])->name('manager.performance');

    Route::get('test', [\App\Http\Controllers\ComlogController::class, 'test'])->name('test');
});
