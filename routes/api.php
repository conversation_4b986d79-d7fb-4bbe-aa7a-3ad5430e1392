<?php

use App\Http\Controllers\ComlogController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\FileController;
use App\Http\Controllers\OrderController;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public endpoints (no authentication required)
// Truck location endpoint - no authentication required for external devices
Route::post('/truck-location', [\App\Http\Controllers\TruckLocationController::class, 'store']);

// Signature submission endpoint - for external apps
Route::post('/purchase-signature', [\App\Http\Controllers\TruckLocationController::class, 'storeSignature']);

// External data import endpoints - for third-party systems
Route::post('data', [ComlogController::class, 'saveData']);
Route::post('products', [ComlogController::class, 'saveData']);
Route::post('relaties', [ComlogController::class, 'saveData']);
Route::post('openpost', [ComlogController::class, 'saveData'])
    ->withoutMiddleware(['throttle:api']);

// Protected endpoints (require authentication and 2FA)
Route::middleware(['auth:sanctum', 'require.2fa'])->group(function () {
    Route::get('/user', function (Request $request) {
        return $request->user();
    });

    // Own transport purchases endpoint
    Route::get('/own-transport-purchases', [\App\Http\Controllers\TruckLocationController::class, 'getOwnTransportPurchases']);

    // Get signature endpoint
    Route::get('/purchase-signature', [\App\Http\Controllers\TruckLocationController::class, 'getSignature']);

    // File upload endpoint
    Route::post('fileupload', [FileController::class, 'store']);

    // Order endpoints
    Route::get('orders', [OrderController::class, 'getOrder']);
    Route::get('bestellingen', [OrderController::class, 'getPurchaseOrder']);
});