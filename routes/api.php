<?php

use App\Http\Controllers\ComlogController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\FileController;
use App\Http\Controllers\OrderController;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Truck location endpoint - no authentication required for simplicity
Route::post('/truck-location', [\App\Http\Controllers\TruckLocationController::class, 'store']);

// Own transport purchases endpoint
Route::get('/own-transport-purchases', [\App\Http\Controllers\TruckLocationController::class, 'getOwnTransportPurchases']);

// Signature submission endpoint
Route::post('/purchase-signature', [\App\Http\Controllers\TruckLocationController::class, 'storeSignature']);

// Get signature endpoint
Route::get('/purchase-signature', [\App\Http\Controllers\TruckLocationController::class, 'getSignature']);

Route::post('fileupload', [FileController::class, 'store']);

//Route::post('relaties', [CustomerController::class, 'parseRelations']);
Route::post('data', [ComlogController::class, 'saveData']);
Route::post('products', [ComlogController::class, 'saveData']);
Route::post('relaties', [ComlogController::class, 'saveData']);
Route::post('openpost', [ComlogController::class, 'saveData'])
    ->withoutMiddleware(['throttle:api']);
Route::get('orders', [OrderController::class, 'getOrder']);
Route::get('bestellingen', [OrderController::class, 'getPurchaseOrder']);