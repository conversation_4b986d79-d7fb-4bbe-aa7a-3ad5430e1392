<?php

namespace Tests\Feature;

use App\Models\Customer;
use App\Models\Product;
use App\Models\Supplier;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PriceAgreementTest extends TestCase
{
    use RefreshDatabase;

    public function test_supplier_can_have_multiple_price_agreements_for_same_product()
    {
        // Create test data
        $user = User::factory()->create();
        $supplier = Supplier::factory()->create();
        $product = Product::factory()->create();

        // Add first price agreement
        $supplier->products()->attach($product->id, [
            'unit_price' => 100.00,
            'unit_type' => 'TON',
            'end_date' => null,
            'default_qty' => 10
        ]);

        // Add second price agreement for the same product
        $supplier->products()->attach($product->id, [
            'unit_price' => 120.00,
            'unit_type' => 'TON',
            'end_date' => null,
            'default_qty' => 15
        ]);

        // Verify both agreements exist
        $agreements = $supplier->products()->where('product_id', $product->id)->get();
        $this->assertCount(2, $agreements);

        // Verify prices are different
        $prices = $agreements->pluck('pivot.unit_price')->toArray();
        $this->assertContains(100.00, $prices);
        $this->assertContains(120.00, $prices);
    }

    public function test_customer_can_have_multiple_price_agreements_for_same_product()
    {
        // Create test data
        $user = User::factory()->create();
        $customer = Customer::factory()->create();
        $product = Product::factory()->create();

        // Add first price agreement
        $customer->products()->attach($product->id, [
            'unit_price' => 150.00,
            'unit_type' => 'TON',
            'end_date' => null,
            'default_qty' => 5
        ]);

        // Add second price agreement for the same product
        $customer->products()->attach($product->id, [
            'unit_price' => 175.00,
            'unit_type' => 'TON',
            'end_date' => null,
            'default_qty' => 8
        ]);

        // Verify both agreements exist
        $agreements = $customer->products()->where('product_id', $product->id)->get();
        $this->assertCount(2, $agreements);

        // Verify prices are different
        $prices = $agreements->pluck('pivot.unit_price')->toArray();
        $this->assertContains(150.00, $prices);
        $this->assertContains(175.00, $prices);
    }

    public function test_active_products_returns_most_recent_agreement()
    {
        // Create test data
        $supplier = Supplier::factory()->create();
        $product = Product::factory()->create();

        // Add older price agreement
        $supplier->products()->attach($product->id, [
            'unit_price' => 100.00,
            'unit_type' => 'TON',
            'end_date' => null,
            'default_qty' => 10,
            'created_at' => now()->subDays(5),
            'updated_at' => now()->subDays(5)
        ]);

        // Add newer price agreement
        $supplier->products()->attach($product->id, [
            'unit_price' => 120.00,
            'unit_type' => 'TON',
            'end_date' => null,
            'default_qty' => 15,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Get active products (should return most recent)
        $activeProducts = $supplier->activeProducts()->get();
        
        // Should have the product
        $this->assertCount(1, $activeProducts);
        
        // Should have the most recent price (120.00)
        $this->assertEquals(120.00, $activeProducts->first()->pivot->unit_price);
    }
}
