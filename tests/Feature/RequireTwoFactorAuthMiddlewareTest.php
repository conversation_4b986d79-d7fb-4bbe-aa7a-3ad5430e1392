<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Fortify\Features;
use Tests\TestCase;

class RequireTwoFactorAuthMiddlewareTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_without_2fa_is_redirected_to_profile()
    {
        if (! Features::canManageTwoFactorAuthentication()) {
            $this->markTestSkipped('Two factor authentication is not enabled.');
            return;
        }

        $user = User::factory()->create();

        $response = $this->actingAs($user)->get('/dashboard');

        $response->assertRedirect(route('profile.show'));
        $response->assertSessionHas('status', 'two-factor-required');
    }

    public function test_user_with_2fa_can_access_dashboard()
    {
        if (! Features::canManageTwoFactorAuthentication()) {
            $this->markTestSkipped('Two factor authentication is not enabled.');
            return;
        }

        $user = User::factory()->create();
        
        // Enable 2FA for the user
        $user->forceFill([
            'two_factor_secret' => encrypt('test-secret'),
            'two_factor_confirmed_at' => now(),
        ])->save();

        $response = $this->actingAs($user)->get('/dashboard');

        $response->assertStatus(200);
    }

    public function test_profile_page_is_accessible_without_2fa()
    {
        if (! Features::canManageTwoFactorAuthentication()) {
            $this->markTestSkipped('Two factor authentication is not enabled.');
            return;
        }

        $user = User::factory()->create();

        $response = $this->actingAs($user)->get(route('profile.show'));

        $response->assertStatus(200);
    }

    public function test_api_endpoint_requires_2fa()
    {
        if (! Features::canManageTwoFactorAuthentication()) {
            $this->markTestSkipped('Two factor authentication is not enabled.');
            return;
        }

        $user = User::factory()->create();

        $response = $this->actingAs($user)->getJson('/api/user');

        $response->assertStatus(403);
        $response->assertJson([
            'message' => 'Two-factor authentication is required.',
        ]);
    }

    public function test_public_api_endpoints_work_without_auth()
    {
        $response = $this->postJson('/api/truck-location', [
            'latitude' => 52.3676,
            'longitude' => 4.9041,
            'timestamp' => now()->timestamp,
            'number_plate' => 'TEST-123'
        ]);

        $response->assertStatus(201);
    }
}
