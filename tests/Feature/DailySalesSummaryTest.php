<?php

namespace Tests\Feature;

use App\Mail\DailySalesSummary;
use App\Models\Customer;
use App\Models\Product;
use App\Models\Purchase;
use App\Models\Sale;
use App\Models\ShippingAddress;
use App\Models\Supplier;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class DailySalesSummaryTest extends TestCase
{
    use RefreshDatabase;

    public function test_daily_sales_summary_command_sends_emails_to_users_with_sales()
    {
        Mail::fake();

        // Create test data
        $user1 = User::factory()->create(['name' => '<PERSON>e', 'email' => '<EMAIL>']);
        $user2 = User::factory()->create(['name' => '<PERSON> Smith', 'email' => '<EMAIL>']);
        $userWithoutSales = User::factory()->create(['name' => 'No Sales User', 'email' => '<EMAIL>']);

        $supplier = Supplier::factory()->create(['company_name' => 'Test Supplier']);
        $product = Product::factory()->create(['name' => 'Test Product', 'sku' => 'TEST-001']);
        $customer = Customer::factory()->create(['company_name' => 'Test Customer']);
        $shippingAddress = ShippingAddress::factory()->create(['customer_id' => $customer->id]);

        // Create a purchase completed yesterday
        $yesterday = Carbon::yesterday();
        $purchase = Purchase::factory()->create([
            'supplier_id' => $supplier->id,
            'product_id' => $product->id,
            'completed' => true,
            'purchase_number' => 'P001',
            'quantity' => 100,
            'price' => 50.00,
            'delivery_date' => $yesterday->addDay(),
            'updated_at' => $yesterday
        ]);

        // Create sales for user1 and user2
        Sale::factory()->create([
            'purchase_id' => $purchase->id,
            'customer_id' => $customer->id,
            'product_id' => $product->id,
            'shipping_address_id' => $shippingAddress->id,
            'user_id' => $user1->id,
            'quantity' => 30,
            'price' => 60.00,
            'total_price' => 1800.00,
            'transport_cost' => 50.00
        ]);

        Sale::factory()->create([
            'purchase_id' => $purchase->id,
            'customer_id' => $customer->id,
            'product_id' => $product->id,
            'shipping_address_id' => $shippingAddress->id,
            'user_id' => $user2->id,
            'quantity' => 20,
            'price' => 65.00,
            'total_price' => 1300.00,
            'transport_cost' => 30.00
        ]);

        // Run the command
        Artisan::call('email:daily-sales-summary');

        // Assert emails were sent to users with sales
        Mail::assertSent(DailySalesSummary::class, 2);
        
        Mail::assertSent(DailySalesSummary::class, function ($mail) use ($user1) {
            return $mail->hasTo($user1->email) && 
                   $mail->user->id === $user1->id &&
                   $mail->completedPurchases->count() === 1;
        });

        Mail::assertSent(DailySalesSummary::class, function ($mail) use ($user2) {
            return $mail->hasTo($user2->email) && 
                   $mail->user->id === $user2->id &&
                   $mail->completedPurchases->count() === 1;
        });

        // Assert no email was sent to user without sales
        Mail::assertNotSent(DailySalesSummary::class, function ($mail) use ($userWithoutSales) {
            return $mail->hasTo($userWithoutSales->email);
        });
    }

    public function test_daily_sales_summary_with_no_completed_purchases()
    {
        Mail::fake();

        $user = User::factory()->create(['email' => '<EMAIL>']);
        
        // Create a sale but no completed purchases from yesterday
        $customer = Customer::factory()->create();
        $product = Product::factory()->create();
        $supplier = Supplier::factory()->create();
        $shippingAddress = ShippingAddress::factory()->create(['customer_id' => $customer->id]);

        $purchase = Purchase::factory()->create([
            'supplier_id' => $supplier->id,
            'product_id' => $product->id,
            'completed' => false, // Not completed
            'updated_at' => Carbon::yesterday()
        ]);

        Sale::factory()->create([
            'purchase_id' => $purchase->id,
            'customer_id' => $customer->id,
            'product_id' => $product->id,
            'shipping_address_id' => $shippingAddress->id,
            'user_id' => $user->id
        ]);

        // Run the command
        Artisan::call('email:daily-sales-summary');

        // No emails should be sent since no purchases were completed
        Mail::assertNothingSent();
    }

    public function test_daily_sales_summary_mailable_content()
    {
        $user = User::factory()->create(['name' => 'Test User']);
        $supplier = Supplier::factory()->create(['company_name' => 'Test Supplier']);
        $product = Product::factory()->create(['name' => 'Test Product', 'sku' => 'TEST-001']);
        $customer = Customer::factory()->create(['company_name' => 'Test Customer']);
        $shippingAddress = ShippingAddress::factory()->create([
            'customer_id' => $customer->id,
            'company_name' => 'Delivery Company',
            'street' => 'Test Street',
            'housenumber' => '123',
            'postal_code' => '1234AB',
            'city' => 'Test City'
        ]);

        $purchase = Purchase::factory()->create([
            'supplier_id' => $supplier->id,
            'product_id' => $product->id,
            'purchase_number' => 'P001',
            'quantity' => 50,
            'price' => 40.00,
            'delivery_date' => Carbon::today()
        ]);

        $sale = Sale::factory()->create([
            'purchase_id' => $purchase->id,
            'customer_id' => $customer->id,
            'product_id' => $product->id,
            'shipping_address_id' => $shippingAddress->id,
            'user_id' => $user->id,
            'quantity' => 25,
            'price' => 50.00,
            'total_price' => 1250.00,
            'transport_cost' => 75.00
        ]);

        // Add the userSales relationship manually for testing
        $purchase->userSales = collect([$sale]);
        $completedPurchases = collect([$purchase]);

        $mailable = new DailySalesSummary($user, $completedPurchases, Carbon::yesterday());

        // Test envelope
        $envelope = $mailable->envelope();
        $this->assertEquals('Daily Sales Summary - ' . Carbon::yesterday()->format('d-m-Y'), $envelope->subject);

        // Test calculated stats
        $this->assertEquals(1, $mailable->totalSales);
        $this->assertEquals(1250.00, $mailable->totalRevenue);
        $this->assertEquals(75.00, $mailable->totalTransportCosts);
    }

    public function test_command_with_specific_date_option()
    {
        Mail::fake();

        $user = User::factory()->create(['email' => '<EMAIL>']);
        $supplier = Supplier::factory()->create();
        $product = Product::factory()->create();
        $customer = Customer::factory()->create();
        $shippingAddress = ShippingAddress::factory()->create(['customer_id' => $customer->id]);

        // Create purchase completed on a specific date
        $specificDate = Carbon::create(2024, 1, 15);
        $purchase = Purchase::factory()->create([
            'supplier_id' => $supplier->id,
            'product_id' => $product->id,
            'completed' => true,
            'updated_at' => $specificDate
        ]);

        Sale::factory()->create([
            'purchase_id' => $purchase->id,
            'customer_id' => $customer->id,
            'product_id' => $product->id,
            'shipping_address_id' => $shippingAddress->id,
            'user_id' => $user->id
        ]);

        // Run command with specific date
        Artisan::call('email:daily-sales-summary', ['--date' => '2024-01-15']);

        // Assert email was sent
        Mail::assertSent(DailySalesSummary::class, function ($mail) use ($user, $specificDate) {
            return $mail->hasTo($user->email) &&
                   $mail->summaryDate->isSameDay($specificDate);
        });
    }

    public function test_command_with_specific_purchase_number()
    {
        Mail::fake();

        $user1 = User::factory()->create(['name' => 'User One', 'email' => '<EMAIL>']);
        $user2 = User::factory()->create(['name' => 'User Two', 'email' => '<EMAIL>']);
        $supplier = Supplier::factory()->create(['company_name' => 'Test Supplier']);
        $product = Product::factory()->create(['name' => 'Test Product', 'sku' => 'TEST-001']);
        $customer = Customer::factory()->create(['company_name' => 'Test Customer']);
        $shippingAddress = ShippingAddress::factory()->create(['customer_id' => $customer->id]);

        // Create a specific purchase for testing
        $testPurchase = Purchase::factory()->create([
            'supplier_id' => $supplier->id,
            'product_id' => $product->id,
            'purchase_number' => 'TEST-P001',
            'completed' => true,
            'quantity' => 100,
            'price' => 50.00
        ]);

        // Create sales for both users
        Sale::factory()->create([
            'purchase_id' => $testPurchase->id,
            'customer_id' => $customer->id,
            'product_id' => $product->id,
            'shipping_address_id' => $shippingAddress->id,
            'user_id' => $user1->id,
            'quantity' => 30,
            'price' => 60.00,
            'total_price' => 1800.00,
            'transport_cost' => 50.00
        ]);

        Sale::factory()->create([
            'purchase_id' => $testPurchase->id,
            'customer_id' => $customer->id,
            'product_id' => $product->id,
            'shipping_address_id' => $shippingAddress->id,
            'user_id' => $user2->id,
            'quantity' => 20,
            'price' => 65.00,
            'total_price' => 1300.00,
            'transport_cost' => 30.00
        ]);

        // Run command with specific purchase number
        $exitCode = Artisan::call('email:daily-sales-summary', ['--purchase' => 'TEST-P001']);

        // Assert command succeeded
        $this->assertEquals(0, $exitCode);

        // Assert emails were sent to both users
        Mail::assertSent(DailySalesSummary::class, 2);

        Mail::assertSent(DailySalesSummary::class, function ($mail) use ($user1) {
            return $mail->hasTo($user1->email) &&
                   $mail->user->id === $user1->id &&
                   $mail->completedPurchases->count() === 1 &&
                   $mail->completedPurchases->first()->purchase_number === 'TEST-P001';
        });

        Mail::assertSent(DailySalesSummary::class, function ($mail) use ($user2) {
            return $mail->hasTo($user2->email) &&
                   $mail->user->id === $user2->id &&
                   $mail->completedPurchases->count() === 1 &&
                   $mail->completedPurchases->first()->purchase_number === 'TEST-P001';
        });
    }

    public function test_command_with_nonexistent_purchase_number()
    {
        Mail::fake();

        // Run command with non-existent purchase number
        $exitCode = Artisan::call('email:daily-sales-summary', ['--purchase' => 'NONEXISTENT']);

        // Assert command failed
        $this->assertEquals(1, $exitCode);

        // Assert no emails were sent
        Mail::assertNothingSent();
    }

    public function test_command_with_purchase_having_no_sales()
    {
        Mail::fake();

        $supplier = Supplier::factory()->create();
        $product = Product::factory()->create();

        // Create purchase with no sales
        $purchase = Purchase::factory()->create([
            'supplier_id' => $supplier->id,
            'product_id' => $product->id,
            'purchase_number' => 'NO-SALES-P001',
            'completed' => true
        ]);

        // Run command with purchase that has no sales
        $exitCode = Artisan::call('email:daily-sales-summary', ['--purchase' => 'NO-SALES-P001']);

        // Assert command succeeded (no error, just no emails to send)
        $this->assertEquals(0, $exitCode);

        // Assert no emails were sent
        Mail::assertNothingSent();
    }
}
