<?php

namespace Tests\Feature;

use App\Models\Purchase;
use App\Models\Sale;
use App\Models\User;
use App\Models\Customer;
use App\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SaleTransportCostTest extends TestCase
{
    use RefreshDatabase;

    public function test_transport_cost_per_unit_calculation()
    {
        $user = User::factory()->create();
        $customer = Customer::factory()->create();
        $product = Product::factory()->create();
        $purchase = Purchase::factory()->create([
            'quantity' => 10,
            'qty_type' => 'TON'
        ]);
        
        $sale = Sale::factory()->create([
            'purchase_id' => $purchase->id,
            'customer_id' => $customer->id,
            'product_id' => $product->id,
            'quantity' => 5,
            'transport_cost' => 0
        ]);

        $this->actingAs($user);

        $component = \Livewire\Livewire::test('edit-purchase', ['purchaseId' => $purchase->id])
            ->call('openTransportCostModal', $sale->id)
            ->set('transportCostType', 'per_unit')
            ->set('transportCostPerUnit', 10.50);
        
        $calculatedCost = $component->call('calculateSaleTransportCost');
        
        $this->assertEquals(52.50, $calculatedCost); // 5 * 10.50
    }

    public function test_transport_cost_total_calculation()
    {
        $user = User::factory()->create();
        $customer = Customer::factory()->create();
        $product = Product::factory()->create();
        $purchase = Purchase::factory()->create([
            'quantity' => 10,
            'qty_type' => 'TON'
        ]);
        
        $sale = Sale::factory()->create([
            'purchase_id' => $purchase->id,
            'customer_id' => $customer->id,
            'product_id' => $product->id,
            'quantity' => 8,
            'transport_cost' => 0
        ]);

        $this->actingAs($user);

        $component = \Livewire\Livewire::test('edit-purchase', ['purchaseId' => $purchase->id])
            ->call('openTransportCostModal', $sale->id)
            ->set('transportCostType', 'total')
            ->set('transportCostTotal', 150.00);
        
        $calculatedCost = $component->call('calculateSaleTransportCost');
        
        $this->assertEquals(150.00, $calculatedCost);
    }

    public function test_save_transport_cost_per_unit()
    {
        $user = User::factory()->create();
        $customer = Customer::factory()->create();
        $product = Product::factory()->create();
        $purchase = Purchase::factory()->create([
            'quantity' => 10,
            'qty_type' => 'TON'
        ]);
        
        $sale = Sale::factory()->create([
            'purchase_id' => $purchase->id,
            'customer_id' => $customer->id,
            'product_id' => $product->id,
            'quantity' => 4,
            'transport_cost' => 0
        ]);

        $this->actingAs($user);

        \Livewire\Livewire::test('edit-purchase', ['purchaseId' => $purchase->id])
            ->call('openTransportCostModal', $sale->id)
            ->set('transportCostType', 'per_unit')
            ->set('transportCostPerUnit', 12.75)
            ->call('saveTransportCost');

        $sale->refresh();
        
        $this->assertEquals(51.00, $sale->transport_cost); // 4 * 12.75
    }

    public function test_save_transport_cost_total()
    {
        $user = User::factory()->create();
        $customer = Customer::factory()->create();
        $product = Product::factory()->create();
        $purchase = Purchase::factory()->create([
            'quantity' => 10,
            'qty_type' => 'TON'
        ]);
        
        $sale = Sale::factory()->create([
            'purchase_id' => $purchase->id,
            'customer_id' => $customer->id,
            'product_id' => $product->id,
            'quantity' => 6,
            'transport_cost' => 0
        ]);

        $this->actingAs($user);

        \Livewire\Livewire::test('edit-purchase', ['purchaseId' => $purchase->id])
            ->call('openTransportCostModal', $sale->id)
            ->set('transportCostType', 'total')
            ->set('transportCostTotal', 200.00)
            ->call('saveTransportCost');

        $sale->refresh();
        
        $this->assertEquals(200.00, $sale->transport_cost);
    }

    public function test_remove_transport_cost()
    {
        $user = User::factory()->create();
        $customer = Customer::factory()->create();
        $product = Product::factory()->create();
        $purchase = Purchase::factory()->create([
            'quantity' => 10,
            'qty_type' => 'TON'
        ]);
        
        $sale = Sale::factory()->create([
            'purchase_id' => $purchase->id,
            'customer_id' => $customer->id,
            'product_id' => $product->id,
            'quantity' => 3,
            'transport_cost' => 75.00
        ]);

        $this->actingAs($user);

        \Livewire\Livewire::test('edit-purchase', ['purchaseId' => $purchase->id])
            ->call('openTransportCostModal', $sale->id)
            ->call('removeTransportCost');

        $sale->refresh();
        
        $this->assertEquals(0, $sale->transport_cost);
    }
}
