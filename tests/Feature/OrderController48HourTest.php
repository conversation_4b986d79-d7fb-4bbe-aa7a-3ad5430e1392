<?php

namespace Tests\Feature;

use App\Models\Purchase;
use App\Models\Sale;
use App\Models\User;
use App\Models\Customer;
use App\Models\Product;
use App\Models\Supplier;
use App\Models\ShippingAddress;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class OrderController48HourTest extends TestCase
{
    use RefreshDatabase;

    public function test_get_order_only_returns_orders_completed_48_hours_ago()
    {
        $user = User::factory()->create();
        $customer = Customer::factory()->create(['exact_id' => 'CUST001']);
        $supplier = Supplier::factory()->create();
        $product = Product::factory()->create(['sku' => 'TEST-SKU']);
        $shippingAddress = ShippingAddress::factory()->create([
            'customer_id' => $customer->id,
            'company_name' => 'Test Company',
            'name' => 'Test Contact',
            'street' => 'Test Street',
            'housenumber' => '123',
            'postal_code' => '1234AB',
            'city' => 'Test City',
            'country' => 'NL'
        ]);

        // Create a purchase completed less than 48 hours ago (should NOT be returned)
        $recentPurchase = Purchase::factory()->create([
            'supplier_id' => $supplier->id,
            'product_id' => $product->id,
            'user_id' => $user->id,
            'completed' => true,
            'sale_exact' => false,
            'administration' => 30,
            'purchase_number' => 'P001',
            'delivery_date' => now()->addDay(),
            'updated_at' => now()->subHours(24) // Only 24 hours ago
        ]);

        Sale::factory()->create([
            'purchase_id' => $recentPurchase->id,
            'customer_id' => $customer->id,
            'product_id' => $product->id,
            'shipping_address_id' => $shippingAddress->id,
            'quantity' => 10,
            'price' => 50.00,
            'total_price' => 500.00,
            'transport_cost' => 25.00
        ]);

        // Create a purchase completed more than 48 hours ago (should be returned)
        $oldPurchase = Purchase::factory()->create([
            'supplier_id' => $supplier->id,
            'product_id' => $product->id,
            'user_id' => $user->id,
            'completed' => true,
            'sale_exact' => false,
            'administration' => 30,
            'purchase_number' => 'P002',
            'delivery_date' => now()->addDay(),
            'updated_at' => now()->subHours(72) // 72 hours ago
        ]);

        Sale::factory()->create([
            'purchase_id' => $oldPurchase->id,
            'customer_id' => $customer->id,
            'product_id' => $product->id,
            'shipping_address_id' => $shippingAddress->id,
            'quantity' => 15,
            'price' => 60.00,
            'total_price' => 900.00,
            'transport_cost' => 30.00
        ]);

        // Call the API endpoint
        $response = $this->get('/api/getOrder');

        $response->assertStatus(200);
        $responseData = $response->json();

        // Should return the old purchase (P002), not the recent one (P001)
        $this->assertCount(1, $responseData);
        $this->assertEquals('P002', $responseData[0]['order_number_buyer']);
        $this->assertEquals($oldPurchase->id, $responseData[0]['order_id']);
    }

    public function test_get_purchase_order_only_returns_orders_completed_48_hours_ago()
    {
        $user = User::factory()->create();
        $supplier = Supplier::factory()->create(['exact_id' => 'SUPP001']);
        $product = Product::factory()->create(['sku' => 'TEST-SKU']);

        // Create a purchase completed less than 48 hours ago (should NOT be returned)
        $recentPurchase = Purchase::factory()->create([
            'supplier_id' => $supplier->id,
            'product_id' => $product->id,
            'user_id' => $user->id,
            'completed' => true,
            'exact' => false,
            'administration' => 30,
            'purchase_number' => 'P001',
            'load_date' => now()->addDay(),
            'quantity' => 20,
            'price' => 45.00,
            'updated_at' => now()->subHours(36) // Only 36 hours ago
        ]);

        // Create a purchase completed more than 48 hours ago (should be returned)
        $oldPurchase = Purchase::factory()->create([
            'supplier_id' => $supplier->id,
            'product_id' => $product->id,
            'user_id' => $user->id,
            'completed' => true,
            'exact' => false,
            'administration' => 30,
            'purchase_number' => 'P002',
            'load_date' => now()->addDay(),
            'quantity' => 25,
            'price' => 55.00,
            'updated_at' => now()->subHours(60) // 60 hours ago
        ]);

        // Call the API endpoint
        $response = $this->get('/api/getPurchaseOrder');

        $response->assertStatus(200);
        $responseData = $response->json();

        // Should return the old purchase (P002), not the recent one (P001)
        $this->assertCount(1, $responseData);
        $this->assertEquals('P002', $responseData[0]['order_number_buyer']);
        $this->assertEquals($oldPurchase->id, $responseData[0]['order_id']);
    }

    public function test_no_orders_returned_when_none_meet_48_hour_criteria()
    {
        $user = User::factory()->create();
        $supplier = Supplier::factory()->create();
        $product = Product::factory()->create();

        // Create only recent purchases (less than 48 hours)
        Purchase::factory()->create([
            'supplier_id' => $supplier->id,
            'product_id' => $product->id,
            'user_id' => $user->id,
            'completed' => true,
            'exact' => false,
            'updated_at' => now()->subHours(12)
        ]);

        Purchase::factory()->create([
            'supplier_id' => $supplier->id,
            'product_id' => $product->id,
            'user_id' => $user->id,
            'completed' => true,
            'exact' => false,
            'updated_at' => now()->subHours(24)
        ]);

        // Both endpoints should return success with no data
        $orderResponse = $this->get('/api/getOrder');
        $orderResponse->assertStatus(200);
        $orderResponse->assertJson(['success' => 'success']);

        $purchaseOrderResponse = $this->get('/api/getPurchaseOrder');
        $purchaseOrderResponse->assertStatus(200);
        $purchaseOrderResponse->assertJson(['success' => 'success']);
    }
}
