<?php

namespace Tests\Feature;

use App\Models\Purchase;
use App\Models\Product;
use App\Models\Supplier;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PurchaseLoggingTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $supplier;
    protected $product;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create(['name' => 'Test User']);
        $this->supplier = Supplier::factory()->create(['company_name' => 'Test Supplier']);
        $this->product = Product::factory()->create(['name' => 'Test Product']);
        
        $this->actingAs($this->user);
    }

    public function test_purchase_creation_is_logged()
    {
        $purchase = Purchase::create([
            'supplier_id' => $this->supplier->id,
            'product_id' => $this->product->id,
            'user_id' => $this->user->id,
            'qty_type' => 'TON',
            'quantity' => 10.5,
            'price' => 25.50,
            'total' => 267.75,
            'load_date' => '2025-01-15',
        ]);

        $this->assertNotNull($purchase->log);
        $this->assertStringContainsString('Inkoop aangemaakt', $purchase->log);
        $this->assertStringContainsString('Test User', $purchase->log);
        $this->assertStringContainsString('Test Supplier', $purchase->log);
        $this->assertStringContainsString('Test Product', $purchase->log);
    }

    public function test_purchase_quantity_change_is_logged()
    {
        $purchase = Purchase::create([
            'supplier_id' => $this->supplier->id,
            'product_id' => $this->product->id,
            'user_id' => $this->user->id,
            'qty_type' => 'TON',
            'quantity' => 10.5,
            'price' => 25.50,
            'total' => 267.75,
            'load_date' => '2025-01-15',
        ]);

        $originalLog = $purchase->log;

        $purchase->update(['quantity' => 15.0]);

        $this->assertNotEquals($originalLog, $purchase->fresh()->log);
        $this->assertStringContainsString('Hoeveelheid gewijzigd', $purchase->fresh()->log);
        $this->assertStringContainsString('10.5', $purchase->fresh()->log);
        $this->assertStringContainsString('15', $purchase->fresh()->log);
    }

    public function test_purchase_date_change_is_logged()
    {
        $purchase = Purchase::create([
            'supplier_id' => $this->supplier->id,
            'product_id' => $this->product->id,
            'user_id' => $this->user->id,
            'qty_type' => 'TON',
            'quantity' => 10.5,
            'price' => 25.50,
            'total' => 267.75,
            'load_date' => '2025-01-15',
        ]);

        $originalLog = $purchase->log;

        $purchase->update(['load_date' => '2025-01-20']);

        $this->assertNotEquals($originalLog, $purchase->fresh()->log);
        $this->assertStringContainsString('Laaddatum gewijzigd', $purchase->fresh()->log);
        $this->assertStringContainsString('15-01-2025', $purchase->fresh()->log);
        $this->assertStringContainsString('20-01-2025', $purchase->fresh()->log);
    }

    public function test_purchase_price_change_is_logged()
    {
        $purchase = Purchase::create([
            'supplier_id' => $this->supplier->id,
            'product_id' => $this->product->id,
            'user_id' => $this->user->id,
            'qty_type' => 'TON',
            'quantity' => 10.5,
            'price' => 25.50,
            'total' => 267.75,
            'load_date' => '2025-01-15',
        ]);

        $originalLog = $purchase->log;

        $purchase->update(['price' => 30.00]);

        $this->assertNotEquals($originalLog, $purchase->fresh()->log);
        $this->assertStringContainsString('Prijs gewijzigd', $purchase->fresh()->log);
        $this->assertStringContainsString('€25,50', $purchase->fresh()->log);
        $this->assertStringContainsString('€30,00', $purchase->fresh()->log);
    }

    public function test_purchase_weight_change_is_logged()
    {
        $purchase = Purchase::create([
            'supplier_id' => $this->supplier->id,
            'product_id' => $this->product->id,
            'user_id' => $this->user->id,
            'qty_type' => 'TON',
            'quantity' => 10.5,
            'price' => 25.50,
            'total' => 267.75,
            'load_date' => '2025-01-15',
        ]);

        $originalLog = $purchase->log;

        $purchase->update(['load_weight' => 10500.50]);

        $this->assertNotEquals($originalLog, $purchase->fresh()->log);
        $this->assertStringContainsString('Laadgewicht gewijzigd', $purchase->fresh()->log);
        $this->assertStringContainsString('10.500,50 kg', $purchase->fresh()->log);
    }

    public function test_purchase_status_change_is_logged()
    {
        $purchase = Purchase::create([
            'supplier_id' => $this->supplier->id,
            'product_id' => $this->product->id,
            'user_id' => $this->user->id,
            'qty_type' => 'TON',
            'quantity' => 10.5,
            'price' => 25.50,
            'total' => 267.75,
            'load_date' => '2025-01-15',
            'ready_for_planning' => false,
        ]);

        $originalLog = $purchase->log;

        $purchase->update(['ready_for_planning' => true]);

        $this->assertNotEquals($originalLog, $purchase->fresh()->log);
        $this->assertStringContainsString('Klaar voor planning gewijzigd', $purchase->fresh()->log);
        $this->assertStringContainsString('nee', $purchase->fresh()->log);
        $this->assertStringContainsString('ja', $purchase->fresh()->log);
    }

    public function test_purchase_deletion_is_logged()
    {
        $purchase = Purchase::create([
            'supplier_id' => $this->supplier->id,
            'product_id' => $this->product->id,
            'user_id' => $this->user->id,
            'qty_type' => 'TON',
            'quantity' => 10.5,
            'price' => 25.50,
            'total' => 267.75,
            'load_date' => '2025-01-15',
        ]);

        $purchaseNumber = $purchase->purchase_number;
        $originalLog = $purchase->log;

        $purchase->delete();

        // Since the purchase is deleted, we can't check the log on the model
        // But we can verify the observer was called by checking if the log was updated before deletion
        $this->assertNotNull($originalLog);
    }
}
