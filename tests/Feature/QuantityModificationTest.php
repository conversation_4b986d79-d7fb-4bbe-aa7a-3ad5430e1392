<?php

namespace Tests\Feature;

use App\Models\Purchase;
use App\Models\Sale;
use App\Models\User;
use App\Models\Customer;
use App\Models\Product;
use App\Models\Supplier;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class QuantityModificationTest extends TestCase
{
    use RefreshDatabase;

    public function test_purchase_quantity_can_be_updated()
    {
        $user = User::factory()->create();
        $supplier = Supplier::factory()->create();
        $product = Product::factory()->create();
        
        $purchase = Purchase::factory()->create([
            'supplier_id' => $supplier->id,
            'product_id' => $product->id,
            'quantity' => 10,
            'price' => 50.00,
            'total' => 500.00,
            'qty_type' => 'TON'
        ]);

        $this->actingAs($user);

        $component = \Livewire\Livewire::test('edit-purchase', ['purchaseId' => $purchase->id])
            ->set('purchaseQuantity', 15)
            ->call('updatedPurchaseQuantity');

        $purchase->refresh();
        
        $this->assertEquals(15, $purchase->quantity);
        $this->assertEquals(750.00, $purchase->total); // 15 * 50
        $this->assertStringContains('Hoeveelheid gewijzigd van 10 naar 15', $purchase->log);
    }

    public function test_sale_quantity_can_be_updated()
    {
        $user = User::factory()->create();
        $customer = Customer::factory()->create();
        $supplier = Supplier::factory()->create();
        $product = Product::factory()->create();
        
        $purchase = Purchase::factory()->create([
            'supplier_id' => $supplier->id,
            'product_id' => $product->id,
            'quantity' => 20,
            'qty_type' => 'TON'
        ]);
        
        $sale = Sale::factory()->create([
            'purchase_id' => $purchase->id,
            'customer_id' => $customer->id,
            'product_id' => $product->id,
            'quantity' => 8,
            'price' => 60.00,
            'total_price' => 480.00
        ]);

        $this->actingAs($user);

        $component = \Livewire\Livewire::test('edit-purchase', ['purchaseId' => $purchase->id])
            ->call('updateSaleQuantity', $sale->id, 12);

        $sale->refresh();
        
        $this->assertEquals(12, $sale->quantity);
        $this->assertEquals(720.00, $sale->total_price); // 12 * 60
    }

    public function test_purchase_quantity_validation()
    {
        $user = User::factory()->create();
        $supplier = Supplier::factory()->create();
        $product = Product::factory()->create();
        
        $purchase = Purchase::factory()->create([
            'supplier_id' => $supplier->id,
            'product_id' => $product->id,
            'quantity' => 10,
            'qty_type' => 'TON'
        ]);

        $this->actingAs($user);

        $component = \Livewire\Livewire::test('edit-purchase', ['purchaseId' => $purchase->id])
            ->set('purchaseQuantity', -5)
            ->call('updatedPurchaseQuantity');

        $component->assertHasErrors(['purchaseQuantity']);
    }

    public function test_sale_quantity_validation()
    {
        $user = User::factory()->create();
        $customer = Customer::factory()->create();
        $supplier = Supplier::factory()->create();
        $product = Product::factory()->create();
        
        $purchase = Purchase::factory()->create([
            'supplier_id' => $supplier->id,
            'product_id' => $product->id,
            'quantity' => 20,
            'qty_type' => 'TON'
        ]);
        
        $sale = Sale::factory()->create([
            'purchase_id' => $purchase->id,
            'customer_id' => $customer->id,
            'product_id' => $product->id,
            'quantity' => 8,
            'price' => 60.00
        ]);

        $this->actingAs($user);

        $component = \Livewire\Livewire::test('edit-purchase', ['purchaseId' => $purchase->id])
            ->set("saleQuantities.{$sale->id}", 0)
            ->call('updateSaleQuantity', $sale->id, 0);

        $component->assertHasErrors(["saleQuantities.{$sale->id}"]);
    }

    public function test_quantity_changes_are_logged()
    {
        $user = User::factory()->create(['name' => 'Test User']);
        $supplier = Supplier::factory()->create();
        $product = Product::factory()->create();
        
        $purchase = Purchase::factory()->create([
            'supplier_id' => $supplier->id,
            'product_id' => $product->id,
            'quantity' => 10,
            'qty_type' => 'TON',
            'log' => null
        ]);

        $this->actingAs($user);

        \Livewire\Livewire::test('edit-purchase', ['purchaseId' => $purchase->id])
            ->set('purchaseQuantity', 15)
            ->call('updatedPurchaseQuantity');

        $purchase->refresh();
        
        $this->assertNotNull($purchase->log);
        $this->assertStringContains('Test User', $purchase->log);
        $this->assertStringContains('Hoeveelheid gewijzigd van 10 naar 15', $purchase->log);
    }

    public function test_transport_cost_recalculates_when_quantity_changes()
    {
        $user = User::factory()->create();
        $customer = Customer::factory()->create();
        $supplier = Supplier::factory()->create();
        $product = Product::factory()->create();

        $purchase = Purchase::factory()->create([
            'supplier_id' => $supplier->id,
            'product_id' => $product->id,
            'quantity' => 20,
            'qty_type' => 'TON'
        ]);

        // Create a sale with transport cost that was clearly set per unit (5.00 per TON)
        $sale = Sale::factory()->create([
            'purchase_id' => $purchase->id,
            'customer_id' => $customer->id,
            'product_id' => $product->id,
            'quantity' => 8,
            'price' => 60.00,
            'transport_cost' => 40.00 // 8 * 5.00 = 40.00
        ]);

        $this->actingAs($user);

        $component = \Livewire\Livewire::test('edit-purchase', ['purchaseId' => $purchase->id])
            ->call('updateSaleQuantity', $sale->id, 10); // Change from 8 to 10

        $sale->refresh();

        // Transport cost should be recalculated: 10 * 5.00 = 50.00
        $this->assertEquals(10, $sale->quantity);
        $this->assertEquals(50.00, $sale->transport_cost);
    }

    public function test_transport_cost_not_recalculated_for_total_amount()
    {
        $user = User::factory()->create();
        $customer = Customer::factory()->create();
        $supplier = Supplier::factory()->create();
        $product = Product::factory()->create();

        $purchase = Purchase::factory()->create([
            'supplier_id' => $supplier->id,
            'product_id' => $product->id,
            'quantity' => 20,
            'qty_type' => 'TON'
        ]);

        // Create a sale with transport cost that was set as total amount (weird per-unit cost)
        $sale = Sale::factory()->create([
            'purchase_id' => $purchase->id,
            'customer_id' => $customer->id,
            'product_id' => $product->id,
            'quantity' => 8,
            'price' => 60.00,
            'transport_cost' => 150.00 // 150/8 = 18.75 per unit (unlikely to be per-unit pricing)
        ]);

        $this->actingAs($user);

        $component = \Livewire\Livewire::test('edit-purchase', ['purchaseId' => $purchase->id])
            ->call('updateSaleQuantity', $sale->id, 10); // Change from 8 to 10

        $sale->refresh();

        // Transport cost should NOT be recalculated (stays at 150.00)
        $this->assertEquals(10, $sale->quantity);
        $this->assertEquals(150.00, $sale->transport_cost);
    }
}
