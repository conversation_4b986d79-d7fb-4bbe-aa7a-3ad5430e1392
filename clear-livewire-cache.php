<?php

// This is a simple script to clear the Livewire component cache
// Run this with: php clear-livewire-cache.php

echo "Clearing Livewire component cache...\n";

// Clear the view cache
if (file_exists('bootstrap/cache/livewire-components.php')) {
    unlink('bootstrap/cache/livewire-components.php');
    echo "Removed livewire-components.php\n";
}

// Clear the config cache
if (file_exists('bootstrap/cache/config.php')) {
    unlink('bootstrap/cache/config.php');
    echo "Removed config.php\n";
}

echo "Done!\n";
echo "Now run: php artisan config:clear\n";
echo "And: php artisan view:clear\n";
