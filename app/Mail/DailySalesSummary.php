<?php

namespace App\Mail;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Carbon\Carbon;

class DailySalesSummary extends Mailable
{
    use Queueable, SerializesModels;

    public $user;
    public $completedPurchases;
    public $summaryDate;
    public $totalSales;
    public $totalRevenue;
    public $totalTransportCosts;

    /**
     * Create a new message instance.
     */
    public function __construct(User $user, $completedPurchases, Carbon $summaryDate)
    {
        $this->user = $user;
        $this->completedPurchases = $completedPurchases;
        $this->summaryDate = $summaryDate;
        
        // Calculate summary statistics
        $this->calculateSummaryStats();
    }

    /**
     * Calculate summary statistics for the email
     */
    private function calculateSummaryStats()
    {
        $this->totalSales = 0;
        $this->totalRevenue = 0;
        $this->totalTransportCosts = 0;

        foreach ($this->completedPurchases as $purchase) {
            foreach ($purchase->userSales as $sale) {
                $this->totalSales++;
                $this->totalRevenue += $sale->total_price;
                $this->totalTransportCosts += $sale->transport_cost;
            }
        }
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Daily Sales Summary - ' . $this->summaryDate->format('d-m-Y'),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.daily-sales-summary',
            with: [
                'user' => $this->user,
                'completedPurchases' => $this->completedPurchases,
                'summaryDate' => $this->summaryDate,
                'totalSales' => $this->totalSales,
                'totalRevenue' => $this->totalRevenue,
                'totalTransportCosts' => $this->totalTransportCosts,
                'hasData' => $this->totalSales > 0
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
