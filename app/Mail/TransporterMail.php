<?php

namespace App\Mail;

use App\Models\Purchase;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class TransporterMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * The purchase instance.
     *
     * @var \App\Models\Purchase
     */
    protected $purchase;

    /**
     * The message text.
     *
     * @var string
     */
    protected $messageText;

    /**
     * The attachments for the email.
     *
     * @var array
     */
    protected $attachmentFiles;

    /**
     * Create a new message instance.
     */
    public function __construct(Purchase $purchase, string $messageText, array $attachmentFiles = [])
    {
        $this->purchase = $purchase;
        $this->messageText = $messageText;
        $this->attachmentFiles = $attachmentFiles;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        // Log data for debugging
        \Illuminate\Support\Facades\Log::info('Building email with message text length: ' . strlen($this->messageText));
        \Illuminate\Support\Facades\Log::info('Purchase ID: ' . $this->purchase->id);

        // Generate plain text content
        $plainText = $this->generatePlainTextContent();

        $mail = $this->subject('Transport Informatie - Rit #' . $this->purchase->purchase_number)
            ->view('emails.transporter')
            ->text('emails.transporter-plain')
            ->with([
                'purchase' => $this->purchase,
                'messageText' => $this->messageText,
            ]);

        // Add Mandrill-specific tags for tracking in production
        if (app()->environment('production')) {
            $mail->withSymfonyMessage(function ($message) {
                $message->getHeaders()
                    ->addTextHeader('X-MC-Tags', 'transporter,purchase-' . $this->purchase->id)
                    ->addTextHeader('X-MC-Metadata', json_encode([
                        'purchase_id' => $this->purchase->id,
                        'purchase_number' => $this->purchase->purchase_number,
                    ]));
            });
        }

        // Add attachments
        if (!empty($this->attachmentFiles)) {
            foreach ($this->attachmentFiles as $file) {
                if (!isset($file['path']) || !isset($file['name'])) {
                    continue;
                }

                try {
                    if (app()->environment('production')) {
                        $disk = 's3';
                    } else {
                        $disk = 'public';
                    }

                    // Handle different file paths based on the file type and environment
                    $filePath = $file['path'];

                    // Check if this is a CMR file
                    $isCmr = (strtolower($file['name']) === 'cmr.pdf');

                    if (app()->environment('production')) {
                        // For production (Vapor/S3), we need to get the file from S3
                        if ($isCmr) {
                            // For CMR files, we can use the PDF content directly
                            $mail->attachData(
                                Storage::disk('s3')->get($filePath),
                                $file['name'],
                                ['mime' => $file['mime'] ?? 'application/pdf']
                            );
                        } else {
                            // For other files, attach from S3
                            $mail->attachFromStorageDisk('s3', $filePath, $file['name'], [
                                'mime' => $file['mime'] ?? 'application/octet-stream',
                            ]);
                        }
                    } else {
                        // For local development
                        $mail->attachFromStorageDisk('public', $filePath, $file['name'], [
                            'mime' => $file['mime'] ?? 'application/octet-stream',
                        ]);
                    }
                } catch (\Exception $e) {
                    \Illuminate\Support\Facades\Log::error('Error attaching file: ' . $e->getMessage());
                }
            }
        }

        return $mail;
    }

    /**
     * Generate plain text content for the email
     *
     * @return string
     */
    protected function generatePlainTextContent(): string
    {
        $text = "Transport Informatie - Rit #{$this->purchase->purchase_number}\n\n";
        $text .= "{$this->messageText}\n\n";
        $text .= "Rit Details:\n";
        $text .= "Rit Nummer: {$this->purchase->purchase_number}\n";
        $text .= "Product: {$this->purchase->product->name}\n";
        $text .= "Hoeveelheid: {$this->purchase->quantity} {$this->purchase->qty_type}\n";
        $text .= "Laaddatum: " . Carbon::parse($this->purchase->load_date)->format('d-m-Y') . "\n";

        if ($this->purchase->delivery_date) {
            $text .= "Losdatum: " . Carbon::parse($this->purchase->delivery_date)->format('d-m-Y') . "\n";
        }

        $text .= "\nLaadadres:\n";
        $text .= "{$this->purchase->supplier->company_name}\n";

        if ($this->purchase->custom_supplier_address) {
            $text .= "{$this->purchase->supplier_street} {$this->purchase->supplier_housenumber}\n";
            $text .= "{$this->purchase->supplier_postal_code} {$this->purchase->supplier_city}\n";
            $text .= "{$this->purchase->supplier_country}\n";
        } else {
            $text .= "{$this->purchase->supplier->street_name} {$this->purchase->supplier->housenumber}\n";
            $text .= "{$this->purchase->supplier->postal_code} {$this->purchase->supplier->city}\n";
            $text .= "{$this->purchase->supplier->country}\n";
        }

        if (count($this->purchase->sales) > 0) {
            $text .= "\nLosadres(sen):\n";
            foreach ($this->purchase->sales as $sale) {
                $text .= "{$sale->customer->company_name}\n";
                $text .= "{$sale->address->street} {$sale->address->housenumber}\n";
                $text .= "{$sale->address->postal_code} {$sale->address->city}\n";
                $text .= "{$sale->address->country}\n\n";
            }
        }

        return $text;
    }
}
