<?php

namespace App\Livewire;

use App\Models\Customer;
use App\Models\Prospect;
use App\Models\Supplier;
use App\Models\User;
use Carbon\Carbon;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class AddPlanTask extends Component
{
    public $showModal = false;
    public $users;
    public $dueDate;
    public $description;
    public $assignedTo;
    public $search;
    public $results = [];
    public $selected;
    public $showTo;
    public $errorMessage;

    public function mount()
    {
        $this->users = User::all();
        $this->showTo = Carbon::now()->endOfWeek()->format('Y-m-d');

    }

    public function updatedShowTo()
    {
        $this->dispatch('showto', $this->showTo);
    }

    public function updatedSearch()
    {
        $this->results = array_merge(
            Customer::where('company_name', 'like', '%' . $this->search . '%')->get()->map(function($customer) {
                $customerArray = $customer->toArray();
                $customerArray['classname'] = 'Customer';  // append classname
                return $customerArray;
            })->all(),
            Supplier::where('company_name', 'like', '%' . $this->search . '%')->get()->map(function($supplier) {
                $supplierArray = $supplier->toArray();
                $supplierArray['classname'] = 'Supplier';  // append classname
                return $supplierArray;
            })->all(),
            Prospect::where('company_name', 'like', '%' . $this->search . '%')->get()->map(function($prospect) {
                $prospectArray = $prospect->toArray();
                $prospectArray['classname'] = 'Prospect';  // append classname
                return $prospectArray;
            })->all()
        );
    }

    public function saveTask()
    {
        $this->validate([
            'dueDate' => 'required|date',
            'description' => 'required|string',
        ]);

        $this->selected->planTasks()->create([
            'due_date' => $this->dueDate,
            'created_by_user_id' => Auth::id(),
            'description' => $this->description,
            'assigned_to_user_id' => $this->assignedTo ?? Auth::id(),
            'type' => 'call'
        ]);
        $this->dispatch('plantask_saved');
        $this->showModal = false;
    }

    public function selectEntity($entityId, $className)
    {
        $modelClass = '\\App\\Models\\' . $className;
        $this->selected = $modelClass::find($entityId);
        $this->search = $this->selected->company_name;
        $this->results = [];
    }

    public function render()
    {
        return view('livewire.add-plan-task');
    }
}
