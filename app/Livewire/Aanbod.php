<?php

namespace App\Livewire;

use App\Models\Product;
use App\Models\Purchase;
use App\Models\Supplier;
use Livewire\Component;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Collection as BaseCollection;

class Aanbod extends Component
{
    public $showModal = false;
    public $showAddProductModal = false;
    public $search = '';
    public $suppliers;
    public $selectedSupplier;
    public $selectedProduct = null;
    public $selectedProductId;
    public $productSearch = '';
    public $products;
    public $supplierProducts;
    public $purchaseQty;
    public $purchasePrice;
    public $purchaseQtyType = 'TON';
    public $loadDate;
    public $message;
    public $purchases;
    public $selectedPurchase;
    public $selectedDemand;
    public $administration;
    public $selectedPurchases = [];
    public $tempLoadDate;
    public $supplierPurchaseNumber;
    public $loadingTimes;
    public $comments;

    // Supplier address fields
    public $showAddressModal = false;
    public $showAddAddressModal = false;
    public $selectedAddressId = null;
    public $supplierAddresses = [];
    public $newAddressName;
    public $newAddressStreet;
    public $newAddressHousenumber;
    public $newAddressPostalCode;
    public $newAddressCity;
    public $newAddressCountry = 'NL';
    public $newAddressLoadingTimes;

    // Properties for adding a product to a supplier
    public $addProductSearch = '';
    public $searchResults = [];
    public $newSelectedProduct = null;
    public $newProductPrice;
    public $newProductUnitType = 'TON';
    public $newProductEndDate;
    public $newProductDefaultQty;

    protected $listeners = [
        'demand-selected' => 'handleDemandSelected',
        'show' => 'show',
        'refresh-aanbod' => 'refreshPurchases'
    ];

    public function show($until, $showDate, $administration)
    {
        $query = Purchase::with('product', 'supplier')
            ->where('ready_for_planning', 0)
            ->where('completed', false);

        if ($until) {
            $query->where('load_date', '<=', $showDate);
        } else {
            $query->where('load_date', $showDate);
        }

        if ($administration) {
            $query->where('administration', $administration);
        }

        $this->purchases = $query->orderBy('load_date', 'asc')->get();
    }

    public function mount()
    {
        $this->suppliers = new Collection();
        $this->products = new Collection();
        $this->purchases = new Collection(); // Initialize with empty collection, will be populated by 'show' event

        // Load initial data with default settings (current date, until=true)
        $this->loadInitialData();
    }

    public function loadInitialData()
    {
        // Use the same default settings as the Toolbar component
        $showDate = now()->format('Y-m-d');
        $until = true;
        $administration = null;

        $this->show($until, $showDate, $administration);
    }

    public function updatedSearch()
    {
        $this->suppliers = Supplier::where('company_name', 'like', '%'.$this->search.'%')->get();
    }

    public function updatedProductSearch()
    {
        // This method is no longer needed as we're using a dropdown
        // But we'll keep it for backward compatibility
        $this->products = Product::where('name', 'like', '%'.$this->productSearch.'%')->get();
    }

    public function selectSupplier(Supplier $supplier)
    {
        $this->selectedSupplier = $supplier;
        $this->search = '';
        $this->suppliers = new Collection();

        // Load supplier loading times
        $this->loadingTimes = $supplier->loading_times;

        // Load supplier addresses
        $this->loadSupplierAddresses();

        // Load products with unit prices for this supplier
        $this->loadSupplierProducts();
    }

    /**
     * Load addresses for the selected supplier
     */
    public function loadSupplierAddresses()
    {
        if (!$this->selectedSupplier) {
            $this->supplierAddresses = [];
            return;
        }

        // Get addresses for this supplier
        $this->supplierAddresses = $this->selectedSupplier->addresses()->get();

        // If no addresses are found, create a default one from the supplier's main address
        if ($this->supplierAddresses->isEmpty()) {
            $defaultAddress = \App\Models\SupplierAddress::create([
                'supplier_id' => $this->selectedSupplier->id,
                'name' => 'Hoofdadres',
                'street' => $this->selectedSupplier->street_name,
                'housenumber' => $this->selectedSupplier->housenumber,
                'postal_code' => $this->selectedSupplier->postal_code,
                'city' => $this->selectedSupplier->city,
                'country' => $this->selectedSupplier->country ?: 'NL',
                'loading_times' => $this->selectedSupplier->loading_times,
                'is_default' => true
            ]);

            $this->supplierAddresses = collect([$defaultAddress]);
            $this->selectedAddressId = $defaultAddress->id;
        } else {
            // Select the default address if available, otherwise select the first one
            $defaultAddress = $this->supplierAddresses->firstWhere('is_default', true);
            $this->selectedAddressId = $defaultAddress ? $defaultAddress->id : $this->supplierAddresses->first()->id;

            // Set loading times from the selected address
            $selectedAddress = $this->supplierAddresses->firstWhere('id', $this->selectedAddressId);
            if ($selectedAddress) {
                $this->loadingTimes = $selectedAddress->loading_times;
            }
        }
    }

    /**
     * Load products with unit prices for the selected supplier
     */
    public function loadSupplierProducts()
    {
        if (!$this->selectedSupplier) {
            $this->supplierProducts = new BaseCollection();
            return;
        }

        // Get only currently active price agreements for this supplier (non-expired agreements)
        // Only shows products with valid, current pricing - excludes expired agreements
        // With versioning, there should be only one active agreement per product
        $productsWithPivot = $this->selectedSupplier->products()
            ->withPivot('unit_price', 'unit_type', 'end_date', 'default_qty', 'id', 'created_at')
            ->withTimestamps()
            ->where(function($query) {
                $query->whereNull('supplier_products.end_date')
                      ->orWhere('supplier_products.end_date', '>', now()->toDateString());
            })
            ->orderBy('supplier_products.created_at', 'desc')
            ->get();

        // Convert to array format that preserves pivot data
        $this->supplierProducts = $productsWithPivot->map(function($product) {
            return [
                'id' => $product->id,
                'name' => $product->name,
                'sku' => $product->sku,
                'pivot' => [
                    'id' => $product->pivot->id ?? null,
                    'unit_price' => $product->pivot->unit_price ?? null,
                    'unit_type' => $product->pivot->unit_type ?? 'TON',
                    'end_date' => $product->pivot->end_date ?? null,
                    'default_qty' => $product->pivot->default_qty ?? null,
                    'created_at' => $product->pivot->created_at ?? null,
                ]
            ];
        })->values();

        // If no products with prices are found, set a message
        if (empty($this->supplierProducts)) {
            $this->message = 'Er zijn geen producten met prijzen gevonden voor deze leverancier.';
        } else {
            $this->message = '';
        }
    }

    public function selectProduct($index)
    {
        // Convert to integer if it's a string from the wire:change event
        if (is_string($index)) {
            $index = (int) $index;
        }

        // Reset values first
        $this->purchasePrice = null;
        $this->purchaseQty = null;
        $this->purchaseQtyType = 'TON';

        // Get the specific price agreement by index from the supplierProducts collection
        if (!isset($this->supplierProducts[$index])) {
            return;
        }

        $selectedAgreement = $this->supplierProducts[$index];

        // Now selectedAgreement is an array, not an Eloquent model
        $this->selectedProductId = $selectedAgreement['id'];
        $this->selectedProduct = (object) $selectedAgreement; // Convert to object for compatibility
        $this->productSearch = '';
        $this->products = new Collection();

        // Set the price, unit type, and default quantity automatically from the selected price agreement
        if ($selectedAgreement && isset($selectedAgreement['pivot'])) {
            $pivotData = $selectedAgreement['pivot'];

            // Set price if available
            if (isset($pivotData['unit_price']) && $pivotData['unit_price']) {
                $this->purchasePrice = (float) $pivotData['unit_price'];
            }

            // Set unit type if available
            if (isset($pivotData['unit_type']) && $pivotData['unit_type']) {
                $this->purchaseQtyType = $pivotData['unit_type'];

                // If unit type is VOLLE VRACHT or HALVE VRACHT, set quantity to 1
                if ($this->purchaseQtyType === 'VOLLE VRACHT' || $this->purchaseQtyType === 'HALVE VRACHT') {
                    $this->purchaseQty = 1;
                }
            }

            // Set default quantity if available and not already set by unit type logic
            if (isset($pivotData['default_qty']) &&
                $pivotData['default_qty'] &&
                $this->purchaseQtyType !== 'VOLLE VRACHT' &&
                $this->purchaseQtyType !== 'HALVE VRACHT') {
                $this->purchaseQty = (int) $pivotData['default_qty'];
            }
        }
    }

    public function selectPurchase(Purchase $purchase)
    {
        if ($this->selectedPurchase && $this->selectedPurchase->id === $purchase->id) {
            $this->selectedPurchase = null;

            return;
        }
        $this->selectedPurchase = $purchase;
        $this->dispatch('purchase-selected', $purchase->id);
    }

    public function handleDemandSelected($demandId)
    {
        $this->selectedDemand = $demandId;
    }

    /**
     * Handle unit type change and set quantity to 1 for special types
     */
    public function updatedPurchaseQtyType()
    {
        if ($this->purchaseQtyType === 'VOLLE VRACHT' || $this->purchaseQtyType === 'HALVE VRACHT') {
            $this->purchaseQty = 1;
        }
    }

    public function savePurchase()
    {
        [$administrationNumber, $administrationName] = explode('-', $this->administration);

        // Ensure quantity is 1 for VOLLE VRACHT or HALVE VRACHT
        if ($this->purchaseQtyType === 'VOLLE VRACHT' || $this->purchaseQtyType === 'HALVE VRACHT') {
            $this->purchaseQty = 1;
        }

        // Get the selected address
        $selectedAddress = null;
        if ($this->selectedAddressId) {
            $selectedAddress = \App\Models\SupplierAddress::find($this->selectedAddressId);
        }

        // Update the loading times for the selected address
        if ($selectedAddress && $selectedAddress->loading_times !== $this->loadingTimes) {
            $selectedAddress->update([
                'loading_times' => $this->loadingTimes
            ]);
        }

        // Create the purchase with custom address if an address is selected
        $purchaseData = [
            'supplier_id' => $this->selectedSupplier->id,
            'product_id' => $this->selectedProduct->id,
            'user_id' => auth()->id(),
            'qty_type' => $this->purchaseQtyType,
            'quantity' => $this->purchaseQty,
            'price' => $this->purchasePrice,
            'total' => $this->purchaseQty * $this->purchasePrice,
            'load_date' => $this->loadDate,
            'administration' => $administrationNumber,
            'admin_name' => $administrationName,
            'supplier_purchase_number' => $this->supplierPurchaseNumber,
            'comments' => $this->comments
        ];

        // Add custom address data if an address is selected
        if ($selectedAddress) {
            $purchaseData['custom_supplier_address'] = true;
            $purchaseData['supplier_street'] = $selectedAddress->street;
            $purchaseData['supplier_housenumber'] = $selectedAddress->housenumber;
            $purchaseData['supplier_postal_code'] = $selectedAddress->postal_code;
            $purchaseData['supplier_city'] = $selectedAddress->city;
            $purchaseData['supplier_country'] = $selectedAddress->country;
            $purchaseData['supplier_latitude'] = $selectedAddress->latitude;
            $purchaseData['supplier_longitude'] = $selectedAddress->longitude;
        }

        Purchase::create($purchaseData);

        $this->showModal = false;
        $this->resetFields();

        // Refresh the purchases list
        $this->refreshPurchases();
    }

    /**
     * Select an address from the address book
     */
    public function selectAddress($addressId)
    {
        $this->selectedAddressId = $addressId;
        $selectedAddress = $this->supplierAddresses->firstWhere('id', $addressId);
        if ($selectedAddress) {
            $this->loadingTimes = $selectedAddress->loading_times;
        }
        $this->showAddressModal = false;
    }

    /**
     * Open the add address modal
     */
    public function openAddAddressModal()
    {
        $this->showAddAddressModal = true;
        $this->newAddressName = '';
        $this->newAddressStreet = '';
        $this->newAddressHousenumber = '';
        $this->newAddressPostalCode = '';
        $this->newAddressCity = '';
        $this->newAddressCountry = 'NL';
        $this->newAddressLoadingTimes = $this->loadingTimes; // Pre-fill with current loading times
    }

    /**
     * Save a new address
     */
    public function saveNewAddress()
    {
        // Validate inputs
        $this->validate([
            'newAddressName' => 'required|string|max:255',
            'newAddressStreet' => 'required|string|max:255',
            'newAddressHousenumber' => 'required|string|max:20',
            'newAddressPostalCode' => 'required|string|max:10',
            'newAddressCity' => 'required|string|max:255',
        ]);

        if (!$this->selectedSupplier) {
            return;
        }

        // Create new address
        $newAddress = \App\Models\SupplierAddress::create([
            'supplier_id' => $this->selectedSupplier->id,
            'name' => $this->newAddressName,
            'street' => $this->newAddressStreet,
            'housenumber' => $this->newAddressHousenumber,
            'postal_code' => $this->newAddressPostalCode,
            'city' => $this->newAddressCity,
            'country' => $this->newAddressCountry,
            'loading_times' => $this->newAddressLoadingTimes,
            'is_default' => false
        ]);

        // Reload supplier addresses and select the new one
        $this->loadSupplierAddresses();
        $this->selectedAddressId = $newAddress->id;
        $this->loadingTimes = $newAddress->loading_times;

        // Close modal
        $this->showAddAddressModal = false;
    }

    public function resetFields()
    {
        $this->search = '';
        $this->selectedSupplier = null;
        $this->suppliers = new Collection();
        $this->purchaseQty = NULL;
        $this->purchasePrice = NULL;
        $this->purchaseQtyType = 'TON';
        $this->message = '';
        $this->selectedProduct = null;
        $this->selectedProductId = null;
        $this->productSearch = '';
        $this->products = new Collection();
        $this->supplierProducts = new Collection();
        $this->loadDate = '';
        $this->supplierPurchaseNumber = null;
        $this->loadingTimes = null;
        $this->comments = null;
        $this->selectedAddressId = null;
        $this->supplierAddresses = [];
    }

    /**
     * Cancel the purchase creation and reset all fields
     */
    public function cancelPurchase()
    {
        $this->resetFields();
        $this->showModal = false;
    }

    public function updateSelectedLoadDates()
    {
        // Update each purchase individually to trigger the observer
        foreach ($this->selectedPurchases as $purchaseId) {
            $purchase = Purchase::find($purchaseId);
            if ($purchase) {
                $purchase->load_date = $this->tempLoadDate;
                $purchase->save();
            }
        }

        $this->selectedPurchases = [];
        $this->tempLoadDate = null;

        // Refresh the purchases
        $this->refreshPurchases();
    }

    public function deleteSelected()
    {
        Purchase::whereIn('id', $this->selectedPurchases)->delete();
        $this->selectedPurchases = [];
        $this->tempLoadDate = null;

        // Refresh the purchases
        $this->refreshPurchases();
    }

    /**
     * Refresh the purchases list
     */
    public function refreshPurchases()
    {
        $this->purchases = Purchase::with('product', 'supplier')
            ->where('ready_for_planning', 0)
            ->where('completed', false)
            ->orderBy('load_date', 'asc')
            ->get();
    }

    /**
     * Open the add product modal
     */
    public function openAddProductModal()
    {
        $this->showAddProductModal = true;
        $this->addProductSearch = '';
        $this->searchResults = [];
        $this->newSelectedProduct = null;
        $this->newProductPrice = null;
        $this->newProductUnitType = 'TON';
    }

    /**
     * Search for products when adding a new product to a supplier
     */
    public function updatedAddProductSearch()
    {
        if (strlen($this->addProductSearch) >= 2) {
            $this->searchResults = Product::where('name', 'like', '%' . $this->addProductSearch . '%')
                ->orWhere('sku', 'like', '%' . $this->addProductSearch . '%')
                ->get();
        } else {
            $this->searchResults = [];
        }
    }

    /**
     * Select a product when adding a new product to a supplier
     */
    public function selectNewProduct($productId)
    {
        $this->newSelectedProduct = Product::find($productId);
        $this->addProductSearch = $this->newSelectedProduct->name;
        $this->searchResults = [];
    }

    /**
     * Add a product to the supplier
     */
    public function addProductToSupplier()
    {
        // Validate inputs
        $this->validate([
            'newProductPrice' => 'required|numeric|min:0',
            'newProductUnitType' => 'required|string',
            'newProductEndDate' => 'nullable|date|after:today',
            'newProductDefaultQty' => 'nullable|integer|min:1',
        ]);

        if (!$this->selectedSupplier || !$this->newSelectedProduct) {
            return;
        }

        // Prepare pivot data
        $pivotData = [
            'unit_price' => $this->newProductPrice,
            'unit_type' => $this->newProductUnitType,
            'end_date' => $this->newProductEndDate,
            'default_qty' => $this->newProductDefaultQty,
        ];

        // Implement versioning: expire any existing active agreements for this product
        $existingActiveAgreements = $this->selectedSupplier->products()
            ->wherePivot('product_id', $this->newSelectedProduct->id)
            ->where(function($query) {
                $query->whereNull('supplier_products.end_date')
                      ->orWhere('supplier_products.end_date', '>', now()->toDateString());
            })
            ->get();

        // Set end_date on all existing active agreements to yesterday
        foreach ($existingActiveAgreements as $agreement) {
            $this->selectedSupplier->products()->updateExistingPivot($agreement->id, [
                'end_date' => now()->subDay()->toDateString()
            ]);
        }

        // Add the new active price agreement
        $this->selectedSupplier->products()->attach($this->newSelectedProduct->id, $pivotData);

        // Reset fields and close modal
        $this->showAddProductModal = false;
        $this->addProductSearch = '';
        $this->searchResults = [];
        $this->newSelectedProduct = null;
        $this->newProductPrice = null;
        $this->newProductUnitType = 'TON';
        $this->newProductEndDate = null;
        $this->newProductDefaultQty = null;

        // Reload supplier products
        $this->loadSupplierProducts();
    }

    public function render()
    {
        return view('livewire.aanbod');
    }
}
