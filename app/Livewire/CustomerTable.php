<?php

namespace App\Livewire;

use App\Models\Customer;
use App\Models\Prospect;
use App\Models\Supplier;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Livewire\Component;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class CustomerTable extends Component
{
    public $myCustomers;

    public function render()
    {
        return view('livewire.customer-table');
    }

    public function mount()
    {
        $this->myCustomers = session('myCustomers', false);
    }

    public function updatedMyCustomers()
    {
        session(['myCustomers' => $this->myCustomers]);
        $this->dispatch('refresh');
    }

    public function getCustomers()
    {
        $query = Customer::query()
            ->leftJoin('product_groupables', function($join) {
                $join->on('customers.id', '=', 'product_groupables.product_groupable_id')
                    ->where('product_groupables.product_groupable_type', '=', 'App\Models\Customer');
            })
            ->leftJoin('product_groups', 'product_groupables.product_group_id', '=', 'product_groups.id')
            ->select([
                'customers.*',
                DB::raw('GROUP_CONCAT(product_groups.name) as product_groups')
            ])
            ->groupBy('customers.id');

        if (session('mycustomers', false)) {
            $query->where('customers.owned_by', Auth::id());
        }

        return DataTables::of($query)
            ->filterColumn('product_groups', function($query, $keyword) {
                $query->whereRaw("product_groups.name like ?", ["%$keyword%"]);
            })
            ->addColumn('product_groups', function($row) {
                return $row->product_groups; // Your logic to return the product groups associated with each customer
            })
            ->make(true);
    }

}
