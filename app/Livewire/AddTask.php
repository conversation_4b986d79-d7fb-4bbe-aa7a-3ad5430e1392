<?php

namespace App\Livewire;

use App\Models\OwnerNotification;
use App\Models\Task;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class AddTask extends Component
{
    public $model;
    public $taskable;
    public $showModal = false;
    public $modelId;
    public $task;
    public $dueDate;
    public $description;
    public $type;
    public $types = ['telefoon', 'bezoek', 'email'];
    public $users;
    public $assignedTo;
    public $isModalOpen = false;
    public $modalTaskId;
    public $modalTask;
    public $note;
    public $dueDateEdit;
    public $ownerId;

    protected $listeners = ['saved' => '$refresh'];

    public function mount($model, $modelId)
    {
        $this->task = new Task();
        $this->model = $model;
        $this->modelId = $modelId;
        $this->users = User::all();
        $this->assignedTo = Auth::id();
    }

    public function saveTask()
    {
        $this->validate([
            'model' => 'required|string',
            'modelId' => 'required|integer',
            'type' => 'required|string',
            'dueDate' => 'required|date',
            'description' => 'required|string',
        ]);

        $modelClass = '\\App\\Models\\' . ucfirst($this->model);
        $modelInstance = $modelClass::find($this->modelId);

        if ($modelInstance) {
            $this->task->type = $this->type;
            $this->task->due_date = $this->dueDate;
            $this->task->description = $this->description;
            $this->task->created_by_user_id = Auth::id();
            $this->task->assigned_to_user_id = $this->assignedTo;

            $modelInstance->tasks()->save($this->task);
        }

        if (Auth::id() != $this->assignedTo) {
            OwnerNotification::create([
                'owner_id' => $this->assignedTo,
                'editor_id' => auth()->id(),
                'notifiable_id' => $modelInstance->id,
                'notifiable_type' => $modelInstance::class,
                'action' => 'edited',
                'details' => Auth::user()->name . ' heeft een taak voor je aangemaakt voor ' . $this->dueDate . ': ' . $this->description,
            ]);
        }

        $this->showModal = false;
        $this->dispatch('saved');
    }

    public function showModal(Task $task)
    {
        $this->isModalOpen = true;
        $this->modalTaskId = $task->id;
        $this->modalTask = $task;
        $this->note = $task->note;
        $this->dueDateEdit = $task->due_date;
        $this->ownerId = $task->assigned_to_user_id;
    }

    public function addTaskNote()
    {
        $this->modalTask->note = $this->note;
        $this->modalTask->due_date = $this->dueDateEdit;
        $this->modalTask->assigned_to_user_id = $this->ownerId;
        $this->modalTask->save();
        $this->isModalOpen = false;
        $this->dispatch('saved');
    }

    public function removeTask()
    {
        $this->modalTask->delete();
        $this->isModalOpen = false;
        $this->dispatch('saved');
    }

    public function render()
    {
        return view('livewire.add-task');
    }
}
