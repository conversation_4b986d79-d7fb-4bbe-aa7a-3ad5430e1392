<?php

namespace App\Livewire;

use App\Models\Purchase;
use App\Models\Sale;
use Illuminate\Database\Eloquent\Collection;
use Livewire\Component;

class Plannable extends Component
{
    public $plannableCargo;
    public $selectedCargo = [];
    public $tempLoadDate;
    public $tempDeliveryDate;
    public $showDeleteConfirmation = false;
    public $purchaseToDelete = null;
    public $showDetachSaleConfirmation = false;
    public $saleToDetach = null;

    protected $listeners = [
        'show' => 'show',
        'refresh-plannable' => 'refreshPlannable'
    ];

    public function mount()
    {
        $this->plannableCargo = new Collection(); // Initialize with empty collection, will be populated by 'show' event

        // Load initial data with default settings (current date, until=true)
        $this->loadInitialData();
    }

    public function loadInitialData()
    {
        // Use the same default settings as the Toolbar component
        $showDate = now()->format('Y-m-d');
        $until = true;
        $administration = null;

        $this->show($until, $showDate, $administration);
    }

    public function show($until, $showDate, $administration)
    {
        $query = Purchase::with(['product', 'supplier', 'sales.address'])
            ->where('ready_for_planning', 1);

        if ($until) {
            $query->where('load_date', '<=', $showDate);
        } else {
            $query->where('load_date', $showDate);
        }

        if ($administration) {
            $query->where('administration', $administration);
        }

        $this->plannableCargo = $query->get();
    }

    public function updateLoadDate($id, $date)
    {
        $purchase = Purchase::find($id);
        $purchase->update(['load_date' => $date]);

        $this->plannableCargo = Purchase::with(['product', 'supplier', 'sales.address'])
            ->where('ready_for_planning', 1)->get();
    }

    public function updateDeliveryDate($id, $date)
    {
        $purchase = Purchase::find($id);
        $purchase->update(['delivery_date' => $date]);

        $this->plannableCargo = Purchase::with(['product', 'supplier', 'sales.address'])
            ->where('ready_for_planning', 1)->get();
    }

    public function updatedSelectedCargo($value)
    {
        // This method will be called whenever the selectedCargo array changes
        $this->dispatch('selection-changed');
    }

    public function updateSelectedLoadDates($newDate)
    {
        Purchase::whereIn('id', $this->selectedCargo)
            ->update(['load_date' => $newDate]);

        $this->plannableCargo = Purchase::with(['product', 'supplier', 'sales.address'])
            ->where('ready_for_planning', 1)->get();
    }

    public function updateSelectedDeliveryDates($newDate)
    {
        Purchase::whereIn('id', $this->selectedCargo)
            ->update(['delivery_date' => $newDate]);

        $this->plannableCargo = Purchase::with(['product', 'supplier', 'sales.address'])
            ->where('ready_for_planning', 1)->get();
    }

    public function updateSelected()
    {
        if ($this->tempLoadDate) {
            Purchase::whereIn('id', $this->selectedCargo)
                ->update(['load_date' => $this->tempLoadDate]);
        }

        if ($this->tempDeliveryDate) {
            Purchase::whereIn('id', $this->selectedCargo)
                ->update(['delivery_date' => $this->tempDeliveryDate]);
        }

        $this->plannableCargo = Purchase::with(['product', 'supplier', 'sales.address'])
            ->where('ready_for_planning', 1)->get();

        $this->selectedCargo = [];
        $this->tempLoadDate = null;
        $this->tempDeliveryDate = null;
    }

    /**
     * Confirm deletion of a purchase
     *
     * @param int $purchaseId The ID of the purchase to delete
     * @return void
     */
    public function confirmDelete($purchaseId)
    {
        $this->purchaseToDelete = $purchaseId;
        $this->showDeleteConfirmation = true;
    }

    /**
     * Cancel the deletion confirmation
     *
     * @return void
     */
    public function cancelDelete()
    {
        $this->purchaseToDelete = null;
        $this->showDeleteConfirmation = false;
    }

    /**
     * Delete a purchase and its associated sales
     *
     * @return void
     */
    public function deletePurchase()
    {
        $purchase = Purchase::find($this->purchaseToDelete);

        if ($purchase) {
            // Delete associated sales first
            $purchase->sales()->delete();

            // Delete the purchase
            $purchase->delete();
        }

        $this->purchaseToDelete = null;
        $this->showDeleteConfirmation = false;

        // Refresh the purchases list
        $this->plannableCargo = Purchase::with(['product', 'supplier', 'sales.address'])
            ->where('ready_for_planning', 1)->get();
    }

    /**
     * Delete selected purchases and their associated sales
     *
     * @return void
     */
    public function deleteSelected()
    {
        if (empty($this->selectedCargo)) {
            return;
        }

        // Get all selected purchases
        $purchases = Purchase::whereIn('id', $this->selectedCargo)->get();

        foreach ($purchases as $purchase) {
            // Delete associated sales first
            $purchase->sales()->delete();

            // Delete the purchase
            $purchase->delete();
        }

        $this->selectedCargo = [];

        // Refresh the purchases list
        $this->plannableCargo = Purchase::with(['product', 'supplier', 'sales.address'])
            ->where('ready_for_planning', 1)->get();
    }

    /**
     * Navigate to the purchase edit page
     *
     * @param int $purchaseId The ID of the purchase to view
     * @return void
     */
    public function viewPurchase($purchaseId)
    {
        return redirect()->route('purchase.edit', $purchaseId);
    }

    /**
     * Refresh the plannable cargo list
     *
     * @return void
     */
    public function refreshPlannable()
    {
        $this->plannableCargo = Purchase::with(['product', 'supplier', 'sales.address'])
            ->where('ready_for_planning', 1)->get();
    }

    /**
     * Confirm detaching a sale from a purchase
     *
     * @param int $saleId The ID of the sale to detach
     * @return void
     */
    public function confirmDetachSale($saleId)
    {
        $this->saleToDetach = $saleId;
        $this->showDetachSaleConfirmation = true;
    }

    /**
     * Cancel the detach sale confirmation
     *
     * @return void
     */
    public function cancelDetachSale()
    {
        $this->saleToDetach = null;
        $this->showDetachSaleConfirmation = false;
    }

    /**
     * Detach a sale from a purchase and move both back to their respective components
     *
     * @return void
     */
    public function detachSale()
    {
        $sale = \App\Models\Sale::with(['purchase', 'customer', 'product', 'address'])->find($this->saleToDetach);

        if (!$sale) {
            $this->cancelDetachSale();
            return;
        }

        $purchase = $sale->purchase;

        // Create a new demand from the sale
        \App\Models\Demand::create([
            'customer_id' => $sale->customer_id,
            'product_id' => $sale->product_id,
            'user_id' => auth()->id(),
            'qty_type' => $sale->sale_qty_type,
            'quantity' => $sale->quantity,
            'price' => $sale->price,
            'delivery_date' => $purchase->delivery_date,
            'street' => $sale->address->street,
            'housenumber' => $sale->address->housenumber,
            'postal_code' => $sale->address->postal_code,
            'city' => $sale->address->city,
            'customer_purchase_number' => $sale->customer_purchase_number
        ]);

        // Delete the sale
        $sale->delete();

        // Check if this was the last sale for this purchase
        if ($purchase->sales()->count() === 0) {
            // Move the purchase back to Aanbod by setting ready_for_planning to false
            $purchase->update(['ready_for_planning' => false]);
        }

        // Reset the confirmation state
        $this->cancelDetachSale();

        // Refresh the plannable cargo list
        $this->refreshPlannable();

        // Dispatch events to refresh other components
        $this->dispatch('refresh-aanbod');
        $this->dispatch('refresh-vraag');
    }

    public function render()
    {
        return view('livewire.plannable');
    }
}
