<?php

namespace App\Livewire;

use App\Models\Customer;
use App\Models\ProductGroup;
use App\Models\Prospect;
use App\Models\Supplier;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class AddProspect extends Component
{
    public $showModal = false;
    public $name;
    public $contactPerson;
    public $telephone;
    public $street;
    public $housenumber;
    public $postalCode;
    public $city;
    public $exactId;
    public $allProductGroups;
    public $selectedProductGroups = [];
    public $errorMessage;

    public function mount()
    {
        $this->allProductGroups = ProductGroup::all();
    }

    protected $rules = [
        'name' => 'required|string|max:255',
        'contactPerson' => 'required|string|max:255',
        'telephone' => 'required|string|max:50',
        'street' => 'required|string|max:255',
        'housenumber' => 'required|string|max:10',
        'postalCode' => 'required|string|max:10',
        'city' => 'required|string|max:255',
        'exactId' => 'required|string|max:255'
    ];

    public function saveProspect()
    {
        $this->validate();

        $existingRecord = Customer::where('postal_code', $this->postalCode)
            ->where('housenumber', $this->housenumber)
            ->orWhere(function ($query) {
                $query->where('postal_code', $this->postalCode)
                    ->where('housenumber', $this->housenumber);
            })->exists();

        $existingRecordSupplier = Supplier::where('postal_code', $this->postalCode)
            ->where('housenumber', $this->housenumber)
            ->orWhere(function ($query) {
                $query->where('postal_code', $this->postalCode)
                    ->where('housenumber', $this->housenumber);
            })->exists();

        $existingRecordProspect = Prospect::where('postal_code', $this->postalCode)
            ->where('housenumber', $this->housenumber)
            ->orWhere(function ($query) {
                $query->where('postal_code', $this->postalCode)
                    ->where('housenumber', $this->housenumber);
            })->exists();

        if ($existingRecord || $existingRecordSupplier || $existingRecordProspect) {
            $this->errorMessage = 'Er bestaat al een relatie met dezelfde postcode en huisnummer.';
            return;
        }

        $prospect = new Prospect();
        $prospect->company_name = $this->name;
        $prospect->name = $this->contactPerson;
        $prospect->street_name = $this->street;
        $prospect->housenumber = $this->housenumber;
        $prospect->postal_code = $this->postalCode;
        $prospect->city = $this->city;
        $prospect->country = 'NL';
        $prospect->telephone = $this->telephone;
        $prospect->owned_by = Auth::id();
        $prospect->exact_id = $this->exactId;
        $prospect->save();

        $prospect->productGroups()->attach($this->selectedProductGroups);

        return redirect()->route('prospect.edit', ['prospect' => $prospect->id]);
    }

    public function render()
    {
        return view('livewire.add-prospect');
    }
}
