<?php

namespace App\Livewire;

use App\Models\Task;
use App\Models\User;
use Livewire\Component;

class ManagerTasks extends Component
{
    public $tasks;
    public $userId;
    public $users;

    public function mount()
    {
        $this->users = User::all();
    }

    public function updatedUserId()
    {
        $this->loadTasks();
    }

    public function loadTasks()
    {
        $this->tasks = Task::where('assigned_to_user_id', $this->userId)->latest()->get();
    }

    public function render()
    {
        return view('livewire.manager-tasks');
    }
}
