<?php

namespace App\Livewire;

use App\Models\Action;
use App\Models\OwnerNotification;
use App\Models\Task;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class OverdueTasks extends Component
{
    public $actions;
    public $isModalOpen = false;
    public $modalTask;
    public $modalTaskId;
    public $note;
    public $dueDate;
    public $users;
    public $description;
    public $ownerId;

    protected $listeners = ['savedDueDate' => 'loadTasks'];

    public function mount()
    {
        $this->users = User::all();
        $this->loadTasks();
    }

    public function loadTasks()
    {
        $startOfWeek = Carbon::now()->startOfWeek();

        $this->actions = Action::with('actionable')
            ->where('assigned_to_user_id', Auth::id())
            ->where('due_date', '<', $startOfWeek)
            ->orderBy('due_date', 'asc')
            ->get();
    }

    public function showModal(Action $action)
    {
        $this->isModalOpen = true;
        $this->modalTaskId = $action->id;
        $this->modalTask = $action;
        $this->description = $action->description;
        $this->dueDate = $action->due_date;
        $this->ownerId = $action->assigned_to_user_id;
    }

    public function updatedDescription($value)
    {
        if (strpos($value, '@@') !== false) {
            $this->description = str_replace('@@', '[' . now()->format('d-m-Y') . '] ', $value);
        }
    }

    public function saveTask()
    {
        $this->modalTask->due_date = $this->dueDate;
        $this->modalTask->description = $this->description;
        $this->modalTask->assigned_to_user_id = $this->ownerId;
        $this->modalTask->save();
        $this->isModalOpen = false;
        $this->dispatch('savedDueDate');
    }

    public function deleteTask()
    {
        $this->modalTask->delete();
        $this->isModalOpen = false;
        $this->dispatch('savedDueDate');
    }

    public function completeTask()
    {
        $this->modalTask->delete();
        $this->isModalOpen = false;

        if ($this->modalTask->created_by_user_id != $this->modalTask->assigned_to_user_id) {
            OwnerNotification::create([
                'owner_id' => $this->modalTask->created_by_user_id,
                'editor_id' => $this->modalTask->assigned_to_user_id,
                'notifiable_id' => $this->modalTask->actionable->id,
                'notifiable_type' => $this->modalTask->actionable_type,
                'action' => 'edited',
                'details' => Auth::user()->name . ' heeft een taak afgerond: ' . $this->modalTask->due_date . ' - ' . $this->modalTask->description,
            ]);
        }
    }

    public function render()
    {
        return view('livewire.overdue-tasks');
    }
}
