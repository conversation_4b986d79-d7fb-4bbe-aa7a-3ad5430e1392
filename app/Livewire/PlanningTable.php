<?php

namespace App\Livewire;

use App\Models\Purchase;
use Livewire\Component;
use Yajra\DataTables\Facades\DataTables;

class PlanningTable extends Component
{
    public function render()
    {
        return view('livewire.planning-table');
    }

    public function getPurchases()
    {
        $query = Purchase::with(['product', 'supplier', 'sales.address'])
            ->where('ready_for_planning', 1)
            ->select('purchases.*');

        return DataTables::of($query)
            ->addColumn('addresses', function ($purchase) {
                return $purchase->sales
                    ->map(fn ($sale) => optional($sale->address)->city)
                    ->filter()
                    ->implode(', ');
            })
            ->addColumn('customers', function ($purchase) {
                return $purchase->sales
                    ->map(fn ($sale) => optional($sale->address)->company_name)
                    ->filter()
                    ->implode(', ');
            })
            ->filterColumn('addresses', function ($query, $keyword) {
                $query->whereHas('sales.address', function ($subQuery) use ($keyword) {
                    $subQuery->where('street', 'like', "%$keyword%")
                        ->orWhere('city', 'like', "%$keyword%")
                        ->orWhere('company_name', 'like', "%$keyword%");
                });
            })
            ->make(true);
    }
}
