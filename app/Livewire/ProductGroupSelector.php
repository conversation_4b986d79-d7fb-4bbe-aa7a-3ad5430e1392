<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\ProductGroup;

class ProductGroupSelector extends Component
{
    public $selectedGroups = [];
    public $dropdownValue = '';
    public $productGroups;
    public $selectedGroupsForInput;
    public $entity;

    public function loadProductGroups()
    {
        $this->productGroups = ProductGroup::all();
    }

    public function updatedSelectedGroups()
    {
        $this->selectedGroupsForInput = json_encode($this->selectedGroups);
        $this->entity->productGroups()->sync($this->selectedGroups);
    }

    public function mount($currentSelectedGroups = [])
    {
        $this->selectedGroups = $currentSelectedGroups;
        $this->updatedSelectedGroups();
        $this->loadProductGroups();
    }

    public function addProductGroup()
    {
        if (!in_array($this->dropdownValue, $this->selectedGroups) && $this->dropdownValue) {
            $this->selectedGroups[] = (int)$this->dropdownValue;
            $this->updatedSelectedGroups();
        }
        $this->dropdownValue = ''; // Reset dropdown
    }

    public function removeProductGroup($groupId)
    {
        $this->selectedGroups = array_filter($this->selectedGroups, function ($value) use ($groupId) {
            return $value != $groupId;
        });
        $this->updatedSelectedGroups();
    }

    public function render()
    {
        return view('livewire.product-group-selector');
    }
}
