<?php

namespace App\Livewire;

use App\Models\Purchase;
use Livewire\Component;
use Yajra\DataTables\Facades\DataTables;

class CompletedTable extends Component
{
    public function render()
    {
        return view('livewire.completed-table');
    }

    public function getPurchases()
    {
        $query = Purchase::with('product', 'supplier')->where('completed', 1);

        return DataTables::of($query)->make(true);
    }
}
