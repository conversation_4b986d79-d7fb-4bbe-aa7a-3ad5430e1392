<?php

namespace App\Livewire;

use App\Models\OwnerNotification;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class Notifications extends Component
{
    public $notifications;

    protected $listeners = ['refresh' => '$refresh'];

    public function mount()
    {
        $this->notifications = OwnerNotification::where('owner_id', Auth::id())->latest()->get();
    }

    public function dismissNotification(OwnerNotification $notification)
    {
        $notification->delete();
        $this->dispatch('refresh');
    }

    public function clearAll()
    {
        foreach($this->notifications as $notification) {
            $notification->delete();
        }
        $this->dispatch('refresh');
    }

    public function render()
    {
        return view('livewire.notifications');
    }
}
