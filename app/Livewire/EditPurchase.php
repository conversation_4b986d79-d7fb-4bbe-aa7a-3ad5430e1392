<?php

namespace App\Livewire;

use App\Models\Customer;
use App\Models\Mail;
use App\Models\Purchase;
use App\Models\Shipper;
use App\Models\ShippingAddress;
use App\Models\Supplier;
use App\Services\GeocodeService;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail as MailFacade;
use App\Mail\TransporterMail;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Storage;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Spatie\LivewireFilepond\WithFilePond;
use Symfony\Component\HttpFoundation\StreamedResponse;

class EditPurchase extends Component
{
    use WithFilePond, WithFileUploads;

    public $file;
    public $purchase;
    public $showModal = false;
    public $customers;
    public $selectedCustomer;
    public $errorMessage;
    public $search = '';
    public $saleQty;
    public $salePrice;
    public $street;
    public $housenumber;
    public $postalCode;
    public $city;
    public $transportPrice;
    public $customerPurchaseNumber;
    public $totalQtySold;
    public $loadDate;
    public $deliveryDate;
    public $totalTransport;
    public $totalPrice;
    public $log;
    public $showShipperModal = false;
    public $shipperSearch = '';
    public $selectedShipper;
    public $shippers;
    public $showShipperMailModal = false;
    public $shipperMail;
    public $messageTextShipper;
    public $shipperMails;
    public $shipperMailAttachments = [];
    public $tempAttachments = [];
    public $singleFileUpload; // For single file uploads in production
    public $uploadedFiles = [];
    public $includeCmr = false;
    public $showCompleteModal = false;
    public $actualQuantities = [];

    // Custom supplier address fields
    public $showSupplierAddressModal = false;
    public $supplierStreet;
    public $supplierHousenumber;
    public $supplierPostalCode;
    public $supplierCity;
    public $supplierCountry = 'NL';
    public $customSupplierAddress = false;

    // Supplier purchase number
    public $supplierPurchaseNumber;

    // Own transport
    public $ownTransport = false;

    // Weight fields
    public $loadWeight;
    public $unloadWeight;

    // Transport cost management
    public $showTransportCostModal = false;
    public $selectedSaleForTransport;
    public $saleTransportCost;
    public $transportCostType = 'per_unit'; // 'per_unit' or 'total'
    public $transportCostPerUnit;
    public $transportCostTotal;

    // Quantity management
    public $purchaseQuantity;
    public $saleQuantities = [];

    protected $rules = [
        'purchaseQuantity' => 'required|numeric|min:0.01',
        'saleQuantities.*' => 'required|numeric|min:0.01',
    ];

    /**
     * Initializes the component with purchase details and calculates aggregated data.
     *
     * @param int $purchaseId The ID of the purchase to load.
     */
    public function mount($purchaseId)
    {
        $this->purchase = Purchase::with(['sales.customer', 'shipper'])->find($purchaseId);
        $this->loadDate = Carbon::parse($this->purchase->load_date)->format('Y-m-d');
        $this->deliveryDate = Carbon::parse($this->purchase->delivery_date)->format('Y-m-d');
        $this->log = $this->purchase->log;
        $this->selectedShipper = $this->purchase->shipper;
        if ($this->selectedShipper) {
            $this->shipperMail = $this->selectedShipper->email_address;
        }

        foreach ($this->purchase->sales as $sale) {
            $this->totalQtySold += $sale->quantity;
            $this->totalPrice += $sale->quantity * $sale->price;
            $this->totalTransport += $sale->transport_cost;

            // Initialize actual quantities with the current quantity
            $this->actualQuantities[$sale->id] = $sale->actual_quantity ?: $sale->quantity;
        }
        $this->customers = new Collection();
        $this->shippers = new Collection();
        $this->loadShipperMails();

        // Load custom supplier address if it exists
        $this->customSupplierAddress = $this->purchase->custom_supplier_address;
        if ($this->customSupplierAddress) {
            $this->supplierStreet = $this->purchase->supplier_street;
            $this->supplierHousenumber = $this->purchase->supplier_housenumber;
            $this->supplierPostalCode = $this->purchase->supplier_postal_code;
            $this->supplierCity = $this->purchase->supplier_city;
            $this->supplierCountry = $this->purchase->supplier_country ?: 'NL';
        } else {
            // Initialize with supplier's default address
            $this->supplierStreet = $this->purchase->supplier->street_name;
            $this->supplierHousenumber = $this->purchase->supplier->housenumber;
            $this->supplierPostalCode = $this->purchase->supplier->postal_code;
            $this->supplierCity = $this->purchase->supplier->city;
            $this->supplierCountry = $this->purchase->supplier->country;
        }

        // Load supplier purchase number if it exists
        $this->supplierPurchaseNumber = $this->purchase->supplier_purchase_number;

        // Load own transport setting
        $this->ownTransport = $this->purchase->own_transport;

        // Load weight values
        $this->loadWeight = $this->purchase->load_weight;
        $this->unloadWeight = $this->purchase->unload_weight;

        // Load quantity values
        $this->purchaseQuantity = $this->purchase->quantity;

        // Initialize sale quantities
        foreach ($this->purchase->sales as $sale) {
            $this->saleQuantities[$sale->id] = (float)$sale->quantity;
        }
    }

    /**
     * Updates the list of customers based on the search query.
     */
    public function updatedSearch()
    {
        $this->customers = Customer::where('company_name', 'like', '%'.$this->search.'%')->get();
    }

    public function updatedShipperSearch()
    {
        $this->shippers = Supplier::where('company_name', 'like', '%'.$this->shipperSearch.'%')->get();
    }

    public function updatedDeliveryDate()
    {
        $this->purchase->delivery_date = $this->deliveryDate;
        $this->log .= '<li>' . Carbon::now()->format('d-m-Y H:i') . ' - ' . auth()->user()->name .  ':  Losdatum gewijzigd naar ' . Carbon::parse($this->purchase->delivery_date)->format('d-m-Y') . '</li>';
        $this->purchase->log = $this->log;
        $this->purchase->save();
    }

    public function updatedLoadDate()
    {
        $this->purchase->load_date = $this->loadDate;
        $this->log .= '<li>' . Carbon::now()->format('d-m-Y H:i') . ' - ' . auth()->user()->name .  ':  Laaddatum gewijzigd naar ' . Carbon::parse($this->purchase->load_date)->format('d-m-Y') . '</li>';
        $this->purchase->log = $this->log;
        $this->purchase->save();
    }

    /**
     * Update the supplier purchase number
     */
    public function updatedSupplierPurchaseNumber()
    {
        $this->purchase->supplier_purchase_number = $this->supplierPurchaseNumber;
        $this->log .= '<li>' . Carbon::now()->format('d-m-Y H:i') . ' - ' . auth()->user()->name .  ': Leverancier inkoopnummer gewijzigd naar ' . $this->supplierPurchaseNumber . '</li>';
        $this->purchase->log = $this->log;
        $this->purchase->save();
    }

    /**
     * Update the own transport setting
     */
    public function updatedOwnTransport()
    {
        $this->purchase->own_transport = $this->ownTransport;

        // If own transport is selected, clear the shipper
        if ($this->ownTransport) {
            $this->purchase->shipper_id = null;
            $this->selectedShipper = null;
            $this->log .= '<li>' . Carbon::now()->format('d-m-Y H:i') . ' - ' . auth()->user()->name .  ': Transport gewijzigd naar eigen transport (transporteur verwijderd)</li>';
        } else {
            $this->log .= '<li>' . Carbon::now()->format('d-m-Y H:i') . ' - ' . auth()->user()->name .  ': Eigen transport uitgeschakeld</li>';
        }

        $this->purchase->log = $this->log;
        $this->purchase->save();
    }

    public function selectCustomer(Customer $customer)
    {
        $this->selectedCustomer = $customer;
        $this->street = $customer->street_name;
        $this->housenumber = $customer->housenumber;
        $this->postalCode = $customer->postal_code;
        $this->city = $customer->city;
    }

    public function selectShipper(Supplier $shipper)
    {
        $this->selectedShipper = $shipper;
        $this->shipperMail = $shipper->email_address;
        $this->showShipperModal = false;

        // If a shipper is selected, disable own transport
        $this->ownTransport = false;
        $this->purchase->own_transport = false;

        $this->log .= '<li>' . Carbon::now()->format('d-m-Y H:i') . ' - ' . auth()->user()->name .  ': Transporteur geselecteerd';
        $this->purchase->log = $this->log;
        $this->purchase->shipper_id = $shipper->id;
        $this->purchase->save();
    }

    /**
     * Upload temporary attachments
     */
    public function updatedTempAttachments()
    {
        // Debug logging
        \Log::info('updatedTempAttachments called', [
            'environment' => app()->environment(),
            'tempAttachments_type' => gettype($this->tempAttachments),
            'is_array' => is_array($this->tempAttachments),
            'array_count' => is_array($this->tempAttachments) ? count($this->tempAttachments) : 'N/A',
            'is_uploaded_file' => $this->tempAttachments instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
        ]);

        if (app()->environment('production')) {
            // Check if we have a valid file
            if (!$this->tempAttachments) {
                \Log::warning('No tempAttachments in production');
                return;
            }

            // In production, handle single file upload
            try {
                $fileToProcess = null;

                // Handle both array and single file cases
                if (is_array($this->tempAttachments)) {
                    \Log::info('tempAttachments is an array with ' . count($this->tempAttachments) . ' items');

                    if (count($this->tempAttachments) > 0) {
                        $fileToProcess = $this->tempAttachments[0]; // Take the first file
                        \Log::info('Processing first file from array');
                    }
                } else {
                    \Log::info('tempAttachments is a single file');
                    $fileToProcess = $this->tempAttachments;
                }

                // Check if we have a valid file to process
                if ($fileToProcess && $fileToProcess instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile) {
                    \Log::info('Valid TemporaryUploadedFile detected');

                    // Process the single file directly (Livewire already validated it)
                    $this->processAndStoreFile($fileToProcess);

                    // Clear the temporary attachment after processing
                    $this->tempAttachments = null;

                    \Log::info('File processed successfully in production');
                } else {
                    \Log::warning('No valid TemporaryUploadedFile found', [
                        'fileToProcess_type' => $fileToProcess ? gettype($fileToProcess) : 'null',
                        'fileToProcess_class' => is_object($fileToProcess) ? get_class($fileToProcess) : 'not an object'
                    ]);

                    $this->addError('tempAttachments', 'Ongeldig bestandstype ontvangen.');
                }
            } catch (\Exception $e) {
                \Log::error('File upload error in production', [
                    'error' => $e->getMessage(),
                    'line' => $e->getLine(),
                    'file' => $e->getFile()
                ]);

                $this->addError('tempAttachments', 'Er is een fout opgetreden bij het uploaden: ' . $e->getMessage());
            }
        } else {
            // In local development, handle both single and multiple files
            try {
                if (is_array($this->tempAttachments)) {
                    \Log::info('Local development: Processing array of files', [
                        'count' => count($this->tempAttachments)
                    ]);

                    $this->validate([
                        'tempAttachments.*' => 'file|max:10240', // 10MB max file size
                    ]);
                } else {
                    \Log::info('Local development: Processing single file');

                    $this->validate([
                        'tempAttachments' => 'file|max:10240', // 10MB max file size
                    ]);
                }
            } catch (\Exception $e) {
                \Log::error('File validation error in local development', [
                    'error' => $e->getMessage()
                ]);

                $this->addError('tempAttachments', 'Bestandsvalidatie mislukt: ' . $e->getMessage());
            }
        }
    }

    /**
     * Handle single file upload for production
     */
    public function updatedSingleFileUpload()
    {
        \Log::info('updatedSingleFileUpload called', [
            'environment' => app()->environment(),
            'singleFileUpload_type' => gettype($this->singleFileUpload),
            'is_uploaded_file' => $this->singleFileUpload instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
        ]);

        if (!$this->singleFileUpload) {
            \Log::warning('No singleFileUpload');
            return;
        }

        try {
            // Check if it's a TemporaryUploadedFile instance
            if ($this->singleFileUpload instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile) {
                \Log::info('Valid single TemporaryUploadedFile detected');

                // Process the single file directly (Livewire already validated it)
                $this->processAndStoreFile($this->singleFileUpload);

                // Clear the temporary attachment after processing
                $this->singleFileUpload = null;

                \Log::info('Single file processed successfully');
            } else {
                \Log::warning('singleFileUpload is not a TemporaryUploadedFile', [
                    'type' => gettype($this->singleFileUpload),
                    'class' => is_object($this->singleFileUpload) ? get_class($this->singleFileUpload) : 'not an object'
                ]);

                $this->addError('singleFileUpload', 'Ongeldig bestandstype ontvangen.');
            }
        } catch (\Exception $e) {
            \Log::error('Single file upload error', [
                'error' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile()
            ]);

            $this->addError('singleFileUpload', 'Er is een fout opgetreden bij het uploaden: ' . $e->getMessage());
        }
    }

    /**
     * Manual upload method as fallback
     */
    public function processManualUpload()
    {
        $fileToProcess = null;

        // Check which property has the file
        if (app()->environment('production')) {
            if (!$this->singleFileUpload) {
                $this->addError('singleFileUpload', 'Geen bestand geselecteerd.');
                return;
            }
            $fileToProcess = $this->singleFileUpload;
        } else {
            if (!$this->tempAttachments) {
                $this->addError('tempAttachments', 'Geen bestand geselecteerd.');
                return;
            }

            // Handle both array and single file cases for local
            if (is_array($this->tempAttachments)) {
                if (count($this->tempAttachments) > 0) {
                    $fileToProcess = $this->tempAttachments[0]; // Take the first file
                }
            } else {
                $fileToProcess = $this->tempAttachments;
            }
        }

        if (!$fileToProcess) {
            $this->addError('tempAttachments', 'Geen geldig bestand gevonden.');
            return;
        }

        try {
            // Process the file
            $this->processAndStoreFile($fileToProcess);

            // Clear the temporary attachment
            if (app()->environment('production')) {
                $this->singleFileUpload = null;
            } else {
                $this->tempAttachments = null;
            }

            session()->flash('message', 'Bestand succesvol geüpload.');
        } catch (\Exception $e) {
            \Log::error('Manual upload error', ['error' => $e->getMessage()]);
            $this->addError('tempAttachments', 'Upload mislukt: ' . $e->getMessage());
        }
    }

    /**
     * Process and store a single file
     *
     * @param TemporaryUploadedFile $file
     * @return void
     */
    protected function processAndStoreFile($file)
    {
        try {
            $filename = $file->getClientOriginalName();
            $uniqueFilename = uniqid() . '-' . $filename;

            if (app()->environment('production')) {
                // In production, store in S3
                $path = $file->storeAs(
                    'mail-attachments/' . $this->purchase->id,
                    $uniqueFilename,
                    's3'
                );
            } else {
                // In local development, store in local disk
                $path = $file->storeAs(
                    'mail-attachments/' . $this->purchase->id,
                    $uniqueFilename,
                    'public'
                );
            }

            // Add to uploaded files array
            $this->uploadedFiles[] = [
                'name' => $filename,
                'path' => $path,
                'size' => $file->getSize(),
                'mime' => $file->getMimeType()
            ];

            // Log successful upload
            \Illuminate\Support\Facades\Log::info('File uploaded: ' . $filename);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error uploading file: ' . $e->getMessage());
        }
    }

    /**
     * Remove a file from the uploaded files list
     *
     * @param int $index
     * @return void
     */
    public function removeUploadedFile($index)
    {
        if (isset($this->uploadedFiles[$index])) {
            $file = $this->uploadedFiles[$index];

            try {
                // Delete the file from storage
                $disk = app()->environment('production') ? 's3' : 'public';
                Storage::disk($disk)->delete($file['path']);

                // Remove from the array
                unset($this->uploadedFiles[$index]);
                $this->uploadedFiles = array_values($this->uploadedFiles); // Re-index array

                // Log deletion
                \Illuminate\Support\Facades\Log::info('File removed: ' . $file['name']);
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::error('Error removing file: ' . $e->getMessage());
            }
        }
    }

    /**
     * Process and store file attachments
     *
     * @return array Array of stored file paths
     */
    protected function processAttachments()
    {
        $storedFiles = $this->uploadedFiles; // Start with already uploaded files

        // In local development, process files that are still in tempAttachments
        if (!app()->environment('production') && !empty($this->tempAttachments)) {
            foreach ($this->tempAttachments as $attachment) {
                /** @var TemporaryUploadedFile $attachment */
                $filename = $attachment->getClientOriginalName();

                // Store in local disk
                $path = $attachment->storeAs(
                    'mail-attachments/' . $this->purchase->id,
                    uniqid() . '-' . $filename,
                    'public'
                );

                $storedFiles[] = [
                    'name' => $filename,
                    'path' => $path,
                    'size' => $attachment->getSize(),
                    'mime' => $attachment->getMimeType()
                ];
            }
        }

        return $storedFiles;
    }

    /**
     * Send email to shipper with attachments
     *
     * @return StreamedResponse|void
     */
    public function sendShipperMail()
    {
        // Validate required fields
        $this->validate([
            'shipperMail' => 'required|email',
            'messageTextShipper' => 'required|min:1',
        ], [
            'shipperMail.required' => 'Het e-mailadres is verplicht.',
            'shipperMail.email' => 'Voer een geldig e-mailadres in.',
            'messageTextShipper.required' => 'Het bericht is verplicht.',
        ]);

        // Process and store attachments
        $attachments = $this->processAttachments();

        try {
            // Create mail record
            $mail = new Mail();
            $mail->purchase_id = $this->purchase->id;
            $mail->mail_to = $this->shipperMail;
            $mail->mail_message = $this->messageTextShipper;
            $mail->attachments = $attachments;
            $mail->save();

            // Log the values for debugging
            \Illuminate\Support\Facades\Log::info('Mail creation values:', [
                'purchase_id' => $this->purchase->id,
                'mail_to' => $this->shipperMail,
                'message_length' => strlen($this->messageTextShipper),
                'attachments_count' => count($attachments)
            ]);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error creating mail record: ' . $e->getMessage());
            session()->flash('error', 'Er is een fout opgetreden bij het versturen van de e-mail: ' . $e->getMessage());
            return;
        }

        // Generate CMR PDF if needed
        if ($this->includeCmr) {
            try {
                // Prepare data for CMR template
                $cmrData = $this->prepareCmrData();

                // Generate CMR PDF with purchase data and fixed A4 settings
                $pdf = PDF::loadView('cmr-template', $cmrData);
                $pdf->setPaper([0, 0, 595.28, 841.89], 'portrait'); // A4 in points
                $pdf->setOptions([
                    'dpi' => 96,
                    'defaultFont' => 'Arial',
                    'isRemoteEnabled' => false,
                    'isHtml5ParserEnabled' => false,
                    'isPhpEnabled' => false,
                    'fontSubsetting' => true,
                    'debugKeepTemp' => false
                ]);

                // Add CMR to attachments
                $cmrFilename = 'CMR-' . $this->purchase->purchase_number . '-' . date('Y-m-d') . '.pdf';
                $cmrPath = 'mail-attachments/' . $this->purchase->id . '/cmr-' . uniqid() . '.pdf';

                if (app()->environment('production')) {
                    Storage::disk('s3')->put($cmrPath, $pdf->output());
                } else {
                    Storage::disk('public')->put($cmrPath, $pdf->output());
                }

                // Update mail record with CMR attachment
                $attachments[] = [
                    'name' => $cmrFilename,
                    'path' => $cmrPath,
                    'size' => strlen($pdf->output()),
                    'mime' => 'application/pdf'
                ];
                $mail->attachments = $attachments;
                $mail->save();

                // Log that CMR was generated and attached
                \Illuminate\Support\Facades\Log::info('CMR generated and attached to email', [
                    'purchase_id' => $this->purchase->id,
                    'purchase_number' => $this->purchase->purchase_number,
                    'filename' => $cmrFilename,
                    'path' => $cmrPath
                ]);
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::error('Error generating CMR: ' . $e->getMessage(), [
                    'purchase_id' => $this->purchase->id,
                    'error_line' => $e->getLine(),
                    'error_file' => $e->getFile()
                ]);

                // Add user-friendly error message
                session()->flash('error', 'Er is een fout opgetreden bij het genereren van de CMR. De e-mail is wel verzonden.');
            }
        }

        // Send the actual email
        try {
            $email = new TransporterMail(
                $this->purchase,
                $this->messageTextShipper ?: 'Geen bericht.', // Ensure we have a default message
                $attachments
            );

            $recipient = $this->shipperMail;

            $email->from(config('mail.from.address'), config('mail.from.name'));

            MailFacade::to($recipient)->send($email);

            if (app()->environment('production')) {
                $this->log .= '<li>' . Carbon::now()->format('d-m-Y H:i') . ' - ' . auth()->user()->name .  ': <strong>TEST MODE</strong> - Email oorspronkelijk bedoeld voor: ' . $this->shipperMail . '</li>';
            }

        } catch (\Exception $e) {
            session()->flash('error', 'Er is een fout opgetreden bij het versturen van de e-mail: ' . $e->getMessage());
            return;
        }

        $this->log .= '<li>' . Carbon::now()->format('d-m-Y H:i') . ' - ' . auth()->user()->name .  ': Email verzonden naar transporteur: ' . $this->shipperMail . '</li>';
        $this->purchase->log = $this->log;
        $this->purchase->save();

        // Reset form
        $this->showShipperMailModal = false;
        $this->tempAttachments = [];
        $this->uploadedFiles = [];
        $this->includeCmr = false;
        $this->messageTextShipper = '';
        $this->loadShipperMails();

        session()->flash('message', 'E-mail is succesvol verzonden naar ' . $this->shipperMail);
    }

    /**
     * Prepare data for CMR template
     *
     * @return array
     */
    private function prepareCmrData(): array
    {
        $supplierAddress = [
            'street' => $this->customSupplierAddress ? $this->supplierStreet : $this->purchase->supplier->street_name,
            'housenumber' => $this->customSupplierAddress ? $this->supplierHousenumber : $this->purchase->supplier->housenumber,
            'postal_code' => $this->customSupplierAddress ? $this->supplierPostalCode : $this->purchase->supplier->postal_code,
            'city' => $this->customSupplierAddress ? $this->supplierCity : $this->purchase->supplier->city,
            'country' => $this->customSupplierAddress ? $this->supplierCountry : ($this->purchase->supplier->country ?: 'NL')
        ];

        $firstSale = $this->purchase->sales->first();
        $deliveryAddress = null;
        if ($firstSale && $firstSale->shippingAddress) {
            $deliveryAddress = [
                'company_name' => $firstSale->customer->company_name,
                'street' => $firstSale->shippingAddress->street,
                'housenumber' => $firstSale->shippingAddress->housenumber,
                'postal_code' => $firstSale->shippingAddress->postal_code,
                'city' => $firstSale->shippingAddress->city,
                'country' => $firstSale->shippingAddress->country ?: 'NL'
            ];
        }

        $totalQuantity = $this->purchase->sales->sum('quantity');
        $totalWeight = $this->purchase->load_weight ?: $this->purchase->sales->sum('quantity'); // Fallback to quantity if no weight

        return [
            'purchase' => $this->purchase,
            'supplierAddress' => $supplierAddress,
            'deliveryAddress' => $deliveryAddress,
            'totalQuantity' => $totalQuantity,
            'totalWeight' => $totalWeight,
            'loadDate' => $this->purchase->load_date ? \Carbon\Carbon::parse($this->purchase->load_date) : now(),
            'deliveryDate' => $this->purchase->delivery_date ? \Carbon\Carbon::parse($this->purchase->delivery_date) : null,
            'shipper' => $this->purchase->shipper,
            'product' => $this->purchase->product,
            'sales' => $this->purchase->sales,
            'generatedDate' => now(),
            'purchaseNumber' => $this->purchase->purchase_number
        ];
    }

    /**
     * Reload shipper mails to populate communication tab
     *
     * @return void
     */
    public function loadShipperMails(): void
    {
        $this->shipperMails = Mail::where('purchase_id', $this->purchase->id)->get();
    }

    /**
     * Download an attachment
     *
     * @param string $path File path
     * @param string $name File name
     * @return StreamedResponse
     */
    public function downloadAttachment($path, $name)
    {
        if (app()->environment('production')) {
            return Storage::disk('s3')->download($path, $name);
        } else {
            return Storage::disk('public')->download($path, $name);
        }
    }

    /**
     * Add a sale to the purchase
     *
     * @return void
     */
    public function addSale(): void
    {
        if (!$this->selectedCustomer) {
            $this->errorMessage = 'Een klant is verplicht.';

            return;
        }

        if (!$this->saleQty || !$this->salePrice || !$this->transportPrice) {
            $this->errorMessage = 'Aantal, Prijs en Transport Prijs zijn allemaal verplicht.';

            return;
        }

        // Create shipping address with geolocation
        $shippingAddressData = [
            'company_name' => $this->selectedCustomer->company_name,
            'customer_id' => $this->selectedCustomer->id,
            'name' => $this->selectedCustomer->name,
            'street' => $this->street,
            'housenumber' => $this->housenumber,
            'postal_code' => $this->postalCode,
            'city' => $this->city,
            'country' => 'NL',
        ];

        // Get geolocation coordinates for the delivery address
        $geocodeService = new GeocodeService();
        $coordinates = $geocodeService->geocode(
            $this->street,
            $this->housenumber,
            $this->postalCode,
            $this->city,
            'NL'
        );

        if ($coordinates) {
            $shippingAddressData['latitude'] = $coordinates['latitude'];
            $shippingAddressData['longitude'] = $coordinates['longitude'];
        }

        $shippingAddress = ShippingAddress::create($shippingAddressData);

        $sale = $this->purchase->sales()->create([
            'customer_id' => $this->selectedCustomer->id,
            'product_id' => $this->purchase->product_id,
            'price' => $this->salePrice,
            'total_price' => $this->salePrice * $this->saleQty,
            'quantity' => $this->saleQty,
            'sale_qty_type' => $this->purchase->qty_type,
            'user_id' => auth()->id(),
            'shipping_address_id' => $shippingAddress->id,
            'transport_cost' => $this->transportPrice,
            'administration' => "0" . $this->purchase->administration,
            'customer_purchase_number' => $this->customerPurchaseNumber
        ]);

        $this->log .= '<li>' . Carbon::now()->format('d-m-Y H:i') . ' - ' . auth()->user()->name .  ': Verkoop toegevoegd: ' . $this->selectedCustomer->name . ': ' . $this->saleQty . ' ' . $this->purchase->qty_type . ' &euro;' . $sale->total_price . '</li>';
        $this->purchase->log = $this->log;
        $this->purchase->save();
        $this->resetFields();
        $this->reloadPurchaseWithSales();
    }

    public function removeSale($saleId) : void
    {
        $sale = $this->purchase->sales()->where('id', $saleId)->first();

        if ($sale) {
            $this->log .= '<li>' . Carbon::now()->format('d-m-Y H:i') . ' - ' . auth()->user()->name .  ': Verkoop verwijderd: ' . $sale->customer->name . ': ' . $sale->quantity . ' ' . $sale->sale_qty_type . ' &euro;' . $sale->total_price . '</li>';
            $this->purchase->log = $this->log;
            $this->purchase->save();
            $sale->delete();
        }
    }

    /**
     * @return void
     */
    public function reloadPurchaseWithSales(): void
    {
        $this->purchase->load(['sales.customer']);
        $this->totalQtySold = 0;
        $this->totalPrice = 0;
        $this->totalTransport = 0;
        foreach ($this->purchase->sales as $sale) {
            // Use actual quantity if available, otherwise use the original quantity
            $quantity = $sale->actual_quantity ?: $sale->quantity;
            $this->totalQtySold += $quantity;
            $this->totalPrice += $quantity * $sale->price;
            $this->totalTransport += $sale->transport_cost;

            // Update the actual quantities array
            $this->actualQuantities[$sale->id] = $sale->actual_quantity ?: $sale->quantity;

            // Update the sale quantities array
            $this->saleQuantities[$sale->id] = (float)$sale->quantity;
        }
    }

    /**
     * @return void
     */
    public function resetFields(): void
    {
        $this->search = '';
        $this->errorMessage = '';
        $this->selectedCustomer = null;
        $this->customers = new Collection();
        $this->saleQty = NULL;
        $this->salePrice = NULL;
        $this->transportPrice = NULL;
        $this->showModal = false;
    }

    /**
     * Mark as ready for planning
     *
     * @return void
     */
    public function sendToTransport(): void
    {
        $this->purchase->ready_for_planning = true;
        $this->purchase->save();
    }

    public function createTransport()
    {
        $this->purchase->transport()->create([
            'load_date' => $this->purchase->load_date,
            'price' => 0.00
        ]);
    }

    public function complete()
    {
        foreach ($this->purchase->sales as $sale) {
            // Save the original quantity if not already set
            if (!$sale->original_quantity) {
                $sale->original_quantity = $sale->quantity;
            }

            // Save the actual quantity from the form
            if (isset($this->actualQuantities[$sale->id])) {
                $sale->actual_quantity = $this->actualQuantities[$sale->id];

                // Update the log with the quantity change if different
                if ($sale->actual_quantity != $sale->quantity) {
                    $this->log .= '<li>' . Carbon::now()->format('d-m-Y H:i') . ' - ' . auth()->user()->name . ': Werkelijke hoeveelheid voor ' . $sale->customer->company_name . ' gewijzigd van ' . $sale->quantity . ' naar ' . $sale->actual_quantity . ' ' . $sale->sale_qty_type . '</li>';
                }

                // Update the total price based on the actual quantity
                $sale->total_price = $sale->actual_quantity * $sale->price;
            }

            // Don't override individual transport costs that have been set
            // $sale->transport_cost is already set individually per sale
            $sale->save();
        }

        $this->purchase->log = $this->log;
        $this->purchase->ready_for_planning = false;
        $this->purchase->completed = true;
        $this->purchase->save();
        $this->showCompleteModal = false;
    }

    /**
     * Save custom supplier address
     *
     * @return void
     */
    public function saveSupplierAddress(): void
    {
        $this->purchase->custom_supplier_address = true;
        $this->purchase->supplier_street = $this->supplierStreet;
        $this->purchase->supplier_housenumber = $this->supplierHousenumber;
        $this->purchase->supplier_postal_code = $this->supplierPostalCode;
        $this->purchase->supplier_city = $this->supplierCity;
        $this->purchase->supplier_country = $this->supplierCountry;

        // Get geolocation coordinates for the address
        $geocodeService = new GeocodeService();
        $coordinates = $geocodeService->geocode(
            $this->supplierStreet,
            $this->supplierHousenumber,
            $this->supplierPostalCode,
            $this->supplierCity,
            $this->supplierCountry
        );

        if ($coordinates) {
            $this->purchase->supplier_latitude = $coordinates['latitude'];
            $this->purchase->supplier_longitude = $coordinates['longitude'];
        }

        $this->log .= '<li>' . Carbon::now()->format('d-m-Y H:i') . ' - ' . auth()->user()->name .  ': Aangepast leverancier adres opgeslagen</li>';
        $this->purchase->log = $this->log;
        $this->purchase->save();

        $this->customSupplierAddress = true;
        $this->showSupplierAddressModal = false;
    }

    /**
     * Reset supplier address to default
     *
     * @return void
     */
    public function resetSupplierAddress(): void
    {
        $this->purchase->custom_supplier_address = false;
        $this->purchase->supplier_street = null;
        $this->purchase->supplier_housenumber = null;
        $this->purchase->supplier_postal_code = null;
        $this->purchase->supplier_city = null;
        $this->purchase->supplier_country = null;
        $this->purchase->supplier_latitude = null;
        $this->purchase->supplier_longitude = null;

        $this->log .= '<li>' . Carbon::now()->format('d-m-Y H:i') . ' - ' . auth()->user()->name .  ': Leverancier adres teruggezet naar standaard</li>';
        $this->purchase->log = $this->log;
        $this->purchase->save();

        // Reset to supplier's default address
        $this->supplierStreet = $this->purchase->supplier->street_name;
        $this->supplierHousenumber = $this->purchase->supplier->housenumber;
        $this->supplierPostalCode = $this->purchase->supplier->postal_code;
        $this->supplierCity = $this->purchase->supplier->city;
        $this->supplierCountry = $this->purchase->supplier->country;

        $this->customSupplierAddress = false;
    }

    /**
     * Update the load weight
     */
    public function updatedLoadWeight()
    {
        $this->purchase->load_weight = $this->loadWeight;
        $this->log .= '<li>' . Carbon::now()->format('d-m-Y H:i') . ' - ' . auth()->user()->name .  ': Laadgewicht gewijzigd naar ' . ($this->loadWeight ?? 'leeg') . ' kg</li>';
        $this->purchase->log = $this->log;
        $this->purchase->save();
    }

    /**
     * Update the unload weight
     */
    public function updatedUnloadWeight()
    {
        $this->purchase->unload_weight = $this->unloadWeight;
        $this->log .= '<li>' . Carbon::now()->format('d-m-Y H:i') . ' - ' . auth()->user()->name .  ': Losgewicht gewijzigd naar ' . ($this->unloadWeight ?? 'leeg') . ' kg</li>';
        $this->purchase->log = $this->log;
        $this->purchase->save();
    }

    /**
     * Open transport cost modal for a specific sale
     */
    public function openTransportCostModal($saleId)
    {
        $this->selectedSaleForTransport = $this->purchase->sales()->with('customer')->find($saleId);

        if (!$this->selectedSaleForTransport) {
            session()->flash('error', 'Verkoop niet gevonden.');
            return;
        }

        $this->showTransportCostModal = true;

        // Load existing transport cost data from the sale
        $this->saleTransportCost = $this->selectedSaleForTransport->transport_cost;

        // Determine the cost type based on existing data
        if ($this->saleTransportCost > 0) {
            // Try to determine if it's per unit or total based on calculation
            $quantity = $this->selectedSaleForTransport->actual_quantity ?: $this->selectedSaleForTransport->quantity;
            $possiblePerUnit = $quantity > 0 ? $this->saleTransportCost / $quantity : 0;

            // If the per-unit calculation results in a reasonable number, assume per-unit
            if ($possiblePerUnit > 0 && $possiblePerUnit < 1000) {
                $this->transportCostType = 'per_unit';
                $this->transportCostPerUnit = $possiblePerUnit;
                $this->transportCostTotal = null;
            } else {
                $this->transportCostType = 'total';
                $this->transportCostTotal = $this->saleTransportCost;
                $this->transportCostPerUnit = null;
            }
        } else {
            $this->transportCostType = 'per_unit';
            $this->transportCostPerUnit = null;
            $this->transportCostTotal = null;
        }
    }

    /**
     * Close transport cost modal
     */
    public function closeTransportCostModal()
    {
        $this->showTransportCostModal = false;
        $this->resetTransportCostFields();
    }

    /**
     * Reset transport cost fields
     */
    public function resetTransportCostFields()
    {
        $this->selectedSaleForTransport = null;
        $this->saleTransportCost = null;
        $this->transportCostPerUnit = null;
        $this->transportCostTotal = null;
        $this->transportCostType = 'per_unit';
    }

    /**
     * Calculate transport cost based on type for the selected sale
     */
    public function calculateSaleTransportCost()
    {
        if (!$this->selectedSaleForTransport) {
            return 0;
        }

        $quantity = $this->selectedSaleForTransport->actual_quantity ?: $this->selectedSaleForTransport->quantity;

        if ($this->transportCostType === 'per_unit' && $this->transportCostPerUnit) {
            return $this->transportCostPerUnit * $quantity;
        } elseif ($this->transportCostType === 'total' && $this->transportCostTotal) {
            return $this->transportCostTotal;
        }

        return 0;
    }

    /**
     * Update transport cost type and clear opposite field
     */
    public function updatedTransportCostType()
    {
        if ($this->transportCostType === 'per_unit') {
            $this->transportCostTotal = null;
        } else {
            $this->transportCostPerUnit = null;
        }
    }

    /**
     * Save transport cost to the selected sale
     */
    public function saveTransportCost()
    {
        if (!$this->selectedSaleForTransport) {
            return;
        }

        $this->validate([
            'transportCostType' => 'required|in:per_unit,total',
            'transportCostPerUnit' => 'nullable|numeric|min:0',
            'transportCostTotal' => 'nullable|numeric|min:0',
        ]);

        // Ensure the correct field is filled based on type
        if ($this->transportCostType === 'per_unit') {
            $this->validate(['transportCostPerUnit' => 'required|numeric|min:0']);
        } else {
            $this->validate(['transportCostTotal' => 'required|numeric|min:0']);
        }

        // Calculate and save transport cost to the sale
        $calculatedCost = $this->calculateSaleTransportCost();
        $this->selectedSaleForTransport->transport_cost = $calculatedCost;
        $this->selectedSaleForTransport->save();

        // Reload purchase data to update totals
        $this->reloadPurchaseWithSales();

        $this->closeTransportCostModal();

        $customerName = $this->selectedSaleForTransport && $this->selectedSaleForTransport->customer
            ? $this->selectedSaleForTransport->customer->company_name
            : 'onbekende klant';

        session()->flash('message', 'Transport kosten succesvol opgeslagen voor ' . $customerName . '.');
    }

    /**
     * Remove transport cost from the selected sale
     */
    public function removeTransportCost()
    {
        if (!$this->selectedSaleForTransport) {
            return;
        }

        $this->selectedSaleForTransport->transport_cost = 0;
        $this->selectedSaleForTransport->save();

        // Reload purchase data to update totals
        $this->reloadPurchaseWithSales();

        $this->closeTransportCostModal();

        $customerName = $this->selectedSaleForTransport && $this->selectedSaleForTransport->customer
            ? $this->selectedSaleForTransport->customer->company_name
            : 'onbekende klant';

        session()->flash('message', 'Transport kosten verwijderd voor ' . $customerName . '.');
    }

    /**
     * Update purchase quantity when changed
     */
    public function updatedPurchaseQuantity()
    {
        $this->validate([
            'purchaseQuantity' => 'required|numeric|min:0.01'
        ]);

        $oldQuantity = $this->purchase->quantity;
        $this->purchase->quantity = (float)$this->purchaseQuantity;
        $this->purchase->total = (float)$this->purchase->quantity * (float)$this->purchase->price;
        $this->purchase->save();

        // Log the quantity change
        $this->logPurchaseChange("Hoeveelheid gewijzigd van {$oldQuantity} naar {$this->purchaseQuantity} {$this->purchase->qty_type}");

        session()->flash('message', 'Inkoop hoeveelheid bijgewerkt.');
    }

    /**
     * Update individual sale quantity
     */
    public function updateSaleQuantity($saleId, $newQuantity)
    {
        $this->validate([
            "saleQuantities.{$saleId}" => 'required|numeric|min:0.01'
        ]);

        $sale = $this->purchase->sales()->find($saleId);
        if (!$sale) {
            session()->flash('error', 'Verkoop niet gevonden.');
            return;
        }

        $oldQuantity = $sale->quantity;
        $oldTransportCost = $sale->transport_cost;

        // Update quantity and recalculate total price
        $sale->quantity = (float)$newQuantity;
        $sale->total_price = (float)$sale->quantity * (float)$sale->price;

        // Recalculate transport cost if it was likely set per unit
        if ($oldTransportCost > 0 && $oldQuantity > 0) {
            $transportCostPerUnit = $oldTransportCost / $oldQuantity;

            // Determine if this was likely set per unit by checking several criteria:
            // 1. The per-unit cost is a reasonable number (between 0.01 and 500)
            // 2. The per-unit cost has at most 2 decimal places (common for pricing)
            // 3. The calculation is exact (no rounding errors)
            $isLikelyPerUnit = (
                $transportCostPerUnit >= 0.01 &&
                $transportCostPerUnit <= 500 &&
                round($transportCostPerUnit, 2) == $transportCostPerUnit &&
                abs(($transportCostPerUnit * $oldQuantity) - $oldTransportCost) < 0.001
            );

            if ($isLikelyPerUnit) {
                // Recalculate transport cost based on new quantity
                $newTransportCost = round($transportCostPerUnit * $newQuantity, 2);
                $sale->transport_cost = $newTransportCost;

                $customerName = $sale->customer->company_name ?? 'Onbekende klant';
                $this->logPurchaseChange("Transport kosten automatisch herberekend voor {$customerName}: €" . number_format($transportCostPerUnit, 2) . " per {$this->purchase->qty_type} × {$newQuantity} = €" . number_format($newTransportCost, 2));
            }
        }

        $sale->save();

        // Update the local array
        $this->saleQuantities[$saleId] = (float)$newQuantity;

        // Recalculate totals
        $this->reloadPurchaseWithSales();

        // Log the quantity change
        $customerName = $sale->customer ? $sale->customer->company_name : 'Onbekende klant';
        $this->logPurchaseChange("Verkoop hoeveelheid gewijzigd voor {$customerName}: van {$oldQuantity} naar {$newQuantity} {$this->purchase->qty_type}");

        session()->flash('message', "Verkoop hoeveelheid bijgewerkt voor {$customerName}.");
    }

    /**
     * Manually recalculate transport cost for a sale based on per-unit pricing
     * This can be called if the automatic detection didn't work correctly
     */
    public function recalculateTransportCost($saleId, $transportCostPerUnit)
    {
        $sale = $this->purchase->sales()->find($saleId);
        if (!$sale) {
            session()->flash('error', 'Verkoop niet gevonden.');
            return;
        }

        $newTransportCost = round($transportCostPerUnit * $sale->quantity, 2);
        $sale->transport_cost = $newTransportCost;
        $sale->save();

        // Recalculate totals
        $this->reloadPurchaseWithSales();

        $customerName = $sale->customer->company_name ?? 'Onbekende klant';
        $this->logPurchaseChange("Transport kosten handmatig herberekend voor {$customerName}: €" . number_format($transportCostPerUnit, 2) . " per {$this->purchase->qty_type} × {$sale->quantity} = €" . number_format($newTransportCost, 2));

        session()->flash('message', "Transport kosten herberekend voor {$customerName}.");
    }

    /**
     * Check if a sale's transport cost was likely set per unit
     */
    public function isTransportCostPerUnit($sale)
    {
        if ($sale->transport_cost <= 0 || $sale->quantity <= 0) {
            return false;
        }

        $transportCostPerUnit = $sale->transport_cost / $sale->quantity;

        return (
            $transportCostPerUnit >= 0.01 &&
            $transportCostPerUnit <= 500 &&
            round($transportCostPerUnit, 2) == $transportCostPerUnit &&
            abs(($transportCostPerUnit * $sale->quantity) - $sale->transport_cost) < 0.001
        );
    }

    /**
     * Log a change to the purchase
     */
    private function logPurchaseChange($message)
    {
        $currentLog = $this->purchase->log ?: '';
        $timestamp = now()->format('d-m-Y H:i:s');
        $userName = auth()->user()->name ?? 'Systeem';

        $newLogEntry = "[{$timestamp}] {$userName}: {$message}";

        if ($currentLog) {
            $this->purchase->log = $currentLog . "\n" . $newLogEntry;
        } else {
            $this->purchase->log = $newLogEntry;
        }

        $this->purchase->save();

        // Update the local log property
        $this->log = $this->purchase->log;
    }

    public function render()
    {
        return view('livewire.edit-purchase');
    }
}
