<?php

namespace App\Livewire;

use App\Models\Attachment;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\WithFileUploads;

class FileUpload extends Component
{
    use WithFileUploads;

    public $model;
    public $attachment;
    public $attachments = [];
    public $type;

    protected $listeners = ['uploaded' => 'loadAttachments'];

    public function mount($model)
    {
        $this->model = $model;
        $this->type = addslashes(get_class($this->model));
        $this->loadAttachments();
    }

    public function loadAttachments()
    {
        $this->attachments = $this->model->attachments->map(function ($attachment) {
            $fileKey = 'attachments/' . $attachment->filename;
            $attachment->downloadUrl = Storage::temporaryUrl($fileKey, now()->addMinutes(30));

            return $attachment;
        });
    }

    public function deleteAttachment($attachmentId)
    {
        $attachment = Attachment::findOrFail($attachmentId);

        Storage::delete('attachments/' . $attachment->filename);
        $attachment->delete();

        $this->loadAttachments();
    }

    public function render()
    {
        return view('livewire.file-upload');
    }
}
