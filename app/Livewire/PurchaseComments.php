<?php

namespace App\Livewire;

use App\Models\PurchaseComment;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class PurchaseComments extends Component
{
    public $purchase;
    public $comment;
    public $showForm = false;

    protected $rules = [
        'comment' => 'required|max:255',
    ];

    public function addComment()
    {
        $this->showForm = true;
    }

    public function saveComment()
    {
        $this->validate();

        PurchaseComment::create([
            'purchase_id' => $this->purchase->id,
            'user_id' => Auth::id(),
            'comment' => $this->comment,
        ]);

        $this->comment = '';
        $this->showForm = false;
    }

    public function render()
    {
        $comments = PurchaseComment::where('purchase_id', $this->purchase->id)
            ->with('user') // If you want to display user information
            ->orderByDesc('created_at')
            ->get();

        return view('livewire.purchase-comments', compact('comments'));
    }
}
