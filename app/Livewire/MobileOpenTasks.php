<?php

namespace App\Livewire;

use App\Models\Action;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class MobileOpenTasks extends Component
{
    public $actions;

    public function mount()
    {
        $this->loadActions();
    }

    public function loadActions()
    {
        $endOfWeek = Carbon::now()->endOfWeek();
        $this->actions = Action::with('actionable')
            ->where('assigned_to_user_id', Auth::id())
            ->where('due_date', '<=' , $endOfWeek)
            ->orderBy('due_date', 'asc')
            ->get();
    }

    public function render()
    {
        return view('livewire.mobile-open-tasks');
    }
}
