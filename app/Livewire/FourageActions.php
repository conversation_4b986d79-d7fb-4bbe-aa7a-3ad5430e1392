<?php

namespace App\Livewire;

use App\Models\Action;
use Carbon\Carbon;
use Livewire\Component;

class FourageActions extends Component
{
    public $actions;
    public $date;
    public $selectedUser;
    public $selectedFromDate;

    public function mount()
    {
        $this->selectedFromDate = Carbon::now()->subWeeks(1)->format('Y-m-d');
        $this->loadActions();
    }

    public function loadActions()
    {
        $selectionDate = $this->selectedFromDate;
        if ($this->selectedUser) {
            $this->actions = Action::where('created_at', '>', $selectionDate)
                ->where('created_by_user_id', $this->selectedUser)
                ->onlyTrashed()
                ->orWhere(function($query) use ($selectionDate) {
                    $query->whereNull('due_date')
                        ->where('created_at', '>', $selectionDate)
                        ->where('created_by_user_id', $this->selectedUser);
                })
                ->orderBy('created_at', 'desc')
                ->get();
        } else {
        $this->actions = Action::where('created_at', '>', $selectionDate)
            ->whereIn('created_by_user_id', [3,5,10,9])
            ->onlyTrashed()
            ->orWhere(function($query) use ($selectionDate) {
                $query->whereNull('due_date')
                ->where('created_at', '>', $selectionDate)
                    ->whereIn('created_by_user_id', [3,5,10,9]);
            })
            ->orderBy('updated_at', 'desc')
            ->get();
        }
    }

    public function updatedSelectedFromDate()
    {
        $this->loadActions();
    }

    public function updatedSelectedUser()
    {
        if ($this->selectedUser == 'all') {
            $this->selectedUser = NULL;
        }
        $this->loadActions();
    }

    public function render()
    {
        return view('livewire.fourage-actions');
    }
}
