<?php

namespace App\Livewire;

use Livewire\Component;

class EntityDescription extends Component
{
    public $model;
    public $modelId;
    public $modelInstance;
    public $description;
    public $edit = false;

    public function mount()
    {
        $modelClass = '\\App\\Models\\' . ucfirst($this->model);
        $this->modelInstance = $modelClass::find($this->modelId);
        $this->description = $this->modelInstance->description;
        if (!$this->description) {
            $this->edit = true;
        }
    }

    public function saveDescription()
    {
        $this->modelInstance->description = $this->description;
        $this->modelInstance->save();
        $this->dispatch('description-saved');
    }

    public function render()
    {
        return view('livewire.entity-description');
    }
}
