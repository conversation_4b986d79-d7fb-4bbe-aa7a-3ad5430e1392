<?php

namespace App\Livewire;

use App\Models\Product;
use App\Models\Supplier;
use Livewire\Component;
use Illuminate\Support\Collection;

class SupplierProducts extends Component
{
    public $supplier;
    public $supplierId;
    public $productSearch = '';
    public $products;
    public $selectedProduct;
    public $unitPrice;
    public $unitType = 'TON';
    public $endDate;
    public $defaultQty;
    public $supplierProducts;

    protected $rules = [
        'unitPrice' => 'required|numeric|min:0',
        'unitType' => 'required|string',
        'endDate' => 'nullable|date|after:today',
        'defaultQty' => 'nullable|integer|min:1',
    ];

    public function mount(Supplier $supplier)
    {
        $this->supplier = $supplier;
        $this->supplierId = $supplier->id;
        $this->products = new Collection();
        $this->loadSupplierProducts();
    }

    public function loadSupplierProducts()
    {
        // Get only currently active price agreements for this supplier (non-expired agreements)
        // With versioning, there should be only one active agreement per product
        $productsWithPivot = $this->supplier->products()
            ->withPivot('unit_price', 'unit_type', 'end_date', 'default_qty', 'id', 'created_at')
            ->withTimestamps()
            ->where(function($query) {
                $query->whereNull('supplier_products.end_date')
                      ->orWhere('supplier_products.end_date', '>', now()->toDateString());
            })
            ->orderBy('supplier_products.created_at', 'desc')
            ->get();

        // Convert to array format that preserves pivot data
        $this->supplierProducts = $productsWithPivot->map(function($product) {
            return [
                'id' => $product->id,
                'name' => $product->name,
                'sku' => $product->sku,
                'pivot' => [
                    'id' => $product->pivot->id ?? null,
                    'unit_price' => $product->pivot->unit_price ?? null,
                    'unit_type' => $product->pivot->unit_type ?? 'TON',
                    'end_date' => $product->pivot->end_date ?? null,
                    'default_qty' => $product->pivot->default_qty ?? null,
                    'created_at' => $product->pivot->created_at ?? null,
                ]
            ];
        })->values();
    }

    public function updatedProductSearch()
    {
        if (strlen($this->productSearch) >= 2) {
            $this->products = Product::where('name', 'like', '%' . $this->productSearch . '%')
                ->orWhere('sku', 'like', '%' . $this->productSearch . '%')
                ->get();
        } else {
            $this->products = new Collection();
        }
    }

    // For Livewire v3, we need to define the properties that can be updated
    protected function getListeners()
    {
        return [
            'productSearch' => 'updatedProductSearch',
        ];
    }

    public function selectProduct($productId)
    {
        $this->selectedProduct = Product::find($productId);
        $this->productSearch = $this->selectedProduct->name;
        $this->products = new Collection();
    }

    public function addProduct()
    {
        $this->validate();



        // Prepare pivot data
        $pivotData = [
            'unit_price' => $this->unitPrice,
            'unit_type' => $this->unitType,
            'end_date' => $this->endDate,
            'default_qty' => $this->defaultQty,
        ];

        // Implement versioning: expire any existing active agreements for this product
        $existingActiveAgreements = $this->supplier->products()
            ->wherePivot('product_id', $this->selectedProduct->id)
            ->where(function($query) {
                $query->whereNull('supplier_products.end_date')
                      ->orWhere('supplier_products.end_date', '>', now()->toDateString());
            })
            ->get();

        // Set end_date on all existing active agreements to yesterday
        foreach ($existingActiveAgreements as $agreement) {
            $this->supplier->products()->updateExistingPivot($agreement->id, [
                'end_date' => now()->subDay()->toDateString()
            ]);
        }

        // Add the new active price agreement
        $this->supplier->products()->attach($this->selectedProduct->id, $pivotData);
        session()->flash('message', 'Product price agreement added successfully. Previous agreements have been expired.');

        // Reset fields
        $this->productSearch = '';
        $this->selectedProduct = null;
        $this->unitPrice = null;
        $this->unitType = 'TON';
        $this->endDate = null;
        $this->defaultQty = null;

        // Reload supplier products
        $this->loadSupplierProducts();
    }

    public function removeProduct($productId)
    {
        $this->supplier->products()->detach($productId);
        $this->loadSupplierProducts();
        session()->flash('message', 'Product removed successfully.');
    }

    public function render()
    {
        return view('livewire.supplier-products');
    }
}
