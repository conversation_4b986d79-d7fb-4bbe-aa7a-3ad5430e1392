<?php

namespace App\Livewire;

use App\Models\ProductGroup;
use Livewire\Component;

class ProductGroups extends Component
{
    public $productGroups;
    public $showModal = false;
    public $name;
    public $editingId = null;
    public $showDeleteModal = false;
    public $deletingId = null;

    protected $listeners = ['saved' => 'loadProductGroups'];

    public function mount()
    {
        $this->loadProductGroups();
    }

    public function loadProductGroups()
    {
        $this->productGroups = ProductGroup::all();
    }

    public function editProductGroup($id)
    {
        $productGroup = ProductGroup::find($id);
        $this->name = $productGroup->name;
        $this->editingId = $id;
        $this->showModal = true;
    }

    public function confirmDelete($id)
    {
        $this->deletingId = $id;
        $this->showDeleteModal = true;
    }

    public function deleteProductGroup()
    {
        $productGroup = ProductGroup::find($this->deletingId);
        $productGroup->delete();

        $this->showDeleteModal = false;
        $this->dispatch('saved');
    }

    public function saveProductGroup()
    {
        $this->validate([
            'name' => 'required|string',
        ]);

        if ($this->editingId) {
            $productGroup = ProductGroup::find($this->editingId);
            $productGroup->name = $this->name;
            $productGroup->save();
            $this->editingId = null;
        } else {
            ProductGroup::create(['name' => $this->name]);
        }

        $this->showModal = false;
        $this->dispatch('saved');
    }

    public function render()
    {
        return view('livewire.product-groups');
    }
}
