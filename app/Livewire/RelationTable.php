<?php

namespace App\Livewire;

use App\Models\Customer;
use App\Models\Prospect;
use App\Models\Supplier;
use Livewire\Component;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class RelationTable extends Component
{
    public function render()
    {
        return view('livewire.relation-table');
    }

    public function getRelations()
    {
        $prospects = Prospect::query()
            ->leftJoin('product_groupables', function ($join) {
                $join->on('prospects.id', '=', 'product_groupables.product_groupable_id')
                    ->where('product_groupables.product_groupable_type', '=', 'App\\Models\\Prospect');
            })
            ->leftJoin('product_groups', 'product_groupables.product_group_id', '=', 'product_groups.id')
            ->select([
                DB::raw("'prospect' AS type"),
                'prospects.id',
                'prospects.name',
                DB::raw('IFNULL(GROUP_CONCAT(product_groups.name SEPARATOR ", "), "") as product_groups')
            ])
            ->groupBy('prospects.id');

        $suppliers = Supplier::query()
            ->leftJoin('product_groupables', function ($join) {
                $join->on('suppliers.id', '=', 'product_groupables.product_groupable_id')
                    ->where('product_groupables.product_groupable_type', '=', 'App\\Models\\Supplier');
            })
            ->leftJoin('product_groups', 'product_groupables.product_group_id', '=', 'product_groups.id')
            ->select([
                DB::raw("'supplier' AS type"),
                'suppliers.id',
                'suppliers.name',
                DB::raw('IFNULL(GROUP_CONCAT(product_groups.name SEPARATOR ", "), "") as product_groups')
            ])
            ->groupBy('suppliers.id');

        $customers = Customer::query()
            ->leftJoin('product_groupables', function ($join) {
                $join->on('customers.id', '=', 'product_groupables.product_groupable_id')
                    ->where('product_groupables.product_groupable_type', '=', 'App\\Models\\Customer');
            })
            ->leftJoin('product_groups', 'product_groupables.product_group_id', '=', 'product_groups.id')
            ->select([
                DB::raw("'customer' AS type"),
                'customers.id',
                'customers.name',
                DB::raw('IFNULL(GROUP_CONCAT(product_groups.name SEPARATOR ", "), "") as product_groups')
            ])
            ->groupBy('customers.id');

        $query = $prospects->unionAll($suppliers)->unionAll($customers);

        return DataTables::of($query)
            ->filterColumn('product_groups', function ($query, $keyword) {
                $query->whereRaw("product_groups.name LIKE ?", ["%$keyword%"]);
            })
            ->addColumn('product_groups', function ($row) {
                return $row->product_groups ?: '-';
            })
            ->make(true);
    }
}
