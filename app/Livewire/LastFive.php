<?php

namespace App\Livewire;

use Livewire\Component;

class LastFive extends Component
{
    public $model;
    public $actions;

    public function mount($model)
    {
        $this->model = $model;
        $this->loadLastFive();
    }

    public function loadLastFive()
    {
        $this->actions = $this->model->actions()->latest()->take(5)->get();
    }

    public function render()
    {
        return view('livewire.last-five');
    }
}
