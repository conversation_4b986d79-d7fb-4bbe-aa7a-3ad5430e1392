<?php

namespace App\Livewire;

use App\Models\ContactPerson;
use App\Models\Customer;
use App\Models\PlanTask;
use App\Models\Prospect;
use App\Models\SharedPlanList as SharedPlanListModel;
use App\Models\Supplier;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use App\Models\SharedPlanList;

class SharedPlanListTasks extends Component
{
    public $planlistid;
    public $planlist;
    public $planListName;
    public $relation;
    public $contactInfoModal = false;
    public $communicationModal = false;
    public $updateTaskModal = false;
    public $confirmDelete = false;
    public $showHistoryModal = false;
    public $showNewTaskModal = false;
    public $changeNameModal = false;
    public $showEditContactPerson = false;
    public $showEditEntityModal = false;
    public $contactPersons;
    public $selected;
    public $description;
    public $updateAssignedTo;
    public $updateDescription;
    public $updateDueDate;
    public $users;
    public $lastCommunications;
    public $lastNotes;
    public $showTo;
    public $search;
    public $results = [];
    public $dueDate;
    public $assignedTo;
    public $moveTo;
    public $companyName;
    public $name;
    public $street;
    public $housenumber;
    public $postalCode;
    public $city;
    public $entityDescription;
    public $telephone;
    public $myPlanLists;
    public $selectedContactPerson;
    public $contactName;
    public $contactEmail;
    public $contactTelephone;
    public $contactMobile;
    public $contactNote;
    public $tasks;
    public $errorMessage;

    protected $listeners = ['refresh' => '$refresh'];

    public function mount()
    {
        $this->myPlanLists = SharedPlanListModel::with(['users', 'creator'])
            ->where(function ($query) {
                $query->where('created_by_user_id', Auth::id())
                    ->orWhereHas('users', function ($query) {
                        $query->where('user_id', Auth::id());
                    });
            })
            ->get();
        $this->loadPlanList();
        $this->assignedTo = Auth::id();
    }

    public function removePlanlist()
    {
        $this->planlist->delete();
        return redirect()->route('planlist.index');
    }

    public function loadPlanList()
    {
        $this->planlist = SharedPlanList::find($this->planlistid);
        $this->planListName = $this->planlist->name;

        $toDate = $this->showTo ?? Carbon::now()->endOfWeek();
        $user = Auth::id();
        $this->tasks = PlanTask::whereDate('due_date', '<=', $toDate)
            ->where('shared_plan_list_id', $this->planlistid)
            ->orderBy('due_date', 'asc')
            ->get();

        $this->users = User::all();
    }

    public function updatedShowTo($showTo)
    {
        $this->showTo = $showTo;
        $this->loadPlanList();
    }

    public function updateName()
    {
        $this->planlist->name = $this->planListName;
        $this->planlist->save();
        $this->changeNameModal = false;
    }

    public function openContactInfo($taskId)
    {
        $task = PlanTask::find($taskId);
        $this->relation = $task->plannable;
        $this->contactPersons = $task->plannable->contactPersons;
        $this->contactInfoModal = true;
    }

    public function editEntity()
    {
        $this->companyName = $this->relation->company_name;
        $this->name = $this->relation->name;
        $this->street = $this->relation->street_name;
        $this->housenumber = $this->relation->housenumber;
        $this->postalCode = $this->relation->postal_code;
        $this->telephone = $this->relation->telephone;
        $this->city = $this->relation->city;
        $this->entityDescription = $this->relation->description;

        $this->contactInfoModal = false;
        $this->showEditEntityModal = true;
    }

    public function editContactPerson(ContactPerson $contactPerson)
    {
        $this->selectedContactPerson = $contactPerson;
        $this->contactName = $contactPerson->name;
        $this->contactEmail = $contactPerson->email;
        $this->contactTelephone = $contactPerson->phone_number;
        $this->contactMobile = $contactPerson->mobile_phone_number;
        $this->contactNote = $contactPerson->note;

        $this->contactInfoModal = false;
        $this->showEditContactPerson = true;
    }

    public function saveContact()
    {
        $this->selectedContactPerson->name = $this->contactName;
        $this->selectedContactPerson->email = $this->contactEmail;
        $this->selectedContactPerson->phone_number = $this->contactTelephone;
        $this->selectedContactPerson->mobile_phone_number = $this->contactMobile;
        $this->selectedContactPerson->note = $this->contactNote;
        $this->selectedContactPerson->save();

        $this->showEditContactPerson = false;
    }

    public function saveEntity()
    {
        $this->relation->company_name = $this->companyName;
        $this->relation->name = $this->name;
        $this->relation->street_name = $this->street;
        $this->relation->housenumber = $this->housenumber;
        $this->relation->postal_code = $this->postalCode;
        $this->relation->city = $this->city;
        $this->relation->telephone = $this->telephone;
        $this->relation->description = $this->entityDescription;

        $this->relation->save();
        $this->showEditEntityModal = false;
    }

    public function openRegisterContact($taskId)
    {
        $this->selected = PlanTask::find($taskId);
        $this->relation = $this->selected->plannable;
        $this->communicationModal = true;
    }

    public function updatePlantask()
    {
        $this->relation->actions()->create([
            'type' => 'call',
            'description' => $this->description,
            'created_by_user_id' => Auth::id(),
            'assigned_to_user_id' => Auth::id()
        ]);
        $this->selected->due_date = $this->dueDate;
        $this->selected->save();
        $this->communicationModal = false;
        $this->loadPlanList();
    }

    public function openPlanTask($taskId)
    {
        $this->selected = PlanTask::find($taskId);

        $this->updateDescription = $this->selected->description;
        $this->updateAssignedTo = $this->selected->assigned_to_user_id;
        $this->updateDueDate = $this->selected->due_date;
        $this->updateTaskModal = true;
    }

    public function updatedUpdateDescription($value)
    {
        if (strpos($value, '@@') !== false) {
            $this->updateDescription = str_replace('@@', '[' . now()->format('d-m-Y') . '] ', $value);
        }
    }

    public function saveUpdated()
    {
        if ($this->moveTo == 'personal') {
            $this->selected->shared_plan_list_id = NULL;
        } elseif ($this->moveTo != 0) {
            $this->selected->shared_plan_list_id = $this->moveTo;
        }

        $this->selected->description = $this->updateDescription;
        $this->selected->assigned_to_user_id = $this->updateAssignedTo;
        $this->selected->due_date = $this->updateDueDate;
        $this->selected->save();
        $this->moveTo = NULL;
        $this->loadPlanList();
        $this->updateTaskModal = false;
    }

    public function openDeleteTask($taskId)
    {
        $this->selected = PlanTask::find($taskId);
        $this->confirmDelete = true;
    }

    public function deleteTask()
    {
        $this->selected->delete();
        $this->loadPlanList();
        $this->confirmDelete = false;
    }

    public function showHistory($taskId)
    {
        $this->selected = PlanTask::find($taskId);
        $entity = $this->selected->plannable;
        $this->lastCommunications = $entity->actions()
            ->with('createdBy')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

//        $this->lastNotes = $entity->notes()
//            ->orderBy('created_at', 'desc')
//            ->take(5)
//            ->get();

        $this->showHistoryModal = true;
    }

    public function updatedSearch()
    {
        $this->errorMessage = null;
        $this->results = array_merge(
            Customer::where('company_name', 'like', '%' . $this->search . '%')->get()->map(function($customer) {
                $customerArray = $customer->toArray();
                $customerArray['classname'] = 'Customer';  // append classname
                return $customerArray;
            })->all(),
            Supplier::where('company_name', 'like', '%' . $this->search . '%')->get()->map(function($supplier) {
                $supplierArray = $supplier->toArray();
                $supplierArray['classname'] = 'Supplier';  // append classname
                return $supplierArray;
            })->all(),
            Prospect::where('company_name', 'like', '%' . $this->search . '%')->get()->map(function($prospect) {
                $prospectArray = $prospect->toArray();
                $prospectArray['classname'] = 'Prospect';  // append classname
                return $prospectArray;
            })->all()
        );
    }

    public function selectEntity($entityId, $className)
    {
        $modelClass = '\\App\\Models\\' . $className;
        $this->selected = $modelClass::find($entityId);
        $this->search = $this->selected->company_name;
        $this->results = [];

        $existingTask = PlanTask::where('shared_plan_list_id', $this->planlistid)
            ->where('plannable_id', $this->selected->id)
            ->where('type', 'call')
            ->exists();

        if ($existingTask) {
            $this->errorMessage = 'Deze relatie bestaat al in de planlijst.';
            return;
        }
    }

    public function saveTask()
    {
        $this->validate([
            'dueDate' => 'required|date',
            'description' => 'required|string',
        ]);

        $this->selected->planTasks()->create([
            'due_date' => $this->dueDate,
            'created_by_user_id' => Auth::id(),
            'description' => $this->description,
            'assigned_to_user_id' => $this->assignedTo ?? Auth::id(),
            'shared_plan_list_id' => $this->planlistid,
            'type' => 'call'
        ]);
        $this->dispatch('refresh');
        $this->showNewTaskModal = false;
        $this->description = null;
        $this->assignedTo = null;
        $this->dueDate = null;
    }

    public function render()
    {
        return view('livewire.shared-plan-list-tasks');
    }
}
