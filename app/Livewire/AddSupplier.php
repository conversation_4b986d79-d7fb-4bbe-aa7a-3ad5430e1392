<?php

namespace App\Livewire;

use App\Models\Customer;
use App\Models\ProductGroup;
use App\Models\Prospect;
use App\Models\Supplier;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class AddSupplier extends Component
{
    public $showModal = false;
    public $name;
    public $contactPerson;
    public $telephone;
    public $street;
    public $housenumber;
    public $postalCode;
    public $city;
    public $exactId;
    public $allProductGroups;
    public $selectedProductGroups = [];
    public $errorMessage;

    public function mount()
    {
        $this->allProductGroups = ProductGroup::all();
    }

    protected $rules = [
        'name' => 'required|string|max:255',
        'contactPerson' => 'required|string|max:255',
        'telephone' => 'required|string|max:50',
        'street' => 'required|string|max:255',
        'housenumber' => 'required|string|max:10',
        'postalCode' => 'required|string|max:10',
        'city' => 'required|string|max:255',
        'exactId' => 'required|string|max:255'
    ];

    public function saveSupplier()
    {
        $this->validate();

        $existingRecord = Customer::where('postal_code', $this->postalCode)
            ->where('housenumber', $this->housenumber)
            ->orWhere(function ($query) {
                $query->where('postal_code', $this->postalCode)
                    ->where('housenumber', $this->housenumber);
            })->exists();

        $existingRecordSupplier = Supplier::where('postal_code', $this->postalCode)
            ->where('housenumber', $this->housenumber)
            ->orWhere(function ($query) {
                $query->where('postal_code', $this->postalCode)
                    ->where('housenumber', $this->housenumber);
            })->exists();

        $existingRecordProspect = Prospect::where('postal_code', $this->postalCode)
            ->where('housenumber', $this->housenumber)
            ->orWhere(function ($query) {
                $query->where('postal_code', $this->postalCode)
                    ->where('housenumber', $this->housenumber);
            })->exists();

        if ($existingRecord || $existingRecordSupplier || $existingRecordProspect) {
            $this->errorMessage = 'Er bestaat al een relatie met dezelfde postcode en huisnummer.';
            return;
        }

        $supplier = new Supplier();
        $supplier->company_name = $this->name;
        $supplier->name = $this->contactPerson;
        $supplier->street_name = $this->street;
        $supplier->housenumber = $this->housenumber;
        $supplier->postal_code = $this->postalCode;
        $supplier->city = $this->city;
        $supplier->country = 'NL';
        $supplier->telephone = $this->telephone;
        $supplier->owned_by = Auth::id();
        $supplier->exact_id = $this->exactId;
        $supplier->save();

        $supplier->productGroups()->attach($this->selectedProductGroups);

        return redirect()->route('supplier.edit', ['supplier' => $supplier->id]);
    }
    
    public function render()
    {
        return view('livewire.add-supplier');
    }
}
