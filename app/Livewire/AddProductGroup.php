<?php

namespace App\Livewire;

use App\Models\ProductGroup;
use Livewire\Component;

class AddProductGroup extends Component
{
    public $showModal = false;
    public $name;

    public function saveProductGroup()
    {
        $productGroup = new ProductGroup();
        $productGroup->name = $this->name;
        $productGroup->save();

        $this->showModal = false;
    }

    public function render()
    {
        return view('livewire.add-product-group');
    }
}
