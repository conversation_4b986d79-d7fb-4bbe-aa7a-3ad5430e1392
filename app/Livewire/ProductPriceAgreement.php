<?php

namespace App\Livewire;

use App\Models\Product;
use App\Models\Supplier;
use App\Models\SupplierProductPrice;
use Illuminate\Database\Eloquent\Collection;
use Livewire\Component;

class ProductPriceAgreement extends Component
{
    public $productid;
    public $product;
    public $selectedSupplier;
    public $suppliers;
    public $supplierSearch = '';
    public $priceAgreements = null;
    public $showModal = false;
    public $tierFrom;
    public $tierTo;
    public $tierPrice;

    public function mount()
    {
        $this->product = Product::find($this->productid);
        $this->suppliers = new Collection();
        $this->priceAgreements = new Collection();
    }

    public function updatedSupplierSearch()
    {
        $this->suppliers = Supplier::where('company_name', 'like', '%'.$this->supplierSearch.'%')->get();
    }

    public function selectSupplier(Supplier $supplier)
    {
        $this->selectedSupplier = $supplier;
        $this->priceAgreements = $this->product->supplierPricesFor($supplier->id) ?? collect();
    }

    public function saveTier()
    {
        if (!$this->tierFrom || !$this->tierTo || !$this->tierPrice || $this->tierFrom >= $this->tierTo) {
            session()->flash('error', 'Vul een geldig bereik en prijs in.');
            return;
        }

        // Controleer of er een bestaande prijsafspraak is met overlappend bereik
        $existingAgreement = SupplierProductPrice::where('product_id', $this->product->id)
            ->where('supplier_id', $this->selectedSupplier->id)
            ->where(function ($query) {
                $query->whereBetween('min_qty', [$this->tierFrom, $this->tierTo])
                    ->orWhereBetween('max_qty', [$this->tierFrom, $this->tierTo])
                    ->orWhere(function ($subQuery) {
                        $subQuery->where('min_qty', '<=', $this->tierFrom)
                            ->where('max_qty', '>=', $this->tierTo);
                    });
            })
            ->exists();

        if ($existingAgreement) {
            session()->flash('error', 'Er bestaat al een prijsafspraak met dit bereik.');
            return;
        }

        // Maak een nieuwe prijsafspraak aan
        SupplierProductPrice::create([
            'product_id'  => $this->product->id,
            'supplier_id' => $this->selectedSupplier->id,
            'min_qty'     => $this->tierFrom,
            'max_qty'     => $this->tierTo,
            'price'       => $this->tierPrice,
        ]);

        // Vernieuw de lijst met prijsafspraken
        $this->priceAgreements = $this->product->supplierPricesFor($this->selectedSupplier->id);

        // Reset de invoervelden
        $this->tierFrom = null;
        $this->tierTo = null;
        $this->tierPrice = null;

        $this->showModal = false;
        session()->flash('success', 'Prijsafspraak succesvol opgeslagen.');
    }

    public function render()
    {
        return view('livewire.product-price-agreement');
    }
}
