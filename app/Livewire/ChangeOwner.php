<?php

namespace App\Livewire;

use App\Models\User;
use Livewire\Component;

class ChangeOwner extends Component
{
    public $entity;
    public $model;
    public $ownerId;
    public $users;
    public $entityId;
    public $showModal = false;
    public $owner;

    public function mount($model)
    {
        $this->model = $model;
        $this->loadOwner();
    }

    public function loadOwner()
    {
        $this->users = User::all();
        $this->entity = $this->model::find($this->entityId);
        $this->ownerId = $this->entity->owned_by ?? null;
        if ($this->ownerId) {
            $user = User::find($this->ownerId);
            $this->owner = $user->name;
        }
    }

    public function updateOwner()
    {
        $this->entity->owned_by = $this->ownerId;
        $this->entity->save();
        $this->showModal = false;
        $this->loadOwner();
    }

    public function render()
    {
        return view('livewire.change-owner');
    }
}
