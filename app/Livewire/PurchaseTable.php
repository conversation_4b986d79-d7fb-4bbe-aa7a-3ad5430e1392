<?php

namespace App\Livewire;

use App\Models\Purchase;
use Livewire\Component;
use Yajra\DataTables\Facades\DataTables;

class PurchaseTable extends Component
{
    public function render()
    {
        return view('livewire.purchase-table');
    }

    public function getPurchases()
    {
        $query = Purchase::with('product', 'supplier')->where('ready_for_planning', 0)->where('completed', false);

        return DataTables::of($query)->make(true);
    }
}
