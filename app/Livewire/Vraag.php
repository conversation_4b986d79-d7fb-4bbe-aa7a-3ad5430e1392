<?php

namespace App\Livewire;

use App\Models\Customer;
use App\Models\Demand;
use App\Models\Product;
use App\Models\Purchase;
use App\Models\Sale;
use App\Models\ShippingAddress;
use Illuminate\Database\Eloquent\Collection;
use Livewire\Component;

class Vraag extends Component
{
    public $showModal = false;
    public $showAddProductModal = false;
    public $search = '';
    public $customers;
    public $selectedCustomer;
    public $selectedProduct = null;
    public $productSearch = '';
    public $products;
    public $customerProducts;
    public $saleQty;
    public $salePrice;
    public $saleQtyType = 'TON';
    public $message;
    public $street;
    public $housenumber;
    public $postalCode;
    public $city;
    public $deliveryDate;
    public $customerPurchaseNumber;
    public $unloadingTimes;
    public $comments;

    // Customer address fields
    public $showAddressModal = false;
    public $showAddAddressModal = false;
    public $selectedAddressId = null;
    public $customerAddresses = [];
    public $newAddressName;
    public $newAddressStreet;
    public $newAddressHousenumber;
    public $newAddressPostalCode;
    public $newAddressCity;
    public $newAddressCountry = 'NL';
    public $newAddressUnloadingTimes;
    public $demands;
    public $selectedDemand;
    public $selectedPurchase;
    public $showLinkModal = false;
    public $showProductMismatchModal = false;
    public $selectedDemands = [];
    public $tempLoadDate;
    public $mismatchMessage;

    // Properties for adding a product to a customer
    public $addProductSearch = '';
    public $searchResults = [];
    public $newSelectedProduct = null;
    public $newProductPrice;
    public $newProductUnitType = 'TON';
    public $newProductEndDate;
    public $newProductDefaultQty;

    protected $listeners = [
        'purchase-selected' => 'handlePurchaseSelected',
        'show' => 'show',
        'refresh-vraag' => 'refreshDemands'
    ];

    public function show($until, $showDate, $administration)
    {
        $query = Demand::with('customer', 'product');

        if ($until) {
            $query->where('delivery_date', '<=', $showDate);
        } else {
            $query->where('delivery_date', $showDate);
        }

        // Administration filtering is not applicable for demands
        // as the demands table doesn't have an administration field

        $this->demands = $query->orderBy('delivery_date', 'asc')->get();
    }

    public function mount()
    {
        $this->customers = new Collection();
        $this->products = new Collection();
        $this->demands = new Collection(); // Initialize with empty collection, will be populated by 'show' event

        // Load initial data with default settings (current date, until=true)
        $this->loadInitialData();
    }

    public function loadInitialData()
    {
        // Use the same default settings as the Toolbar component
        $showDate = now()->format('Y-m-d');
        $until = true;
        $administration = null;

        $this->show($until, $showDate, $administration);
    }

    public function updatedSearch()
    {
        $this->customers = Customer::where('company_name', 'like', '%'.$this->search.'%')->get();
    }

    public function selectDemand(Demand $demand)
    {
        if ($this->selectedDemand && $this->selectedDemand->id === $demand->id) {
            $this->selectedDemand = null;
            return;
        }
        $this->selectedDemand = $demand;

        if ($this->selectedPurchase) {
            $this->showLinkModal = true;
        }

        $this->dispatch('demand-selected', demandId: $demand->id);
    }

    public function updatedProductSearch()
    {
        $this->products = Product::where('name', 'like', '%'.$this->productSearch.'%')->get();
    }

    public function selectCustomer(Customer $customer)
    {
        $this->selectedCustomer = $customer;
        $this->street = $customer->street_name;
        $this->housenumber = $customer->housenumber;
        $this->postalCode = $customer->postal_code;
        $this->city = $customer->city;
        $this->search = '';
        $this->customers = new Collection();

        // Load customer unloading times
        $this->unloadingTimes = $customer->unloading_times;

        // Load customer addresses
        $this->loadCustomerAddresses();

        // Load products with unit prices for this customer
        $this->loadCustomerProducts();
    }

    /**
     * Load addresses for the selected customer
     */
    public function loadCustomerAddresses()
    {
        if (!$this->selectedCustomer) {
            $this->customerAddresses = [];
            return;
        }

        // Get addresses for this customer
        $this->customerAddresses = $this->selectedCustomer->shippingAddresses()->get();

        // If no addresses are found, create a default one from the customer's main address
        if ($this->customerAddresses->isEmpty()) {
            $defaultAddress = \App\Models\ShippingAddress::create([
                'customer_id' => $this->selectedCustomer->id,
                'name' => 'Hoofdadres',
                'company_name' => $this->selectedCustomer->company_name,
                'street' => $this->selectedCustomer->street_name,
                'housenumber' => $this->selectedCustomer->housenumber,
                'postal_code' => $this->selectedCustomer->postal_code,
                'city' => $this->selectedCustomer->city,
                'country' => $this->selectedCustomer->country ?: 'NL',
                'unloading_times' => $this->selectedCustomer->unloading_times,
                'is_default' => true
            ]);

            $this->customerAddresses = collect([$defaultAddress]);
            $this->selectedAddressId = $defaultAddress->id;

            // Set address fields from the default address
            $this->street = $defaultAddress->street;
            $this->housenumber = $defaultAddress->housenumber;
            $this->postalCode = $defaultAddress->postal_code;
            $this->city = $defaultAddress->city;
            $this->unloadingTimes = $defaultAddress->unloading_times;
        } else {
            // Select the default address if available, otherwise select the first one
            $defaultAddress = $this->customerAddresses->firstWhere('is_default', true);
            $selectedAddress = $defaultAddress ?: $this->customerAddresses->first();
            $this->selectedAddressId = $selectedAddress->id;

            // Set address fields from the selected address
            $this->street = $selectedAddress->street;
            $this->housenumber = $selectedAddress->housenumber;
            $this->postalCode = $selectedAddress->postal_code;
            $this->city = $selectedAddress->city;
            $this->unloadingTimes = $selectedAddress->unloading_times;
        }
    }

    /**
     * Load products with unit prices for the selected customer
     */
    public function loadCustomerProducts()
    {
        if (!$this->selectedCustomer) {
            $this->customerProducts = new Collection();
            return;
        }

        // Get only currently active price agreements for this customer (non-expired agreements)
        // Only shows products with valid, current pricing - excludes expired agreements
        // With versioning, there should be only one active agreement per product
        $productsWithPivot = $this->selectedCustomer->products()
            ->withPivot('unit_price', 'unit_type', 'end_date', 'default_qty', 'id', 'created_at')
            ->withTimestamps()
            ->where(function($query) {
                $query->whereNull('customer_products.end_date')
                      ->orWhere('customer_products.end_date', '>', now()->toDateString());
            })
            ->orderBy('customer_products.created_at', 'desc')
            ->get();

        // Convert to array format that preserves pivot data
        $this->customerProducts = $productsWithPivot->map(function($product) {
            return [
                'id' => $product->id,
                'name' => $product->name,
                'sku' => $product->sku,
                'pivot' => [
                    'id' => $product->pivot->id ?? null,
                    'unit_price' => $product->pivot->unit_price ?? null,
                    'unit_type' => $product->pivot->unit_type ?? 'TON',
                    'end_date' => $product->pivot->end_date ?? null,
                    'default_qty' => $product->pivot->default_qty ?? null,
                    'created_at' => $product->pivot->created_at ?? null,
                ]
            ];
        })->values();

        // If no products with prices are found, set a message
        if (empty($this->customerProducts)) {
            $this->message = 'Er zijn geen producten met prijzen gevonden voor deze klant.';
        } else {
            $this->message = '';
        }
    }

    public function selectProduct($index)
    {
        // If no product is selected (empty string from dropdown), reset and return
        if ($index === '' || $index === null) {
            $this->selectedProduct = null;
            $this->salePrice = null;
            return;
        }

        // Convert to integer if it's a string from the wire:change event
        if (is_string($index)) {
            $index = (int) $index;
        }

        // Reset values first
        $this->salePrice = null;
        $this->saleQty = null;
        $this->saleQtyType = 'TON';

        // Get the specific price agreement by index from the customerProducts collection
        if (!isset($this->customerProducts[$index])) {
            return;
        }

        $selectedAgreement = $this->customerProducts[$index];

        // Now selectedAgreement is an array, not an Eloquent model
        $this->selectedProduct = (object) $selectedAgreement; // Convert to object for compatibility
        $this->productSearch = '';
        $this->products = new Collection();

        // Set the price, unit type, and default quantity automatically from the selected price agreement
        if ($selectedAgreement && isset($selectedAgreement['pivot'])) {
            $pivotData = $selectedAgreement['pivot'];

            // Set price if available
            if (isset($pivotData['unit_price']) && $pivotData['unit_price']) {
                $this->salePrice = (float) $pivotData['unit_price'];
            }

            // Set unit type if available
            if (isset($pivotData['unit_type']) && $pivotData['unit_type']) {
                $this->saleQtyType = $pivotData['unit_type'];

                // If unit type is VOLLE VRACHT or HALVE VRACHT, set quantity to 1
                if ($this->saleQtyType === 'VOLLE VRACHT' || $this->saleQtyType === 'HALVE VRACHT') {
                    $this->saleQty = 1;
                }
            }

            // Set default quantity if available and not already set by unit type logic
            if (isset($pivotData['default_qty']) &&
                $pivotData['default_qty'] &&
                $this->saleQtyType !== 'VOLLE VRACHT' &&
                $this->saleQtyType !== 'HALVE VRACHT') {
                $this->saleQty = (int) $pivotData['default_qty'];
            }
        }
    }

    /**
     * Handle unit type change and set quantity to 1 for special types
     */
    public function updatedSaleQtyType()
    {
        if ($this->saleQtyType === 'VOLLE VRACHT' || $this->saleQtyType === 'HALVE VRACHT') {
            $this->saleQty = 1;
        }
    }

    public function save()
    {
        $this->showModal = false;

        // Ensure quantity is 1 for VOLLE VRACHT or HALVE VRACHT
        if ($this->saleQtyType === 'VOLLE VRACHT' || $this->saleQtyType === 'HALVE VRACHT') {
            $this->saleQty = 1;
        }

        // Get the selected address
        $selectedAddress = null;
        if ($this->selectedAddressId) {
            $selectedAddress = \App\Models\ShippingAddress::find($this->selectedAddressId);
        }

        // Update the unloading times for the selected address
        if ($selectedAddress && $selectedAddress->unloading_times !== $this->unloadingTimes) {
            $selectedAddress->update([
                'unloading_times' => $this->unloadingTimes
            ]);
        }

        // Create the new demand
        $demand = Demand::create([
            'customer_id' => $this->selectedCustomer->id,
            'product_id' => $this->selectedProduct->id,
            'user_id' => auth()->id(),
            'qty_type' => $this->saleQtyType,
            'quantity' => $this->saleQty,
            'price' => $this->salePrice,
            'delivery_date' => $this->deliveryDate,
            'street' => $this->street,
            'housenumber' => $this->housenumber,
            'postal_code' => $this->postalCode,
            'city' => $this->city,
            'customer_purchase_number' => $this->customerPurchaseNumber,
            'comments' => $this->comments
        ]);

        // Reset form fields
        $this->resetFields();

        // Refresh the demands list to include the new entry
        $this->refreshDemands();
    }

    public function handlePurchaseSelected($purchaseId)
    {
        $this->selectedPurchase = $purchaseId;
        if ($this->selectedDemand) {
            // Get the purchase and demand products to check if they match
            $purchase = \App\Models\Purchase::with('product')->find($purchaseId);
            $demand = $this->selectedDemand;

            // Check if the products match
            if ($purchase && $demand && $purchase->product_id !== $demand->product_id) {
                // Products don't match, show warning modal
                $this->mismatchMessage = "De producten komen niet overeen. \n\n";
                $this->mismatchMessage .= "Vraag product: {$demand->product->name} ({$demand->product->sku})\n";
                $this->mismatchMessage .= "Aanbod product: {$purchase->product->name} ({$purchase->product->sku})";
                $this->showProductMismatchModal = true;
            } else {
                // Products match, show link modal
                $this->showLinkModal = true;
            }
        }
    }

    public function link()
    {
        // Use existing shipping address if available, otherwise create a new one
        $shippingAddressId = null;

        // Check if there's an existing address with the same details
        $existingAddress = ShippingAddress::where([
            'customer_id' => $this->selectedDemand->customer_id,
            'street' => $this->selectedDemand->street,
            'housenumber' => $this->selectedDemand->housenumber,
            'postal_code' => $this->selectedDemand->postal_code,
            'city' => $this->selectedDemand->city,
        ])->first();

        if ($existingAddress) {
            $shippingAddressId = $existingAddress->id;

            // Update unloading times if needed
            if ($this->selectedDemand->unloading_times && $existingAddress->unloading_times !== $this->selectedDemand->unloading_times) {
                $existingAddress->update([
                    'unloading_times' => $this->selectedDemand->unloading_times
                ]);
            }
        } else {
            // Create shipping address from demand data
            $newAddress = ShippingAddress::create([
                'company_name' => $this->selectedDemand->customer->company_name,
                'customer_id' => $this->selectedDemand->customer_id,
                'name' => $this->selectedDemand->customer->name,
                'street' => $this->selectedDemand->street,
                'housenumber' => $this->selectedDemand->housenumber,
                'postal_code' => $this->selectedDemand->postal_code,
                'city' => $this->selectedDemand->city,
                'country' => 'NL',
                'unloading_times' => $this->selectedDemand->unloading_times,
            ]);

            $shippingAddressId = $newAddress->id;
        }

        // Create sale from demand data
        Sale::create([
            'customer_id' => $this->selectedDemand->customer_id,
            'product_id' => $this->selectedDemand->product_id,
            'purchase_id' => $this->selectedPurchase,
            'shipping_address_id' => $shippingAddressId,
            'price' => $this->selectedDemand->price,
            'total_price' => $this->selectedDemand->price * $this->selectedDemand->quantity,
            'quantity' => $this->selectedDemand->quantity,
            'sale_qty_type' => $this->selectedDemand->qty_type,
            'user_id' => auth()->id(),
            'transport_cost' => 0,
            'administration' => "030", // Default administration code
            'customer_purchase_number' => $this->selectedDemand->customer_purchase_number
        ]);

        // Mark the purchase as ready for planning
        $purchase = Purchase::find($this->selectedPurchase);
        if ($purchase) {
            $purchase->ready_for_planning = true;
            $purchase->delivery_date = $this->selectedDemand->delivery_date;
            $purchase->save();
        }

        // Delete the demand since it's fulfilled
        $this->selectedDemand->delete();

        // Close the modal and refresh the demands list
        $this->showLinkModal = false;
        $this->refreshDemands();

        // Reset selections
        $this->selectedDemand = null;
        $this->selectedPurchase = null;

        // Dispatch events to reload other components
        $this->dispatch('refresh-plannable');
        $this->dispatch('refresh-aanbod');
        $this->dispatch('refresh-vraag');
    }

    /**
     * Select an address from the address book
     */
    public function selectAddress($addressId)
    {
        $this->selectedAddressId = $addressId;
        $selectedAddress = $this->customerAddresses->firstWhere('id', $addressId);
        if ($selectedAddress) {
            $this->street = $selectedAddress->street;
            $this->housenumber = $selectedAddress->housenumber;
            $this->postalCode = $selectedAddress->postal_code;
            $this->city = $selectedAddress->city;
            $this->unloadingTimes = $selectedAddress->unloading_times;
        }
        $this->showAddressModal = false;
    }

    /**
     * Open the add address modal
     */
    public function openAddAddressModal()
    {
        $this->showAddAddressModal = true;
        $this->newAddressName = '';
        $this->newAddressStreet = '';
        $this->newAddressHousenumber = '';
        $this->newAddressPostalCode = '';
        $this->newAddressCity = '';
        $this->newAddressCountry = 'NL';
        $this->newAddressUnloadingTimes = $this->unloadingTimes; // Pre-fill with current unloading times
    }

    /**
     * Save a new address
     */
    public function saveNewAddress()
    {
        // Validate inputs
        $this->validate([
            'newAddressName' => 'required|string|max:255',
            'newAddressStreet' => 'required|string|max:255',
            'newAddressHousenumber' => 'required|string|max:20',
            'newAddressPostalCode' => 'required|string|max:10',
            'newAddressCity' => 'required|string|max:255',
        ]);

        if (!$this->selectedCustomer) {
            return;
        }

        // Create new address
        $newAddress = \App\Models\ShippingAddress::create([
            'customer_id' => $this->selectedCustomer->id,
            'name' => $this->newAddressName,
            'company_name' => $this->selectedCustomer->company_name,
            'street' => $this->newAddressStreet,
            'housenumber' => $this->newAddressHousenumber,
            'postal_code' => $this->newAddressPostalCode,
            'city' => $this->newAddressCity,
            'country' => $this->newAddressCountry,
            'unloading_times' => $this->newAddressUnloadingTimes,
            'is_default' => false
        ]);

        // Reload customer addresses and select the new one
        $this->loadCustomerAddresses();
        $this->selectedAddressId = $newAddress->id;
        $this->street = $newAddress->street;
        $this->housenumber = $newAddress->housenumber;
        $this->postalCode = $newAddress->postal_code;
        $this->city = $newAddress->city;
        $this->unloadingTimes = $newAddress->unloading_times;

        // Close modal
        $this->showAddAddressModal = false;
    }

    public function resetFields()
    {
        $this->search = '';
        $this->selectedCustomer = null;
        $this->customers = new Collection();
        $this->saleQty = NULL;
        $this->salePrice = NULL;
        $this->saleQtyType = 'TON';
        $this->message = '';
        $this->selectedProduct = null;
        $this->productSearch = '';
        $this->products = new Collection();
        $this->customerProducts = new Collection();
        $this->street = '';
        $this->housenumber = '';
        $this->postalCode = '';
        $this->city = '';
        $this->deliveryDate = '';
        $this->customerPurchaseNumber = null;
        $this->unloadingTimes = null;
        $this->comments = null;
        $this->selectedAddressId = null;
        $this->customerAddresses = [];
    }

    /**
     * Cancel the demand creation and reset all fields
     */
    public function cancelDemand()
    {
        $this->resetFields();
        $this->showModal = false;
    }

    public function updateSelectedLoadDates()
    {
        Demand::whereIn('id', $this->selectedDemands)
            ->update(['delivery_date' => $this->tempLoadDate]);

        $this->selectedDemands = [];
        $this->tempLoadDate = null;

        // Refresh the demands list
        $this->refreshDemands();
    }

    public function deleteSelected()
    {
        Demand::whereIn('id', $this->selectedDemands)->delete();
        $this->selectedDemands = [];
        $this->tempLoadDate = null;

        // Refresh the demands list
        $this->refreshDemands();
    }

    /**
     * Open the add product modal
     */
    public function openAddProductModal()
    {
        $this->showAddProductModal = true;
        $this->addProductSearch = '';
        $this->searchResults = [];
        $this->newSelectedProduct = null;
        $this->newProductPrice = null;
        $this->newProductUnitType = 'TON';
    }

    /**
     * Search for products when adding a new product to a customer
     */
    public function updatedAddProductSearch()
    {
        if (strlen($this->addProductSearch) >= 2) {
            $this->searchResults = Product::where('name', 'like', '%' . $this->addProductSearch . '%')
                ->orWhere('sku', 'like', '%' . $this->addProductSearch . '%')
                ->get();
        } else {
            $this->searchResults = [];
        }
    }

    /**
     * Select a product when adding a new product to a customer
     */
    public function selectNewProduct($productId)
    {
        $this->newSelectedProduct = Product::find($productId);
        $this->addProductSearch = $this->newSelectedProduct->name;
        $this->searchResults = [];
    }

    /**
     * Add a product to the customer
     */
    public function addProductToCustomer()
    {
        // Validate inputs
        $this->validate([
            'newProductPrice' => 'required|numeric|min:0',
            'newProductUnitType' => 'required|string',
            'newProductEndDate' => 'nullable|date|after:today',
            'newProductDefaultQty' => 'nullable|integer|min:1',
        ]);

        if (!$this->selectedCustomer || !$this->newSelectedProduct) {
            return;
        }

        // Prepare pivot data
        $pivotData = [
            'unit_price' => $this->newProductPrice,
            'unit_type' => $this->newProductUnitType,
            'end_date' => $this->newProductEndDate,
            'default_qty' => $this->newProductDefaultQty,
        ];

        // Implement versioning: expire any existing active agreements for this product
        $existingActiveAgreements = $this->selectedCustomer->products()
            ->wherePivot('product_id', $this->newSelectedProduct->id)
            ->where(function($query) {
                $query->whereNull('customer_products.end_date')
                      ->orWhere('customer_products.end_date', '>', now()->toDateString());
            })
            ->get();

        // Set end_date on all existing active agreements to yesterday
        foreach ($existingActiveAgreements as $agreement) {
            $this->selectedCustomer->products()->updateExistingPivot($agreement->id, [
                'end_date' => now()->subDay()->toDateString()
            ]);
        }

        // Add the new active price agreement
        $this->selectedCustomer->products()->attach($this->newSelectedProduct->id, $pivotData);

        // Reset fields and close modal
        $this->showAddProductModal = false;
        $this->addProductSearch = '';
        $this->searchResults = [];
        $this->newSelectedProduct = null;
        $this->newProductPrice = null;
        $this->newProductUnitType = 'TON';
        $this->newProductEndDate = null;
        $this->newProductDefaultQty = null;

        // Reload customer products
        $this->loadCustomerProducts();
    }

    /**
     * Close the product mismatch modal and reset selections
     */
    public function closeProductMismatchModal()
    {
        $this->showProductMismatchModal = false;
        $this->selectedPurchase = null;
        $this->mismatchMessage = null;
    }

    /**
     * Refresh the demands list
     */
    public function refreshDemands()
    {
        $this->demands = Demand::with('customer', 'product')
            ->orderBy('delivery_date', 'asc')
            ->get();
    }

    public function render()
    {
        return view('livewire.vraag');
    }
}
