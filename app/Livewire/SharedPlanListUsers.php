<?php

namespace App\Livewire;

use App\Models\SharedPlanList;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class SharedPlanListUsers extends Component
{
    public $planlistid;
    public $planlist;
    public $showModal = false;
    public $shareWithUsers;
    public $shareWith;

    protected $listeners = ['refreshPlanlist' => '$refresh'];

    public function mount()
    {
        $this->loadPlanList();
    }

    public function loadPlanList()
    {
        $this->planlist = SharedPlanList::find($this->planlistid);
        $this->shareWithUsers = User::whereNotIn('id', $this->planlist->users->pluck('id'))
            ->where('id', '<>', Auth::id())
            ->get();
    }

    public function removeUser($userId)
    {
        $this->planlist->users()->detach($userId);
        // TODO: Remove user from any task and assign it to creator
        $this->dispatch('refreshPlanlist');
    }

    public function addUser()
    {
        $this->planlist->users()->attach($this->shareWith);
        $this->shareWith = null;
        $this->showModal = false;
        $this->dispatch('refreshPlanlist');
    }

    public function render()
    {
        return view('livewire.shared-plan-list-users');
    }
}
