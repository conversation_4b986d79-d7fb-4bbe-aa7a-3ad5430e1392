<?php

namespace App\Livewire;

use App\Models\Customer;
use App\Models\Supplier;
use App\Models\Prospect;
use App\Models\ProductGroup;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class AddCustomer extends Component
{
    public $showModal = false;
    public $name;
    public $contactPerson;
    public $telephone;
    public $street;
    public $housenumber;
    public $postalCode;
    public $city;
    public $exactId;
    public $allProductGroups;
    public $selectedProductGroups = [];
    public $errorMessage;

    public function mount()
    {
        $this->allProductGroups = ProductGroup::all();
    }

    protected $rules = [
        'name' => 'required|string|max:255',
        'contactPerson' => 'required|string|max:255',
        'telephone' => 'required|string|max:50',
        'street' => 'required|string|max:255',
        'housenumber' => 'required|string|max:10',
        'postalCode' => 'required|string|max:10',
        'city' => 'required|string|max:255',
        'exactId' => 'required|string|max:255'
    ];

    public function saveCustomer()
    {
        $this->validate();

        $existingRecord = Customer::where('postal_code', $this->postalCode)
            ->where('housenumber', $this->housenumber)
            ->orWhere(function ($query) {
                $query->where('postal_code', $this->postalCode)
                    ->where('housenumber', $this->housenumber);
            })->exists();

        $existingRecordSupplier = Supplier::where('postal_code', $this->postalCode)
            ->where('housenumber', $this->housenumber)
            ->orWhere(function ($query) {
                $query->where('postal_code', $this->postalCode)
                    ->where('housenumber', $this->housenumber);
            })->exists();

        $existingRecordProspect = Prospect::where('postal_code', $this->postalCode)
            ->where('housenumber', $this->housenumber)
            ->orWhere(function ($query) {
                $query->where('postal_code', $this->postalCode)
                    ->where('housenumber', $this->housenumber);
            })->exists();

        if ($existingRecord || $existingRecordSupplier || $existingRecordProspect) {
            $this->errorMessage = 'Er bestaat al een relatie met dezelfde postcode en huisnummer.';
            return;
        }

        $customer = new Customer();
        $customer->company_name = $this->name;
        $customer->name = $this->contactPerson;
        $customer->street_name = $this->street;
        $customer->housenumber = $this->housenumber;
        $customer->postal_code = $this->postalCode;
        $customer->city = $this->city;
        $customer->country = 'NL';
        $customer->telephone = $this->telephone;
        $customer->owned_by = Auth::id();
        $customer->exact_id = $this->exactId;
        $customer->save();

        $customer->productGroups()->attach($this->selectedProductGroups);

        return redirect()->route('customer.edit', ['customer' => $customer->id]);
    }

    public function render()
    {
        return view('livewire.add-customer');
    }
}
