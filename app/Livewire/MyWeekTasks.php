<?php

namespace App\Livewire;

use App\Models\Action;
use App\Models\OwnerNotification;
use App\Models\User;
use Livewire\Component;
use App\Models\Task;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class MyWeekTasks extends Component
{
    public $actions;
    public $isModalOpen = false;
    public $showNewActionModal = false;
    public $modalTaskId;
    public $modalTask;
    public $description;
    public $dueDate;
    public $selectedEndDate;
    public $users;
    public $ownerId;
    public $types = ['nvt', 'telefoon', 'email', 'bezoek'];
    public $type;
    public $assignedTo;
    public $newDueDate;
    public $newDescription;

    protected $listeners = [
        'refresh' => '$refresh',
        'savedDueDate' => 'loadTasks'
    ];

    public function mount()
    {
        $this->users = User::all();
        $this->loadTasks();
    }

    public function updatedSelectedEndDate()
    {
        $this->loadTasks();
    }

    public function showModal(Action $action)
    {
        $this->isModalOpen = true;
        $this->ownerId = $action->assigned_to_user_id;
        $this->modalTaskId = $action->id;
        $this->modalTask = $action;
        $this->description = $action->description;
        $this->dueDate = $action->due_date;
    }

    public function loadTasks()
    {
        $startOfWeek = Carbon::now()->startOfWeek();
        $endOfWeek = $this->selectedEndDate ?? Carbon::now()->endOfWeek();

        $this->actions = Action::with('actionable')
            ->where('assigned_to_user_id', Auth::id())
            ->whereBetween('due_date', [$startOfWeek, $endOfWeek])
            ->orderBy('due_date', 'asc')
            ->get();
    }

    public function updatedDescription($value)
    {
        if (strpos($value, '@@') !== false) {
            $this->description = str_replace('@@', now()->format('d-m-y'), $value);
        }
    }

    public function addTaskNote()
    {
        $this->modalTask->description = $this->description;
        $this->modalTask->due_date = $this->dueDate;
        $this->modalTask->assigned_to_user_id = $this->ownerId;
        $this->modalTask->save();
        $this->isModalOpen = false;
        $this->dispatch('refresh');
    }

    public function deleteTask()
    {
        $this->modalTask->delete();
        $this->isModalOpen = false;
        $this->dispatch('refresh');
    }

    public function completeTask()
    {
        if ($this->modalTask->created_by_user_id != $this->modalTask->assigned_to_user_id) {
            OwnerNotification::create([
                'owner_id' => $this->modalTask->created_by_user_id,
                'editor_id' => $this->modalTask->assigned_to_user_id,
                'notifiable_id' => $this->modalTask->actionable->id,
                'notifiable_type' => $this->modalTask->actionable_type,
                'action' => 'edited',
                'details' => Auth::user()->name . ' heeft een taak afgerond: ' . $this->modalTask->due_date . ' - ' . $this->modalTask->description,
            ]);
        }

        $this->modalTask->delete();
        $this->isModalOpen = false;
        $this->loadTasks();
    }

    public function saveNewAction()
    {
        $action = Action::create([
            'created_by_user_id' => Auth::id(),
            'assigned_to_user_id' => $this->assignedTo ?: Auth::id(),
            'due_date' => $this->newDueDate,
            'type' => $this->type ?: 'nvt',
            'description' => $this->newDescription
        ]);

        $this->assignedTo = Auth::id();
        $this->newDescription = NULL;
        $this->newDueDate = NULL;
        $this->type = NULL;

        $this->showNewActionModal = false;

        $this->loadTasks();
    }

    public function render()
    {
        return view('livewire.my-week-tasks');
    }
}
