<?php

namespace App\Livewire;

use App\Models\Prospect;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Yajra\DataTables\Facades\DataTables;

class ProspectTable extends Component
{
    public $myProspects;

    public function render()
    {
        return view('livewire.prospect-table');
    }

    public function mount()
    {
        $this->myCustomers = session('myCustomers', false);
    }

    public function updatedMyProspects()
    {
        session(['myProspects' => $this->myProspects]);
        $this->dispatch('refresh');
    }

    public function getProspects()
    {
        $query = Prospect::query()
            ->leftJoin('product_groupables', function($join) {
                $join->on('prospects.id', '=', 'product_groupables.product_groupable_id')
                    ->where('product_groupables.product_groupable_type', '=', 'App\Models\Prospect');
            })
            ->leftJoin('product_groups', 'product_groupables.product_group_id', '=', 'product_groups.id')
            ->select([
                'prospects.*',
                DB::raw('GROUP_CONCAT(product_groups.name) as product_groups')
            ])
            ->groupBy('prospects.id');

        if (session('myProspects', false)) {
            $query->where('prospects.owned_by', Auth::id());
        }

        return DataTables::of($query)
            ->filterColumn('product_groups', function($query, $keyword) {
                $query->whereRaw("product_groups.name like ?", ["%$keyword%"]);
            })
            ->addColumn('product_groups', function($row) {
                return $row->product_groups; // Your logic to return the product groups associated with each customer
            })
            ->make(true);
    }
}
