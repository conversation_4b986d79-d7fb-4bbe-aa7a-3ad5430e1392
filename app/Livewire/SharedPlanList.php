<?php

namespace App\Livewire;

use App\Models\SharedPlanList as SharedPlanListModel;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class SharedPlanList extends Component
{
    public $showModal = false;
    public $name;
    public $myPlanLists;
    public $sharedWithMePlanLists;

    protected $listeners = ['planListSaved' => 'loadPlanLists'];

    public function mount()
    {
        $this->loadPlanLists();
    }

    public function loadPlanLists()
    {
        $this->myPlanLists = SharedPlanListModel::with('users')
            ->where('created_by_user_id', Auth::id())
            ->get();
        $this->sharedWithMePlanLists = SharedPlanListModel::with(['creator', 'users'])
            ->whereHas('users', function ($query) {
                $query->where('user_id', Auth::id());
            })
            ->get();
    }

    public function savePlanList()
    {
        $sharedPlanList = new SharedPlanListModel();
        $sharedPlanList->created_by_user_id = Auth::id();
        $sharedPlanList->name = $this->name;
        $sharedPlanList->save();

        $this->showModal = false;
        $this->name = '';
        $this->dispatch('planListSaved');
    }

    public function render()
    {
        return view('livewire.shared-plan-list');
    }
}
