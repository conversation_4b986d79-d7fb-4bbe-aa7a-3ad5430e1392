<?php

namespace App\Livewire;

use App\Models\Customer;
use App\Models\Prospect;
use App\Models\Supplier;
use Livewire\Component;

class GlobalSearch extends Component
{
    public $isModalOpen = false;
    public $searchTerm = '';
    public $results = [];

    public function updatedSearchTerm()
    {
        $this->results = collect();

        if (!empty($this->searchTerm)) {
            $this->results = $this->results
                ->concat(
                    Customer::where(function ($query) {
                        $query->where('name', 'like', '%' . $this->searchTerm . '%')
                            ->orWhere('company_name', 'like', '%' . $this->searchTerm . '%');
                    })->get()
                )
                ->concat(
                    Supplier::where(function ($query) {
                        $query->where('name', 'like', '%' . $this->searchTerm . '%')
                            ->orWhere('company_name', 'like', '%' . $this->searchTerm . '%');
                    })->get()
                )
                ->concat(
                    Prospect::where(function ($query) {
                        $query->where('name', 'like', '%' . $this->searchTerm . '%')
                            ->orWhere('company_name', 'like', '%' . $this->searchTerm . '%');
                    })->get()
                );
        }
    }

    public function render()
    {
        return view('livewire.global-search');
    }
}
