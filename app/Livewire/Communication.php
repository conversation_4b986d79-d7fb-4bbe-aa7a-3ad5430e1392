<?php

namespace App\Livewire;

use App\Models\Customer;
use App\Models\Interaction;
use App\Models\OwnerNotification;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class Communication extends Component
{
    public $interactable;
    public $startDate;
    public $endDate;
    public $showModal = false;
    public $interactionType;
    public $interactionNote;
    public $users;
    public $ownerId;
    public $notify = false;

    public function mount($interactable)
    {
        $this->interactable = $interactable;
        $this->startDate = now()->subMonth()->toDateString();
        $this->endDate = now()->toDateString();
        $this->ownerId = $interactable->owned_by;
        if (request()->input('taskcompleted') == '1') {
            $this->showModal = true;
        }
        $this->users = User::all();
    }

    public function save()
    {
        $this->validate([
            'interactionType' => 'required',
            'interactionNote' => 'required',
        ]);


        $this->interactable->interactions()->create([
            'type' => $this->interactionType,
            'note' => $this->interactionNote,
            'user_id' => Auth::id()
        ]);

        if ($this->notify) {
            OwnerNotification::create([
                'owner_id' => $this->ownerId,
                'editor_id' => auth()->id(),
                'notifiable_id' => $this->interactable->id,
                'notifiable_type' => get_class($this->interactable),
                'action' => 'edited',
                'details' => Auth::user()->name . ' heeft communicatie toegevoegd: ' . $this->interactionNote,
            ]);
        }

        $this->showModal = false;

        $this->interactionType = '';
        $this->interactionNote = '';
        $this->ownerId = $this->interactable->owned_by;
        $this->notify = false;

        $this->dispatch('refreshContactPersons');
    }

    public function removeInteraction($entityId)
    {
        $interaction = Interaction::find($entityId);
        $interaction->delete();
    }

    public function getInteractionsProperty()
    {
        if (!$this->startDate || !$this->endDate) {
            return;
        }
        $startDate = $this->startDate ? Carbon::parse($this->startDate) : null;
        $endDate = $this->endDate ? Carbon::parse($this->endDate)->endOfDay() : null;

        return $this->interactable->interactions()
            ->when($startDate, function ($query, $startDate) {
                return $query->where('created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query, $endDate) {
                return $query->where('created_at', '<=', $endDate);
            })
            ->latest()->get();
    }

    public function render()
    {
        return view('livewire.communication');
    }
}
