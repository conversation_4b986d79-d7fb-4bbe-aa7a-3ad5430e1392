<?php

namespace App\Livewire;

use App\Models\Supplier;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class SupplierTable extends Component
{
    public $mySuppliers;

    public function render()
    {
        return view('livewire.supplier-table');
    }

    public function mount()
    {
        $this->mySuppliers = session('mySuppliers', false);
    }

    public function updatedMySuppliers()
    {
        session(['mySuppliers' => $this->mySuppliers]);
        $this->dispatch('refresh');
    }

    public function getSuppliers()
    {
        $query = Supplier::query()
            ->leftJoin('product_groupables', function($join) {
                $join->on('suppliers.id', '=', 'product_groupables.product_groupable_id')
                    ->where('product_groupables.product_groupable_type', '=', 'App\Models\Supplier');
            })
            ->leftJoin('product_groups', 'product_groupables.product_group_id', '=', 'product_groups.id')
            ->select([
                'suppliers.*',
                DB::raw('GROUP_CONCAT(product_groups.name) as product_groups')
            ])
            ->groupBy('suppliers.id');

        if (session('mySuppliers', false)) {
            $query->where('suppliers.owned_by', Auth::id());
        }

        return DataTables::of($query)
            ->filterColumn('product_groups', function($query, $keyword) {
                $query->whereRaw("product_groups.name like ?", ["%$keyword%"]);
            })
            ->addColumn('product_groups', function($row) {
                return $row->product_groups; // Your logic to return the product groups associated with each customer
            })
            ->make(true);
    }
}
