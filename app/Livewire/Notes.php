<?php

namespace App\Livewire;

use App\Models\Note;
use App\Models\OwnerNotification;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class Notes extends Component
{
    public $noteable;
    public $content;
    public $showModal = false;
    public $users;
    public $ownerId;
    public $notify = false;

    protected $listeners = ['saved' => '$refresh'];

    public function mount()
    {
        $this->users = User::all();
        $this->ownerId = $this->noteable->user_id;
    }

    public function save()
    {
        $this->validate([
            'content' => 'required',
        ]);

        $this->noteable->notes()->create([
            'content' => $this->content,
            'user_id' => Auth::id(),
        ]);

        if ($this->notify) {
            OwnerNotification::create([
                'owner_id' => $this->ownerId,
                'editor_id' => auth()->id(),
                'notifiable_id' => $this->noteable->id,
                'notifiable_type' => get_class($this->noteable),
                'action' => 'edited',
                'details' => Auth::user()->name . ' heeft notitie toegevoegd: ' . $this->content,
            ]);
        }

        $this->showModal = false;

        $this->content = '';

        $this->dispatch('saved');
        $this->resetErrorBag();
    }

    public function removeNote(Note $note)
    {
        $note->delete();
        $this->dispatch('saved');
    }

    public function render()
    {
        return view('livewire.notes');
    }
}
