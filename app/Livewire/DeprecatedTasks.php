<?php

namespace App\Livewire;

use App\Models\OwnerNotification;
use App\Models\Task;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class DeprecatedTasks extends Component
{
    public $tasks;
    public $isModalOpen = false;
    public $modalTaskId;
    public $modalTask;
    public $description;
    public $dueDate;
    public $selectedEndDate;
    public $users;
    public $ownerId;

    protected $listeners = [
        'refresh' => '$refresh',
        'savedDueDate' => 'loadTasks'
    ];

    public function mount()
    {
        $this->users = User::all();
        $this->loadTasks();
    }

    public function updatedSelectedEndDate()
    {
        $this->loadTasks();
    }

    public function showModal(Task $task)
    {
        $this->isModalOpen = true;
        $this->ownerId = $task->assigned_to_user_id;
        $this->modalTaskId = $task->id;
        $this->modalTask = $task;
        $this->description = $task->description;
        $this->dueDate = $task->due_date;
    }

    public function loadTasks()
    {
        $startOfWeek = Carbon::now()->startOfWeek();
        $endOfWeek = $this->selectedEndDate ?? Carbon::now()->endOfWeek();

        $this->tasks = Task::with('taskable')
            ->where('assigned_to_user_id', Auth::id())
            ->where('due_date', '<=', $endOfWeek)
            ->orderBy('due_date', 'asc')
            ->get();
    }

    public function updatedDescription($value)
    {
        if (strpos($value, '@@') !== false) {
            $this->description = str_replace('@@', now()->format('d-m-y'), $value);
        }
    }

    public function addTaskNote()
    {
        $this->modalTask->description = $this->description;
        $this->modalTask->due_date = $this->dueDate;
        $this->modalTask->assigned_to_user_id = $this->ownerId;
        $this->modalTask->save();
        $this->isModalOpen = false;
        $this->dispatch('refresh');
    }

    public function deleteTask()
    {
        $this->modalTask->delete();
        $this->isModalOpen = false;
        $this->dispatch('refresh');
    }

    public function completeTask()
    {
        if ($this->modalTask->created_by_user_id != $this->modalTask->assigned_to_user_id) {
            OwnerNotification::create([
                'owner_id' => $this->modalTask->created_by_user_id,
                'editor_id' => $this->modalTask->assigned_to_user_id,
                'notifiable_id' => $this->modalTask->actionable->id,
                'notifiable_type' => $this->modalTask->actionable_type,
                'action' => 'edited',
                'details' => Auth::user()->name . ' heeft een taak afgerond: ' . $this->modalTask->due_date . ' - ' . $this->modalTask->description,
            ]);
        }

        $this->modalTask->delete();
        $this->isModalOpen = false;
    }

    public function render()
    {
        return view('livewire.deprecated-tasks');
    }
}
