<?php

namespace App\Livewire;

use App\Models\Action;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class MobileActions extends Component
{
    public $actionable;
    public $model;
    public $modelId;
    public $showModal = false;
    public $users;
    public $types = ['nvt', 'telefoon', 'email', 'bezoek'];

    public $selectedActionId;
    public $type = 'nvt';
    public $assignedTo;
    public $dueDate;
    public $description;

    public function mount()
    {
        $this->users = User::all();
    }

    public function updatedDescription($value): void
    {
        if (strpos($value, '@@') !== false) {
            $this->description = str_replace('@@', '[' . now()->format('d-m-Y') . '] ', $value);
        }
    }

    /**
     * Save the action into the database and reset the input values.
     *
     * @return void
     */
    public function saveOrUpdateAction(): void
    {
        $modelClass = 'App\\Models\\' . ucfirst($this->model);

        $this->actionable->actions()->updateOrCreate(
            [
                'id' => $this->selectedActionId
            ],
            [
                'actionable_type' => $modelClass,
                'created_by_user_id' => Auth::id(),
                'assigned_to_user_id' => $this->assignedTo ?: Auth::id(),
                'due_date' => $this->dueDate,
                'type' => $this->type,
                'description' => $this->description
            ]);

        $this->resetFields();

        $this->showModal = false;
    }

    public function cancelModal()
    {
        $this->resetFields();
        $this->showModal = false;
    }

    public function completeAction()
    {
        $action = Action::find($this->selectedActionId);
        $action->description = $this->description;
        $action->done = 1;
        $action->save();

        $this->resetFields();
        $this->showModal = false;
    }

    public function showEditModal($action)
    {
        $this->selectedActionId = $action['id'];
        $this->type = $action['type'];
        $this->assignedTo = $action['assigned_to_user_id'];
        $this->dueDate = $action['due_date'];
        $this->description = $action['description'];
        $this->showModal = true;
    }

    protected function resetFields(): void
    {
        $this->selectedActionId = null;
        $this->type = null;
        $this->assignedTo = null;
        $this->dueDate = null;
        $this->description = null;
    }

    public function render()
    {
        return view('livewire.mobile-actions');
    }
}
