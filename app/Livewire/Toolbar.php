<?php

namespace App\Livewire;

use Livewire\Component;
use Carbon\Carbon;

class Toolbar extends Component
{
    public $until = false;
    public $showDate;
    public $administration;

    public function mount()
    {
        $this->showDate = now()->format('Y-m-d');
        $this->until = true;
    }

    public function nextDay()
    {

            $this->showDate = Carbon::parse($this->showDate)->addDay()->format('Y-m-d');
            $this->dispatch('show', $this->until, $this->showDate, $this->administration);

    }

    public function previousDay()
    {

            $this->showDate = Carbon::parse($this->showDate)->subDay()->format('Y-m-d');
            $this->dispatch('show', $this->until, $this->showDate, $this->administration);

    }

    public function updatedShowDate()
    {
        $this->dispatch('show', $this->until, $this->showDate, $this->administration);
    }

    public function updatedUntil()
    {
        $this->dispatch('show', $this->until, $this->showDate, $this->administration);
    }

    public function updatedAdministration()
    {
        $this->dispatch('show', $this->until, $this->showDate, $this->administration);
    }

    public function show()
    {
        $this->dispatch('show', $this->until, $this->showDate, $this->administration);
    }

    public function showAll()
    {
        $this->until = false;
        $this->showDate = null;
        $this->administration = null;
        $this->dispatch('show', false, null, null);
    }

    public function render()
    {
        return view('livewire.toolbar');
    }
}
