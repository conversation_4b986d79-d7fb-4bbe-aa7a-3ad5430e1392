<?php

namespace App\Livewire;

use App\Models\ContactPerson;
use Livewire\Component;

class ContactPersons extends Component
{
    public $contactable;
    public $showModal = false;
    public $showEditModal = false;
    public $showRemoveModal = false;
    public $name;
    public $editName;
    public $email;
    public $editEmail;
    public $phoneNumber;
    public $editPhoneNumber;
    public $mobileNumber;
    public $editMobileNumber;
    public $note;
    public $editNote;
    public $editContactPerson;

    protected $listeners = ['refreshContactPersons' => '$refresh'];

    public function save()
    {
        $this->validate([
            'name' => 'required',
            'email' => 'required|email',
            'phoneNumber' => 'nullable',
            'mobileNumber' => 'nullable',
        ]);

        $this->contactable->contactPersons()->create([
            'name' => $this->name,
            'email' => $this->email,
            'phone_number' => $this->phoneNumber,
            'mobile_phone_number' => $this->mobileNumber,
            'note' => $this->note,
        ]);

        $this->showModal = false;

        $this->name = '';
        $this->email = '';
        $this->phoneNumber = '';
        $this->mobileNumber = '';
        $this->note = '';

        $this->dispatch('refreshContactPersons');
        $this->resetErrorBag();
    }

    public function edit(ContactPerson $contactPerson)
    {
        $this->editContactPerson = $contactPerson;
        $this->editName = $contactPerson->name;
        $this->editEmail = $contactPerson->email;
        $this->editPhoneNumber = $contactPerson->phone_number;
        $this->editMobileNumber = $contactPerson->mobile_phone_number;
        $this->editNote = $contactPerson->note;
        $this->showEditModal = true;
    }

    public function update()
    {
        $this->editContactPerson->name = $this->editName;
        $this->editContactPerson->email = $this->editEmail;
        $this->editContactPerson->phone_number = $this->editPhoneNumber;
        $this->editContactPerson->mobile_phone_number = $this->editMobileNumber;
        $this->editContactPerson->note = $this->editNote;
        $this->editContactPerson->save();
        $this->showEditModal = false;
        $this->dispatch('refreshContactPersons');
    }

    public function toggleDeleteModal()
    {
        $this->showEditModal = false;
        $this->showRemoveModal = true;
    }

    public function removeContact()
    {
        $this->editContactPerson->delete();
        $this->showRemoveModal = false;
        $this->dispatch('refreshContactPersons');
    }

    public function render()
    {
        return view('livewire.contact-persons');
    }
}
