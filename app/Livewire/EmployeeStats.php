<?php

namespace App\Livewire;

use App\Models\Customer;
use App\Models\Interaction;
use App\Models\Task;
use App\Models\User;
use Carbon\Carbon;
use Livewire\Component;

class EmployeeStats extends Component
{
    public $userId;
    public $startDate;
    public $endDate;
    public $showStartDate;
    public $showEndDate;
    public $users;
    public $completedTasks = 0;
    public $lateTasks = 0;
    public $communications = 0;
    public $customers = 0;

    public function mount()
    {
        $this->users = User::all();
    }

    public function showStats()
    {
        $this->showStartDate = $this->startDate;
        $this->showEndDate = $this->endDate;
        $this->completedTasks = Task::onlyTrashed()
            ->where('assigned_to_user_id', $this->userId)
            ->whereBetween('deleted_at', [$this->startDate, $this->endDate])
            ->count();
        $startOfWeek = Carbon::now()->startOfWeek();

        $this->lateTasks = Task::where('assigned_to_user_id', $this->userId)
            ->where('due_date', '<', $startOfWeek)
            ->count();

        $this->communications = Interaction::where('user_id', $this->userId)
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->count();

        $this->customers = Customer::where('owned_by', $this->userId)->count();
    }

    public function render()
    {
        return view('livewire.employee-stats');
    }
}
