<?php

namespace App\Livewire;

use App\Models\Product;
use App\Models\Purchase;
use App\Models\Supplier;
use App\Models\SupplierProductPrice;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class CreatePurchase extends Component
{
    public $search = '';
    public $productSearch = '';
    public $showModal = false;
    public $suppliers;
    public $selectedSupplier = null;
    public $loadDate;
    public $purchaseQty;
    public $purchasePrice;
    public $purchaseQtyType = 'TON';
    public $selectedProduct = null;
    public $products;
    public $errorMessage;
    public $productSelected = false;
    public $days = 0;
    public $times = 0;
    public $priceAgreement;
    public $message;
    public $administration;
    public $errorAdmin;

    public function mount()
    {
        $this->suppliers = new Collection();
        $this->products = new Collection();
    }

    public function updatedSearch()
    {
        $this->suppliers = Supplier::where('company_name', 'like', '%'.$this->search.'%')->get();
    }

    public function updatedPurchaseQty()
    {
        $priceAgreement = SupplierProductPrice::where('product_id', $this->selectedProduct->id)
            ->where('supplier_id', $this->selectedSupplier->id)
            ->where('min_qty', '<=', $this->purchaseQty)
            ->where('max_qty', '>=', $this->purchaseQty)
            ->orderBy('min_qty', 'desc')
            ->first();
        if ($priceAgreement) {
            $this->purchasePrice = $priceAgreement->price;
            $this->message = 'Prijsafspraak gevonden: ' . $priceAgreement->min_qty . ' - ' . $priceAgreement->max_qty . ' €' . $priceAgreement->price;
        }
    }

    public function updatedProductSearch()
    {
        $this->products = Product::where('name', 'like', '%'.$this->productSearch.'%')->get();
    }

    public function updatedAdministration()
    {
        $this->errorAdmin = '';
    }

    public function selectProduct(Product $product)
    {
        $this->selectedProduct = $product;

        // Set defaults if both supplier and product are selected
        if ($this->selectedSupplier && $this->selectedProduct) {
            $this->setProductDefaults();
        }
    }

    public function selectSupplier(Supplier $supplier)
    {
        $this->selectedSupplier = $supplier;

        $this->search = '';
        $this->suppliers = new Collection();

        // Set defaults if both supplier and product are selected
        if ($this->selectedSupplier && $this->selectedProduct) {
            $this->setProductDefaults();
        }
    }

    private function setProductDefaults()
    {
        $pivotData = $this->selectedSupplier->activeProducts()
            ->where('product_id', $this->selectedProduct->id)
            ->first();

        if ($pivotData && isset($pivotData->pivot)) {
            // Set price if available
            if (isset($pivotData->pivot->unit_price)) {
                $this->purchasePrice = $pivotData->pivot->unit_price;
            }

            // Set unit type if available
            if (isset($pivotData->pivot->unit_type)) {
                $this->purchaseQtyType = $pivotData->pivot->unit_type;
            }

            // Set default quantity if available
            if (isset($pivotData->pivot->default_qty)) {
                $this->purchaseQty = $pivotData->pivot->default_qty;
            }
        }
    }

    public function addProduct()
    {
        $this->purchaseQty = str_replace(',', '.', $this->purchaseQty);
        $this->purchasePrice = str_replace(',', '.', $this->purchasePrice);
        $this->productSelected = true;
        $this->showModal= false;
    }

    public function savePurchase()
    {
        if (!$this->administration) {
            $this->errorAdmin = 'Administratie is verplicht.';
            return;
        }
        $count = 1;
        if ($this->times > 0) {
            $currentLoadDate = Carbon::parse($this->loadDate);
            $daysToAdd = (int) $this->days;

            for ($i = 1; $i <= $this->times; $i++) {
                $purchase = new Purchase();
                $purchase->supplier_id = $this->selectedSupplier->id;
                $purchase->product_id = $this->selectedProduct->id;
                $purchase->user_id = Auth::id();
                $purchase->qty_type = $this->purchaseQtyType;
                $purchase->quantity = $this->purchaseQty;
                $purchase->price = $this->purchasePrice;
                $purchase->total = round($this->purchaseQty * $this->purchasePrice, 2);
                $purchase->load_date = $currentLoadDate;
                $purchase->administration = $this->administration;

                $now = Carbon::now();
                $purchase->purchase_number = strtoupper(substr(str_replace(' ', '', $this->selectedSupplier->company_name), 0, 4)) .
                    substr($now->year, 2, 2) .
                    str_pad($purchase->id, 6, '0', STR_PAD_LEFT) .
                    $i;

                $purchase->save();

                $currentLoadDate->addDays($daysToAdd);
            }
        } else {
            $purchase = new Purchase();
            $purchase->supplier_id = $this->selectedSupplier->id;
            $purchase->product_id = $this->selectedProduct->id;
            $purchase->purchase_number = 'dummy';
            $purchase->user_id = Auth::user()->id;
            $purchase->qty_type = $this->purchaseQtyType;
            $purchase->quantity = $this->purchaseQty;
            $purchase->price = $this->purchasePrice;
            $purchase->total = round($this->purchaseQty * $this->purchasePrice,2);
            $purchase->load_date = $this->loadDate;
            $purchase->administration = $this->administration;
            $purchase->save();

            $now = Carbon::now();
            $purchase->purchase_number = strtoupper(substr(str_replace(' ', '', $this->selectedSupplier->company_name), 0, 4)) . substr($now->year, 2, 2) . str_pad($purchase->id, 6, '0', STR_PAD_LEFT). '1';
            $purchase->save();
        }

        return redirect()->route('business.index');
    }

    public function removeProduct()
    {
        $this->productSelected = false;
        $this->selectedProduct = null;
        $this->purchaseQty = 0;
        $this->purchasePrice = 0;
    }

    public function render()
    {
        return view('livewire.create-purchase');
    }
}
