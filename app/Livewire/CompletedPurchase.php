<?php

namespace App\Livewire;

use App\Models\Customer;
use App\Models\Mail;
use App\Models\Purchase;
use App\Models\Shipper;
use App\Models\ShippingAddress;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Livewire\Component;
use Illuminate\Database\Eloquent\Collection;
use Spatie\LivewireFilepond\WithFilePond;
use Symfony\Component\HttpFoundation\StreamedResponse;

class CompletedPurchase extends Component
{
    use WithFilePond;
    use \App\Traits\RefreshesLog;

    public $file;
    public $purchase;
    public $showModal = false;
    public $customers;
    public $selectedCustomer;
    public $errorMessage;
    public $search = '';
    public $saleQty;
    public $salePrice;
    public $street;
    public $housenumber;
    public $postalCode;
    public $city;
    public $transportPrice;
    public $totalQtySold;
    public $loadDate;
    public $deliveryDate;
    public $totalTransport;
    public $totalPrice;
    public $log;
    public $showShipperModal = false;
    public $shipperSearch = '';
    public $selectedShipper;
    public $shippers;
    public $showShipperMailModal = false;
    public $shipperMail;
    public $messageTextShipper;
    public $shipperMails;
    public $showCompleteModal = false;

    /**
     * Initializes the component with purchase details and calculates aggregated data.
     *
     * @param int $purchaseId The ID of the purchase to load.
     */
    public function mount($purchaseId)
    {
        $this->purchase = Purchase::find($purchaseId);
        $this->loadDate = Carbon::parse($this->purchase->load_date)->format('Y-m-d');
        $this->deliveryDate = Carbon::parse($this->purchase->delivery_date)->format('Y-m-d');
        $this->log = $this->purchase->log;
        $this->selectedShipper = $this->purchase->shipper;
        if ($this->selectedShipper) {
            $this->shipperMail = $this->selectedShipper->email_address;
        }

        foreach ($this->purchase->sales as $sale) {
            // Use actual quantity if available, otherwise use the original quantity
            $quantity = $sale->actual_quantity ?: $sale->quantity;
            $this->totalQtySold += $quantity;
            $this->totalPrice += $quantity * $sale->price;
            $this->totalTransport += $sale->transport_cost;
        }
        $this->customers = new Collection();
        $this->shippers = new Collection();
        $this->loadShipperMails();
    }

    /**
     * Updates the list of customers based on the search query.
     */
    public function updatedSearch()
    {
        $this->customers = Customer::where('company_name', 'like', '%'.$this->search.'%')->get();
    }

    public function updatedShipperSearch()
    {
        $this->shippers = Shipper::where('company_name', 'like', '%'.$this->shipperSearch.'%')->get();
    }

    public function updatedDeliveryDate()
    {
        $this->purchase->delivery_date = $this->deliveryDate;
        $this->savePurchaseAndRefreshLog();
    }

    public function updatedLoadDate()
    {
        $this->purchase->load_date = $this->loadDate;
        $this->savePurchaseAndRefreshLog();
    }

    public function selectCustomer(Customer $customer)
    {
        $this->selectedCustomer = $customer;
        $this->street = $customer->street_name;
        $this->housenumber = $customer->housenumber;
        $this->postalCode = $customer->postal_code;
        $this->city = $customer->city;
    }

    public function selectShipper(Shipper $shipper)
    {
        $this->selectedShipper = $shipper;
        $this->shipperMail = $shipper->email_address;
        $this->showShipperModal = false;
        $this->log .= '<li>' . Carbon::now()->format('d-m-Y H:i') . ' - ' . auth()->user()->name .  ': Transporteur geselecteerd';
        $this->purchase->log = $this->log;
        $this->purchase->shipper_id = $shipper->id;
        $this->purchase->save();
    }

    /**
     * @return StreamedResponse
     */
    public function sendShipperMail()
    {
        $mail = Mail::create([
            'purchase_id' => $this->purchase->id,
            'mail_to' => $this->shipperMail,
            'mail_message' => $this->messageTextShipper
        ]);

        // Prepare data for CMR template
        $cmrData = $this->prepareCmrData();

        // Generate CMR PDF with purchase data and fixed A4 settings
        $pdf = PDF::loadView('cmr-template', $cmrData);
        $pdf->setPaper([0, 0, 595.28, 841.89], 'portrait'); // A4 in points
        $pdf->setOptions([
            'dpi' => 96,
            'defaultFont' => 'Arial',
            'isRemoteEnabled' => false,
            'isHtml5ParserEnabled' => false,
            'isPhpEnabled' => false,
            'fontSubsetting' => true,
            'debugKeepTemp' => false
        ]);

        $this->showShipperMailModal = false;
        $this->loadShipperMails();

        $cmrFilename = 'CMR-' . $this->purchase->purchase_number . '-' . date('Y-m-d') . '.pdf';

        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->stream();
        }, $cmrFilename);
    }

    /**
     * Prepare data for CMR template
     *
     * @return array
     */
    private function prepareCmrData(): array
    {
        // Get supplier address
        $supplierAddress = [
            'street' => $this->purchase->custom_supplier_address ? $this->purchase->supplier_street : $this->purchase->supplier->street_name,
            'housenumber' => $this->purchase->custom_supplier_address ? $this->purchase->supplier_housenumber : $this->purchase->supplier->housenumber,
            'postal_code' => $this->purchase->custom_supplier_address ? $this->purchase->supplier_postal_code : $this->purchase->supplier->postal_code,
            'city' => $this->purchase->custom_supplier_address ? $this->purchase->supplier_city : $this->purchase->supplier->city,
            'country' => $this->purchase->custom_supplier_address ? $this->purchase->supplier_country : ($this->purchase->supplier->country ?: 'NL')
        ];

        // Get first delivery address for CMR
        $firstSale = $this->purchase->sales->first();
        $deliveryAddress = null;
        if ($firstSale && $firstSale->shippingAddress) {
            $deliveryAddress = [
                'company_name' => $firstSale->customer->company_name,
                'street' => $firstSale->shippingAddress->street,
                'housenumber' => $firstSale->shippingAddress->housenumber,
                'postal_code' => $firstSale->shippingAddress->postal_code,
                'city' => $firstSale->shippingAddress->city,
                'country' => $firstSale->shippingAddress->country ?: 'NL'
            ];
        }

        // Calculate total quantities and weights
        $totalQuantity = $this->purchase->sales->sum('quantity');
        $totalWeight = $this->purchase->load_weight ?: $this->purchase->sales->sum('quantity');

        return [
            'purchase' => $this->purchase,
            'supplierAddress' => $supplierAddress,
            'deliveryAddress' => $deliveryAddress,
            'totalQuantity' => $totalQuantity,
            'totalWeight' => $totalWeight,
            'loadDate' => $this->purchase->load_date ? \Carbon\Carbon::parse($this->purchase->load_date) : now(),
            'deliveryDate' => $this->purchase->delivery_date ? \Carbon\Carbon::parse($this->purchase->delivery_date) : null,
            'shipper' => $this->purchase->shipper,
            'product' => $this->purchase->product,
            'sales' => $this->purchase->sales,
            'generatedDate' => now(),
            'purchaseNumber' => $this->purchase->purchase_number
        ];
    }

    /**
     * Reload shipper mails to populate communication tab
     *
     * @return void
     */
    public function loadShipperMails(): void
    {
        $this->shipperMails = Mail::where('purchase_id', $this->purchase->id)->get();
    }

    /**
     * Add a sale to the purchase
     *
     * @return void
     */
    public function addSale(): void
    {
        if (!$this->selectedCustomer) {
            $this->errorMessage = 'Een klant is verplicht.';

            return;
        }

        if (!$this->saleQty || !$this->salePrice || !$this->transportPrice) {
            $this->errorMessage = 'Aantal, Prijs en Transport Prijs zijn allemaal verplicht.';

            return;
        }

        $shippingAddress = ShippingAddress::create([
            'company_name' =>$this->selectedCustomer->company_name,
            'customer_id' => $this->selectedCustomer->id,
            'name' => $this->selectedCustomer->name,
            'street' => $this->street,
            'housenumber' => $this->housenumber,
            'postal_code' => $this->postalCode,
            'city' => $this->city,
            'country' => 'NL',
        ]);

        $sale = $this->purchase->sales()->create([
            'customer_id' => $this->selectedCustomer->id,
            'product_id' => $this->purchase->product_id,
            'price' => $this->salePrice,
            'total_price' => $this->salePrice * $this->saleQty,
            'quantity' => $this->saleQty,
            'sale_qty_type' => $this->purchase->qty_type,
            'user_id' => auth()->id(),
            'shipping_address_id' => $shippingAddress->id,
            'transport_cost' => $this->transportPrice
        ]);

        $this->log .= '<li>' . Carbon::now()->format('d-m-Y H:i') . ' - ' . auth()->user()->name .  ': Verkoop toegevoegd: ' . $this->selectedCustomer->name . ': ' . $this->saleQty . ' ' . $this->purchase->qty_type . ' &euro;' . $sale->total_price . '</li>';
        $this->purchase->log = $this->log;
        $this->purchase->save();
        $this->resetFields();
        $this->reloadPurchaseWithSales();
    }

    /**
     * @return void
     */
    public function reloadPurchaseWithSales(): void
    {
        $this->purchase->load('sales');
        $this->totalQtySold = 0;
        $this->totalPrice = 0;
        $this->totalTransport = 0;
        foreach ($this->purchase->sales as $sale) {
            // Use actual quantity if available, otherwise use the original quantity
            $quantity = $sale->actual_quantity ?: $sale->quantity;
            $this->totalQtySold += $quantity;
            $this->totalPrice += $quantity * $sale->price;
            $this->totalTransport += $sale->transport_cost;
        }
    }

    /**
     * @return void
     */
    public function resetFields(): void
    {
        $this->search = '';
        $this->errorMessage = '';
        $this->selectedCustomer = null;
        $this->customers = new Collection();
        $this->saleQty = NULL;
        $this->salePrice = NULL;
        $this->transportPrice = NULL;
        $this->showModal = false;
    }

    /**
     * Mark as ready for planning
     *
     * @return void
     */
    public function sendToTransport(): void
    {
        $this->purchase->ready_for_planning = true;
        $this->purchase->save();
    }

    public function createTransport()
    {
        $this->purchase->transport()->create([
            'load_date' => $this->purchase->load_date,
            'price' => 0.00
        ]);
    }

    public function complete()
    {
        foreach ($this->purchase->sales as $sale) {
            $sale->transport_cost = $this->totalTransport;
            $sale->save();
        }

        $this->purchase->ready_for_planning = false;
        $this->purchase->completed = true;
        $this->purchase->save();
        $this->showCompleteModal = false;
    }

    public function render()
    {
        return view('livewire.completed-purchase');
    }
}
