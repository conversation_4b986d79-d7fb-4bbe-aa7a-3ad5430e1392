<?php

namespace App\Livewire;

use App\Models\ContactPerson;
use App\Models\PlanTask;
use App\Models\SharedPlanList as SharedPlanListModel;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class PlanList extends Component
{
    public $tasks;
    public $contactInfoModal = false;
    public $communicationModal = false;
    public $updateTaskModal = false;
    public $confirmDelete = false;
    public $showHistoryModal = false;
    public $showEditEntityModal = false;
    public $showEditContactPerson = false;
    public $contactPersons;
    public $description;
    public $dueDate;
    public $relation;
    public $selected;
    public $updateAssignedTo;
    public $updateDescription;
    public $updateDueDate;
    public $users;
    public $lastCommunications;
    public $lastNotes;
    public $showTo;
    public $myPlanLists;
    public $moveTo;
    public $companyName;
    public $name;
    public $street;
    public $housenumber;
    public $postalCode;
    public $city;
    public $entityDescription;
    public $telephone;
    public $selectedContactPerson;
    public $contactName;
    public $contactEmail;
    public $contactTelephone;
    public $contactMobile;
    public $contactNote;

    protected $listeners = [
        'plantask_saved' => 'loadPlanList',
        'showto' => 'updateShowTo'
    ];

    public function mount()
    {
        $this->loadPlanList();
        $this->myPlanLists = SharedPlanListModel::with(['users', 'creator'])
            ->where(function ($query) {
                $query->where('created_by_user_id', Auth::id())
                    ->orWhereHas('users', function ($query) {
                        $query->where('user_id', Auth::id());
                    });
            })
            ->get();
    }

    public function updateShowTo($showTo)
    {
        $this->showTo = $showTo;
        $this->loadPlanList();
    }

    public function loadPlanList()
    {
        $toDate = $this->showTo ?? Carbon::now()->endOfWeek();
        $user = Auth::id();
        $this->tasks = PlanTask::where('assigned_to_user_id', $user)
            ->whereDate('due_date', '<=', $toDate)
            ->whereNull('shared_plan_list_id')
            ->orderBy('due_date', 'asc')
            ->get();
        $this->users = User::all();
    }

    public function openContactInfo($taskId)
    {
        $task = PlanTask::find($taskId);
        $this->relation = $task->plannable;
        $this->contactPersons = $task->plannable->contactPersons;
        $this->contactInfoModal = true;
    }

    public function editEntity()
    {
        $this->companyName = $this->relation->company_name;
        $this->name = $this->relation->name;
        $this->street = $this->relation->street_name;
        $this->housenumber = $this->relation->housenumber;
        $this->postalCode = $this->relation->postal_code;
        $this->telephone = $this->relation->telephone;
        $this->city = $this->relation->city;
        $this->entityDescription = $this->relation->description;

        $this->contactInfoModal = false;
        $this->showEditEntityModal = true;
    }

    public function updatedUpdateDescription($value)
    {
        if (strpos($value, '@@') !== false) {
            $this->updateDescription = str_replace('@@', '[' . now()->format('d-m-Y') . '] ', $value);
        }
    }

    public function editContactPerson(ContactPerson $contactPerson)
    {
        $this->selectedContactPerson = $contactPerson;
        $this->contactName = $contactPerson->name;
        $this->contactEmail = $contactPerson->email;
        $this->contactTelephone = $contactPerson->phone_number;
        $this->contactMobile = $contactPerson->mobile_phone_number;
        $this->contactNote = $contactPerson->note;

        $this->contactInfoModal = false;
        $this->showEditContactPerson = true;
    }

    public function saveContact()
    {
        $this->selectedContactPerson->name = $this->contactName;
        $this->selectedContactPerson->email = $this->contactEmail;
        $this->selectedContactPerson->phone_number = $this->contactTelephone;
        $this->selectedContactPerson->mobile_phone_number = $this->contactMobile;
        $this->selectedContactPerson->note = $this->contactNote;
        $this->selectedContactPerson->save();

        $this->showEditContactPerson = false;
    }

    public function saveEntity()
    {
        $this->relation->company_name = $this->companyName;
        $this->relation->name = $this->name;
        $this->relation->street_name = $this->street;
        $this->relation->housenumber = $this->housenumber;
        $this->relation->postal_code = $this->postalCode;
        $this->relation->city = $this->city;
        $this->relation->telephone = $this->telephone;
        $this->relation->description = $this->entityDescription;

        $this->relation->save();
        $this->showEditEntityModal = false;
    }

    public function openRegisterContact($taskId)
    {
        $this->selected = PlanTask::find($taskId);
        $this->relation = $this->selected->plannable;
        $this->communicationModal = true;
    }

    public function updatePlantask()
    {
        $this->relation->actions()->create([
            'type' => 'call',
            'description' => $this->description,
            'created_by_user_id' => Auth::id(),
            'assigned_to_user_id' => Auth::id()
        ]);
        $this->selected->due_date = $this->dueDate;
        $this->selected->save();
        $this->communicationModal = false;
        $this->loadPlanList();
    }

    public function openPlanTask($taskId)
    {
        $this->selected = PlanTask::find($taskId);

        $this->updateDescription = $this->selected->description;
        $this->updateAssignedTo = $this->selected->assigned_to_user_id;
        $this->updateDueDate = $this->selected->due_date;
        $this->updateTaskModal = true;
    }

    public function saveUpdated()
    {
        if ($this->moveTo != 0) {
            $this->selected->shared_plan_list_id = $this->moveTo;
        }

        $this->selected->description = $this->updateDescription;
        $this->selected->assigned_to_user_id = $this->updateAssignedTo;
        $this->selected->due_date = $this->updateDueDate;
        $this->selected->save();
        $this->moveTo = NULL;
        $this->loadPlanList();
        $this->updateTaskModal = false;
    }

    public function openDeleteTask($taskId)
    {
        $this->selected = PlanTask::find($taskId);
        $this->confirmDelete = true;
    }

    public function deleteTask()
    {
        $this->selected->delete();
        $this->loadPlanList();
        $this->confirmDelete = false;
    }

    public function showHistory($taskId)
    {
        $this->selected = PlanTask::find($taskId);
        $entity = $this->selected->plannable;
        $this->lastCommunications = $entity->actions()
            ->with('createdBy')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

//        $this->lastNotes = $entity->notes()
//            ->orderBy('created_at', 'desc')
//            ->take(5)
//            ->get();

        $this->showHistoryModal = true;
    }

    public function render()
    {
        return view('livewire.plan-list');
    }
}
