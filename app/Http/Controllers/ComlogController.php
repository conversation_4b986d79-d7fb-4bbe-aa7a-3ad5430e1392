<?php

namespace App\Http\Controllers;

use App\Models\Comlog;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ComlogController extends Controller
{
    /**
     * Get the data and save it first before processing
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function saveData(Request $request): JsonResponse
    {
        $comlog = new Comlog();
        $comlog->message = $request->getContent();
        $comlog->save();

        return response()->json(['success' => 'success'], 200);
    }

    public function test()
    {
        Comlog::parseComlog();
    }
}
