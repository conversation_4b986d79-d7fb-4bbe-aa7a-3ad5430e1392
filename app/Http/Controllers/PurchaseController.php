<?php

namespace App\Http\Controllers;

use App\Models\Purchase;
use Illuminate\Http\Request;

class PurchaseController extends Controller
{
    public function index()
    {
        return view('purchase-index');
    }

    public function new()
    {
        return view('purchase-new');
    }

    public function edit(Purchase $purchase)
    {
        return view('purchase-edit', compact('purchase'));
    }

    public function planning()
    {
        return view('purchase-planning');
    }

    public function completed()
    {
        return view('purchase-completed');
    }

    public function completedPurchase(Purchase $purchase)
    {
        return view('purchase-done', compact('purchase'));
    }
}
