<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\OwnerNotification;
use App\Models\ProductGroup;
use App\Models\Supplier;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

use Spatie\Activitylog\Models\Activity;

class CustomerController extends Controller
{
    public function index()
    {
        return view('customers');
    }

    public function edit(Customer $customer)
    {
        $productGroups = ProductGroup::all();
//        $agent = new Agent();
//        if ($agent->isMobile()) {
//            return view('mobile-customer-edit')->with('customer', $customer)->with('productGroups', $productGroups);
//        }

        return view('customer-edit')->with('customer', $customer)->with('productGroups', $productGroups);
    }

    public function update(Request $request, Customer $customer)
    {
        $validatedData = $request->validate([
            'exact_id' => 'required|numeric',
            'company_name' => 'required|string|max:255',
            'name' => 'required|string|max:255',
            'telephone' => 'required',
            'street' => 'required|string|max:255',
            'housenumber' => 'required|string|max:50',
            'postal_code' => 'required|string|max:10',
            'city' => 'required|string|max:255',
            'product_groups' => 'nullable|array',
            'product_groups.*' => 'nullable|exists:product_groups,id',
        ]);

        $customer->exact_id = $request->get('exact_id');
        $customer->company_name = $request->get('company_name');
        $customer->name = $request->get('name');
        $customer->telephone = $request->get('telephone');
        $customer->street_name = $request->get('street');
        $customer->housenumber = $request->get('housenumber');
        $customer->postal_code = $request->get('postal_code');
        $customer->city = $request->get('city');
        $customer->save();

        if ($customer->owned_by) {
            if ($customer->owned_by !== auth()->id()) {
                OwnerNotification::create([
                    'owner_id' => $customer->owned_by,
                    'editor_id' => auth()->id(),
                    'notifiable_id' => $customer->id,
                    'notifiable_type' => Customer::class,
                    'action' => 'edited',
                    'details' => 'Klant is gewijzigd',
                ]);
            }
        }

        return redirect()->back();
    }

    public function showActivities(Customer $customer)
    {
        $activities = Activity::forSubject($customer)->latest()->get();

        return view('customer-activities', compact('activities'));
    }

    public function relaties(Request $request)
    {
        $data = $request->getBody();
    }
}
