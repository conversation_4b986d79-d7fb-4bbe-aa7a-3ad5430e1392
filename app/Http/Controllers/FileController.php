<?php

namespace App\Http\Controllers;

use App\Models\Attachment;
use App\Models\Customer;
use App\Models\Prospect;
use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class FileController extends Controller
{
    public function store(Request $request)
    {
        $temporaryFileKey = $request->key;
        $targetFileKey = 'attachments/' . $request->name;
        Storage::copy($temporaryFileKey, $targetFileKey);

        Storage::delete($temporaryFileKey);

        switch ($request->type) {
            case 'App\\Models\\Customer':
                $model = Customer::find($request->entity_id);
                break;
            case 'App\\Models\\Prospect':
                $model = Prospect::find($request->entity_id);
                break;
            case 'App\\Models\\Supplier':
                $model = Supplier::find($request->entity_id);
                break;
        }

        $model->attachments()->create([
            'filename' => $request->name,
        ]);

        return response()->json(['message' => 'File stored successfully']);
    }
}
