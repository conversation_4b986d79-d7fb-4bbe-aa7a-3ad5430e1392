<?php

namespace App\Http\Controllers;

use App\Models\Purchase;
use Illuminate\Http\Request;

class OrderController extends Controller
{
    public function getOrder()
    {
        // Only return orders that have been completed for at least 48 hours
        $purchaseOrder = Purchase::where('completed', 1)
            ->where('sale_exact', 0)
            ->where('updated_at', '<=', now()->subHours(48))
            ->first();

        if (!$purchaseOrder) {
            return response()->json(['success' => 'success'], 200);
        }

        foreach ($purchaseOrder->sales as $sale) {

            $orders = [];
    //        $customer = $order->customer;
            $address = $sale->address;

            $orderArray = [];
            $orderArray['type'] = 'V';
            $orderArray['admin'] = '0' . $purchaseOrder->administration;
            $orderArray['order_id'] = $purchaseOrder->id;
            $orderArray['customer_id'] = $sale->customer->exact_id;
            $orderArray['delivery_date'] = str_replace('00:00:00', '', $purchaseOrder->delivery_date);
            $orderArray['delivery_company'] = $address->company_name;
            $orderArray['delivery_name'] = $address->name;
            $orderArray['delivery_street'] = $address->street . ' ' . $address->housenumber;
            $orderArray['delivery_postalcode'] = $address->postal_code;
            $orderArray['delivery_city'] = $address->city;
            $orderArray['delivery_country'] = $address->country;
            $orderArray['transport_price'] = $sale->transport_cost;
            $orderArray['order_number_buyer'] = $purchaseOrder->purchase_number;
            $orderArray['lines'] = [];

            $count = 1;
    //        foreach($order->orderitems as $orderItem) {
                $orderline = [];
                $orderline['line_number'] = $count;

                $orderline['sku'] = $sale->product->sku;
                $orderline['qty'] = $sale->quantity;


                $orderline['price'] = $sale->total_price;

                $orderArray['lines'][] = $orderline;
                $count++;
    //        }
            $orders[] = $orderArray;
            $sale->exact = 1;
            $sale->save();
            $purchaseOrder->sale_exact = 1;
            $purchaseOrder->save();
            return response()->json($orders);
        }
    }

    public function getPurchaseOrder()
    {
        // Only return purchase orders that have been completed for at least 48 hours
        $purchaseOrder = Purchase::where('exact', 0)
            ->where('completed', 1)
            ->where('updated_at', '<=', now()->subHours(48))
            ->first();
        if ($purchaseOrder) {
            $orders = [];

            $orderArray = [];
            $orderArray['type'] = 'B';
            $orderArray['admin'] = "0" . $purchaseOrder->administration;
            $orderArray['order_id'] = $purchaseOrder->id;
            $orderArray['supplier_id'] = $purchaseOrder->supplier->exact_id;
            $orderArray['delivery_date'] = str_replace('00:00:00', '', $purchaseOrder->load_date);
            $orderArray['order_number_buyer'] = $purchaseOrder->purchase_number;
            $orderArray['lines'] = [];

            $count = 1;

            $orderline = [];
            $orderline['line_number'] = $count;

            $orderline['sku'] = $purchaseOrder->product->sku;
            $orderline['qty'] = $purchaseOrder->quantity;


            $orderline['price'] = $purchaseOrder->price;
            $orderArray['lines'][] = $orderline;

            $orders[] = $orderArray;

            $purchaseOrder->exact = 1;
            $purchaseOrder->save();

            return response()->json($orders);
        }

        return response()->json(['success' => 'success'], 200);
    }

    public function puchaseSale()
    {
        return view('purchase-sale');
    }
}
