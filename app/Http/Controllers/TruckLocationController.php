<?php

namespace App\Http\Controllers;

use App\Models\TruckLocation;
use App\Models\Purchase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class TruckLocationController extends Controller
{
    /**
     * Store a newly created truck location in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Validate the request data
        $validator = Validator::make($request->all(), [
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'timestamp' => 'required|numeric',
            'accuracy' => 'nullable|numeric|min:0',
            'altitude' => 'nullable|numeric',
            'speed' => 'nullable|numeric|min:0',
            'heading' => 'nullable|numeric|between:0,360',
            'device_id' => 'nullable|string|max:255',
            'number_plate' => 'nullable|string|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Create a new truck location
        $location = new TruckLocation();
        $location->latitude = $request->latitude;
        $location->longitude = $request->longitude;
        $location->timestamp = $request->timestamp;
        $location->accuracy = $request->accuracy;
        $location->altitude = $request->altitude;
        $location->speed = $request->speed;
        $location->heading = $request->heading;
        $location->number_plate = $request->numberPlate;
        $location->current_state = $request->currentState;

        // Set user_id if authenticated, otherwise use device_id
        if (Auth::check()) {
            $location->user_id = Auth::id();
        } else {
            $location->device_id = $request->device_id ?? $request->ip();
        }

        $location->save();

        return response()->json(['message' => 'Location saved successfully', 'location' => $location], 201);
    }

    /**
     * Display the map view with truck locations.
     *
     * @return \Illuminate\Http\Response
     */
    public function map()
    {
        return view('truck-locations.map');
    }

    /**
     * Display a simplified map view for testing.
     *
     * @return \Illuminate\Http\Response
     */
    public function simpleMap()
    {
        return view('truck-locations.simple-map');
    }

    /**
     * Display a basic map view without using @push directives.
     *
     * @return \Illuminate\Http\Response
     */
    public function basicMap()
    {
        return view('truck-locations.basic-map');
    }

    /**
     * Get the latest locations for all trucks.
     *
     * @return \Illuminate\Http\Response
     */
    public function getLatestLocations()
    {
        // Get the latest location for each truck, prioritizing number_plate as identifier
        $latestLocations = TruckLocation::whereNotNull('number_plate')
            ->select('number_plate')
            ->selectRaw('MAX(id) as latest_id')
            ->groupBy('number_plate')
            ->get()
            ->pluck('latest_id');

        // Also get latest for trucks without number_plate (fallback to device_id/user_id)
        $fallbackLocations = TruckLocation::whereNull('number_plate')
            ->select('user_id', 'device_id')
            ->selectRaw('MAX(id) as latest_id')
            ->groupBy('user_id', 'device_id')
            ->get()
            ->pluck('latest_id');

        // Combine both sets of IDs
        $allLatestIds = $latestLocations->merge($fallbackLocations);

        $locations = TruckLocation::whereIn('id', $allLatestIds)
            ->with('user') // Include user data if available
            ->get();

        return response()->json($locations);
    }

    /**
     * Get location history for a specific truck.
     *
     * @param  Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getLocationHistory(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'nullable|exists:users,id',
            'device_id' => 'nullable|string',
            'number_plate' => 'nullable|string|max:20',
            'hours' => 'nullable|integer|min:1|max:24',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $hours = $request->hours ?? 1; // Default to 1 hour
        $timestamp = now()->subHours($hours)->timestamp * 1000; // Convert to milliseconds

        $query = TruckLocation::where('timestamp', '>=', $timestamp);

        if ($request->has('number_plate')) {
            $query->where('number_plate', $request->number_plate);
        } elseif ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        } elseif ($request->has('device_id')) {
            $query->where('device_id', $request->device_id);
        } else {
            return response()->json(['error' => 'Either number_plate, user_id, or device_id is required'], 422);
        }

        $locations = $query->orderBy('timestamp', 'asc')->get();

        return response()->json($locations);
    }

    /**
     * Get all purchases with own transport that don't have a signature yet.
     * Returns purchases where own_transport = true AND signature IS NULL.
     *
     * Includes complete supplier loading address information:
     * - Default supplier address OR custom address if specified
     * - Loading times and coordinates for navigation
     * - Address type indicator (custom vs default)
     *
     * Optional query parameters:
     * - status: ready_for_planning, planned, completed, not_completed
     * - load_date_from: YYYY-MM-DD format
     * - load_date_to: YYYY-MM-DD format
     * - delivery_date_from: YYYY-MM-DD format
     * - delivery_date_to: YYYY-MM-DD format
     * - supplier_id: integer
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getOwnTransportPurchases(Request $request)
    {
        $query = Purchase::where('own_transport', true)
            ->whereNull('signature');

        // Optional filters
        if ($request->has('status')) {
            switch ($request->status) {
                case 'ready_for_planning':
                    $query->where('ready_for_planning', true);
                    break;
                case 'planned':
                    $query->where('planned', true);
                    break;
                case 'completed':
                    $query->where('completed', true);
                    break;
                case 'not_completed':
                    $query->where('completed', false);
                    break;
            }
        }

        if ($request->has('load_date_from')) {
            $query->where('load_date', '>=', $request->load_date_from);
        }

        if ($request->has('load_date_to')) {
            $query->where('load_date', '<=', $request->load_date_to);
        }

        if ($request->has('delivery_date_from')) {
            $query->where('delivery_date', '>=', $request->delivery_date_from);
        }

        if ($request->has('delivery_date_to')) {
            $query->where('delivery_date', '<=', $request->delivery_date_to);
        }

        if ($request->has('supplier_id')) {
            $query->where('supplier_id', $request->supplier_id);
        }

        $purchases = $query
            ->with([
                'sales' => function ($query) {
                    $query->with(['customer', 'product', 'shippingAddress']);
                },
                'supplier',
                'product',
                'user'
            ])
            ->get();

        // Transform the data to include additional calculated fields
        $transformedPurchases = $purchases->map(function ($purchase) {
            return [
                'id' => $purchase->id,
                'purchase_number' => $purchase->purchase_number,
                'supplier' => [
                    'id' => $purchase->supplier->id,
                    'company_name' => $purchase->supplier->company_name,
                    'city' => $purchase->supplier->city,
                    'loading_address' => [
                        'street' => $purchase->custom_supplier_address ? $purchase->supplier_street : $purchase->supplier->street_name,
                        'housenumber' => $purchase->custom_supplier_address ? $purchase->supplier_housenumber : $purchase->supplier->housenumber,
                        'postal_code' => $purchase->custom_supplier_address ? $purchase->supplier_postal_code : $purchase->supplier->postal_code,
                        'city' => $purchase->custom_supplier_address ? $purchase->supplier_city : $purchase->supplier->city,
                        'country' => $purchase->custom_supplier_address ? $purchase->supplier_country : $purchase->supplier->country,
                        'is_custom_address' => $purchase->custom_supplier_address,
                        'loading_times' => $purchase->loading_times
                    ],
                ],
                'product' => [
                    'id' => $purchase->product->id,
                    'name' => $purchase->product->name,
                ],
                'quantity' => $purchase->quantity,
                'qty_type' => $purchase->qty_type,
                'price' => $purchase->price,
                'total' => $purchase->total,
                'load_date' => $purchase->load_date,
                'delivery_date' => $purchase->delivery_date,
                'load_weight' => $purchase->load_weight,
                'unload_weight' => $purchase->unload_weight,
                'own_transport' => $purchase->own_transport,
                'ready_for_planning' => $purchase->ready_for_planning,
                'planned' => $purchase->planned,
                'completed' => $purchase->completed,
                'created_by' => $purchase->user ? $purchase->user->name : null,
                'created_at' => $purchase->created_at,
                'updated_at' => $purchase->updated_at,
                'sales' => $purchase->sales->map(function ($sale) {
                    return [
                        'id' => $sale->id,
                        'customer' => [
                            'id' => $sale->customer->id,
                            'company_name' => $sale->customer->company_name,
                            'city' => $sale->customer->city,
                        ],
                        'product' => [
                            'id' => $sale->product->id,
                            'name' => $sale->product->name,
                        ],
                        'shipping_address' => $sale->shippingAddress ? [
                            'id' => $sale->shippingAddress->id,
                            'name' => $sale->shippingAddress->name,
                            'company_name' => $sale->shippingAddress->company_name,
                            'street' => $sale->shippingAddress->street,
                            'housenumber' => $sale->shippingAddress->housenumber,
                            'postal_code' => $sale->shippingAddress->postal_code,
                            'city' => $sale->shippingAddress->city,
                            'country' => $sale->shippingAddress->country,
                            'unloading_times' => $sale->shippingAddress->unloading_times,
                        ] : null,
                        'quantity' => $sale->quantity,
                        'original_quantity' => $sale->original_quantity,
                        'actual_quantity' => $sale->actual_quantity,
                        'sale_qty_type' => $sale->sale_qty_type,
                        'price' => $sale->price,
                        'total_price' => $sale->total_price,
                        'transport_cost' => $sale->transport_cost,
                        'delivery_date' => $sale->purchase->delivery_date,
                        'customer_purchase_number' => $sale->customer_purchase_number,
                        'created_at' => $sale->created_at,
                        'updated_at' => $sale->updated_at,
                    ];
                }),
                'total_sales_quantity' => $purchase->sales->sum('quantity'),
                'total_sales_value' => $purchase->sales->sum('total_price'),
                'sales_count' => $purchase->sales->count(),
            ];
        });

        return response()->json([
            'success' => true,
            'message' => 'Own transport purchases retrieved successfully',
            'data' => $transformedPurchases,
            'count' => $transformedPurchases->count(),
        ]);
    }

    /**
     * Store a signature for a purchase.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function storeSignature(Request $request)
    {
        // Validate the request data
        $purchase = Purchase::where('purchase_number', $request->purchase_number)->first();

        $purchase->signature = $request->signature;
        $purchase->load_weight = $request->load_weight;
        $purchase->unload_weight = $request->unload_weight;
        $purchase->signature_received_at = now();
        $purchase->save();
        return response()->json([
                'success' => true,
            ], 200);

        // Find the purchase by purchase number
//        $purchase = Purchase::where('purchase_number', $request->purchase_number)->first();
//
//        if (!$purchase) {
//            return response()->json([
//                'success' => false,
//                'message' => 'Purchase not found with the provided purchase number',
//                'purchase_number' => $request->purchase_number
//            ], 404);
//        }
//
//        // Validate signature size (optional - to prevent extremely large signatures)
//        $signatureData = $request->signature;
//        $signatureSize = strlen($signatureData);
//        $maxSize = 5 * 1024 * 1024; // 5MB limit
//
//        if ($signatureSize > $maxSize) {
//            return response()->json([
//                'success' => false,
//                'message' => 'Signature data is too large. Maximum size is 5MB.',
//                'size' => $signatureSize,
//                'max_size' => $maxSize
//            ], 413);
//        }
//
//        // Save the signature to the purchase
//        $purchase->signature = $signatureData;
//        $purchase->signature_received_at = now();
//        $purchase->save();
//
//        return response()->json([
//            'success' => true,
//            'message' => 'Signature saved successfully',
//            'purchase' => [
//                'id' => $purchase->id,
//                'purchase_number' => $purchase->purchase_number,
//                'signature_received_at' => $purchase->signature_received_at,
//                'signature_size' => strlen($signatureData)
//            ]
//        ], 200);
    }

    /**
     * Get signature for a purchase.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getSignature(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'purchase_number' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Find the purchase by purchase number
        $purchase = Purchase::where('purchase_number', $request->purchase_number)->first();

        if (!$purchase) {
            return response()->json([
                'success' => false,
                'message' => 'Purchase not found with the provided purchase number',
                'purchase_number' => $request->purchase_number
            ], 404);
        }

        if (!$purchase->signature) {
            return response()->json([
                'success' => false,
                'message' => 'No signature found for this purchase',
                'purchase_number' => $request->purchase_number
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'Signature retrieved successfully',
            'purchase' => [
                'id' => $purchase->id,
                'purchase_number' => $purchase->purchase_number,
                'signature' => $purchase->signature,
                'signature_received_at' => $purchase->signature_received_at,
                'signature_size' => strlen($purchase->signature)
            ]
        ], 200);
    }
}
