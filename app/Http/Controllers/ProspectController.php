<?php

namespace App\Http\Controllers;

use App\Models\OwnerNotification;
use App\Models\ProductGroup;
use App\Models\Prospect;
use Illuminate\Http\Request;
use Jenssegers\Agent\Agent;
use Spatie\Activitylog\Models\Activity;

class ProspectController extends Controller
{
    public function index()
    {
        return view('prospects');
    }

    public function edit(Prospect $prospect)
    {
        $productGroups = ProductGroup::all();
        $agent = new Agent();
        if($agent->isMobile()) {
            return view('mobile-prospect-edit')->with('prospect', $prospect)->with('productGroups', $productGroups);
        }

        return view('prospect-edit')->with('prospect', $prospect)->with('productGroups', $productGroups);
    }

    public function update(Request $request, Prospect $prospect)
    {
        $validatedData = $request->validate([
            'exact_id' => 'required|numeric',
            'company_name' => 'required|string|max:255',
            'name' => 'required|string|max:255',
            'telephone' => 'required',
            'street' => 'required|string|max:255',
            'housenumber' => 'required|string|max:50',
            'postal_code' => 'required|string|max:10',
            'city' => 'required|string|max:255',
            'product_groups' => 'nullable|array',
            'product_groups.*' => 'nullable|exists:product_groups,id',
        ]);

        $prospect->company_name = $request->get('company_name');
        $prospect->name = $request->get('name');
        $prospect->exact_id = $request->get('exact_id');
        $prospect->telephone = $request->get('telephone');
        $prospect->street_name = $request->get('street');
        $prospect->housenumber = $request->get('housenumber');
        $prospect->postal_code = $request->get('postal_code');
        $prospect->city = $request->get('city');
        $prospect->save();

        if ($prospect->owned_by) {
            if ($prospect->owned_by !== auth()->id()) {
                OwnerNotification::create([
                    'owner_id' => $prospect->owned_by,
                    'editor_id' => auth()->id(),
                    'notifiable_id' => $prospect->id,
                    'notifiable_type' => Prospect::class,
                    'action' => 'edited',
                    'details' => 'Prospect is gewijzigd',
                ]);
            }
        }

        return redirect()->back();
    }

    public function showActivities(Prospect $prospect)
    {
        $activities = Activity::forSubject($prospect)->latest()->get();

        return view('prospect-activities', compact('activities'));
    }
}
