<?php

namespace App\Http\Controllers;

use App\Models\OwnerNotification;
use App\Models\ProductGroup;
use App\Models\Supplier;
use Illuminate\Http\Request;

use Spatie\Activitylog\Models\Activity;

class SupplierController extends Controller
{
    public function index()
    {
        return view('suppliers');
    }

    public function edit(Supplier $supplier)
    {
        $productGroups = ProductGroup::all();


        return view('supplier-edit')->with('supplier', $supplier)->with('productGroups', $productGroups);
    }

    public function update(Request $request, Supplier $supplier)
    {
        $validatedData = $request->validate([
            'exact_id' => 'required|numeric',
            'company_name' => 'required|string|max:255',
            'name' => 'required|string|max:255',
            'telephone' => 'required',
            'street' => 'required|string|max:255',
            'housenumber' => 'required|string|max:50',
            'postal_code' => 'required|string|max:10',
            'city' => 'required|string|max:255',
            'product_groups' => 'nullable|array',
            'product_groups.*' => 'nullable|exists:product_groups,id',
        ]);

        $supplier->company_name = $request->get('company_name');
        $supplier->name = $request->get('name');
        $supplier->telephone = $request->get('telephone');
        $supplier->exact_id = $request->get('exact_id');
        $supplier->street_name = $request->get('street');
        $supplier->housenumber = $request->get('housenumber');
        $supplier->postal_code = $request->get('postal_code');
        $supplier->city = $request->get('city');
        $supplier->save();

        if ($supplier->owned_by) {
            if ($supplier->owned_by !== auth()->id()) {
                OwnerNotification::create([
                    'owner_id' => $supplier->owned_by,
                    'editor_id' => auth()->id(),
                    'notifiable_id' => $supplier->id,
                    'notifiable_type' => Supplier::class,
                    'action' => 'edited',
                    'details' => 'Leverancier is gewijzigd',
                ]);
            }
        }

        return redirect()->back();
    }

    public function showActivities(Supplier $supplier)
    {
        $activities = Activity::forSubject($supplier)->latest()->get();

        return view('supplier-activities', compact('activities'));
    }
}
