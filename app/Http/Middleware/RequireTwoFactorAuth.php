<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RequireTwoFactorAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();

        // If user is not authenticated, let other middleware handle it
        if (!$user) {
            return $next($request);
        }

        // Routes that should be excluded from 2FA requirement
        $excludedRoutes = [
            'two-factor.login',
            'two-factor.enable',
            'two-factor.confirm',
            'two-factor.disable',
            'two-factor.recovery-codes',
            'user-profile-information.update',
            'user-password.update',
            'current-user-photo.update',
            'current-user-photo.destroy',
            'profile.show',
            'logout',
            'password.confirm',
            'password.confirmation',
        ];

        // Check if current route should be excluded
        if (in_array($request->route()->getName(), $excludedRoutes)) {
            return $next($request);
        }

        // Check if user has 2FA enabled and confirmed
        if (!$user->hasEnabledTwoFactorAuthentication()) {
            // Redirect to profile page with a message to enable 2FA
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Two-factor authentication is required.',
                    'redirect' => route('profile.show')
                ], 403);
            }

            return redirect()->route('profile.show')
                ->with('status', 'two-factor-required')
                ->with('message', 'Two-factor authentication is required to access this application. Please enable it in your profile settings.');
        }

        return $next($request);
    }
}
