<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Mail;
use Illuminate\Mail\Events\MessageSending;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Mime\Email;

class TestMailServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Always intercept emails in all environments
        // Listen for the MessageSending event
        Event::listen(MessageSending::class, function (MessageSending $event) {
            // Get the current user's email
            $userEmail = Auth::check() ? Auth::user()->email : null;

            if (!$userEmail) {
                Log::warning('No authenticated user found when sending email. Email will be sent to original recipient.');
                return;
            }

            // Force all emails to go to the logged-in user
            Log::info("Redirecting all emails to: {$userEmail}");

            // Get the message object
            $message = $event->message;

            // Check if it's a Symfony Email object
            if (method_exists($message, 'getHeaders')) {
                // Get the original recipients for logging
                $originalTo = [];
                $headers = $message->getHeaders();

                if ($headers->has('To')) {
                    $toHeader = $headers->get('To')->getBodyAsString();
                    preg_match_all('/<([^>]+)>/', $toHeader, $matches);
                    $originalTo = $matches[1] ?? [];
                }

                $originalToString = implode(', ', $originalTo);

                // Get the email subject
                $subject = '';
                if ($headers->has('Subject')) {
                    $subject = $headers->get('Subject')->getBodyAsString();
                }

                // Keep the original subject
                $newSubject = $subject;

                // Get the original message
                $originalMessage = $event->message;

                // Create a new message with the same content but different recipients
                if (method_exists($originalMessage, 'getBody')) {
                    $body = $originalMessage->getBody();

                    // Create a new message
                    $newMessage = (new Email())
                        ->subject($newSubject)
                        ->to($userEmail);

                    // Add a note about the original recipient in the email body
                    $originalRecipientNote = "<div style=\"background-color: #f8f9fa; padding: 10px; margin-bottom: 15px; border: 1px solid #dee2e6; border-radius: 5px;\">\n";
                    $originalRecipientNote .= "<p style=\"margin: 0;\"><strong>Note:</strong> This email was originally intended for: {$originalToString}</p>\n";
                    $originalRecipientNote .= "</div>\n";

                    // Copy the body
                    if (method_exists($body, 'getHtmlBody') && $body->getHtmlBody()) {
                        // Add the original recipient note to the HTML body
                        $htmlBody = $body->getHtmlBody();

                        // Try to insert the note after the opening body tag
                        if (strpos($htmlBody, '<body') !== false) {
                            $htmlBody = preg_replace('/(<body[^>]*>)/i', '$1' . $originalRecipientNote, $htmlBody);
                        } else {
                            // If no body tag, just prepend to the HTML
                            $htmlBody = $originalRecipientNote . $htmlBody;
                        }

                        $newMessage->html($htmlBody);
                    }

                    if (method_exists($body, 'getTextBody') && $body->getTextBody()) {
                        // Add the original recipient note to the text body
                        $textBody = $body->getTextBody();
                        $textNote = "Note: This email was originally intended for: {$originalToString}\n\n";
                        $newMessage->text($textNote . $textBody);
                    }

                    // Copy attachments if any
                    if (method_exists($originalMessage, 'getAttachments')) {
                        foreach ($originalMessage->getAttachments() as $attachment) {
                            $newMessage->attach(
                                $attachment->getBody(),
                                $attachment->getName(),
                                $attachment->getContentType()
                            );
                        }
                    }

                    // Replace the original message with our new one
                    $event->message = $newMessage;
                } else {
                    Log::warning('Could not modify email recipients. Email redirection may not work properly.');
                }
            } else {
                // Fallback for other message types
                Log::warning('Unknown message type in TestMailServiceProvider. Email redirection may not work properly.');
            }

            // Log the redirection
            if (isset($originalToString) && isset($subject)) {
                Log::info("Email redirected: From '{$originalToString}' to '{$userEmail}'. Subject: '{$subject}'");
            } else {
                Log::info("Email redirected to '{$userEmail}'.");
            }

            // Add a note to the application log
            if (isset($originalToString)) {
                Log::channel('daily')->info("TEST MODE: Email to '{$originalToString}' redirected to '{$userEmail}'.");
            }
        });
    }
}
