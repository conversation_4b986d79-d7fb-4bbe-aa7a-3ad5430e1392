<?php

namespace App\Traits;

trait RefreshesLog
{
    /**
     * Refresh the log property from the database after a purchase has been updated
     * This ensures the Livewire component shows the latest log entries added by the observer
     */
    protected function refreshPurchaseLog()
    {
        if (isset($this->purchase) && $this->purchase) {
            $this->log = $this->purchase->fresh()->log;
        }
    }

    /**
     * Save a purchase and refresh the log
     */
    protected function savePurchaseAndRefreshLog()
    {
        if (isset($this->purchase) && $this->purchase) {
            $this->purchase->save();
            $this->refreshPurchaseLog();
        }
    }
}
