<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function customerPrices()
    {
        return $this->hasMany(CustomerProductPrice::class);
    }

    public function getCustomerPrice($customerId, $productId, $quantity)
    {
        return CustomerProductPrice::where('customer_id', $customerId)
            ->where('product_id', $productId)
            ->where('min_qty', '<=', $quantity)
            ->where('max_qty', '>=', $quantity)
            ->orderBy('min_qty', 'desc') // Zorgt dat de hoogste passende staffel gekozen wordt
            ->first();
    }

    public function customerPricesFor($customerId)
    {
        return $this->hasMany(CustomerProductPrice::class)
            ->where('customer_id', $customerId)
            ->orderBy('min_qty', 'asc'); // Sorteer op staffel vanaf de kleinste hoeveelheid
    }

    public function getSupplierPrice($supplierId, $productId, $quantity)
    {
        return SupplierProductPrice::where('supplier_id', $supplierId)
            ->where('product_id', $productId)
            ->where('min_qty', '<=', $quantity)
            ->where('max_qty', '>=', $quantity)
            ->orderBy('min_qty', 'desc') // Zorgt dat de hoogste passende staffel gekozen wordt
            ->first();
    }

    public function supplierPricesFor($supplierId)
    {
        return $this->hasMany(SupplierProductPrice::class)
            ->where('supplier_id', $supplierId)
            ->orderBy('min_qty', 'asc')->get();
    }

    public function suppliers()
    {
        return $this->belongsToMany(Supplier::class, 'supplier_products')
                    ->withPivot('unit_price', 'unit_type', 'end_date', 'default_qty')
                    ->withTimestamps();
    }

    public function customers()
    {
        return $this->belongsToMany(Customer::class, 'customer_products')
                    ->withPivot('unit_price', 'unit_type', 'end_date', 'default_qty')
                    ->withTimestamps();
    }
}
