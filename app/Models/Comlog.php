<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Comlog extends Model
{
    protected $guarded = [];

    public static function parseComlog()
    {
        $comlogs = self::where('parsed', 0)->get();
        foreach ($comlogs as $comlog) {
            if (str_contains($comlog->message, 'ArticleCode')) {
                self::parseArticle($comlog);
                $comlog->parsed = 1;
                $comlog->save();
            }

            if (str_contains($comlog->message, 'SoortRelatie')) {
                self::parseRelation($comlog);
                $comlog->parsed = 1;
                $comlog->save();
            }
        }
    }

    public static function parseArticle($comlog)
    {
        $raw = $comlog->message;
        $firstStep = str_replace("\\", '', $raw);
        $secondStep = str_replace('["', '', $firstStep);
        $thirdStep = str_replace('"]', '', $secondStep);
        $jsonComlog = json_decode($thirdStep);

        foreach ($jsonComlog->ArticleCode->ArticleCode as $jsonComlog) {
            Product::firstOrCreate(
                ['sku' => $jsonComlog->ArticleCode], // Zoek op SKU
                [
                    'name' => $jsonComlog->ArticleDescription,
                    'description' => $jsonComlog->ArticleDescription,
                ]
            );
        }
    }

    public static function parseRelation($comlog)
    {
        $raw = $comlog->message;
        $firstStep = str_replace("\\", '', $raw);
        $secondStep = str_replace('["', '', $firstStep);
        $thirdStep = str_replace('"]', '', $secondStep);
        $jsonComlogs = json_decode($thirdStep);

        if (!$jsonComlogs) {
            return;
        }
        foreach ($jsonComlogs->cmp_code->cmp_code as $jsonComlog) {
            if ($jsonComlog->SoortRelatie == 'S') {
                preg_match('/^([\p{L}\s]+)(?:\s(\d+\s?[A-Za-z]*))?$/u', $jsonComlog->Adres, $matches);

                Supplier::firstOrCreate(
                    ['exact_id' => $jsonComlog->cmp_code],
                    [
                        'company_name' => $jsonComlog->Naam,
                        'name' => $jsonComlog->Naam,
                        'street_name' => $matches[1] ?? '',
                        'housenumber' => $matches[2] ?? '',
                        'postal_code' => isset($jsonComlog->Postcode) ? $jsonComlog->Postcode : '',
                        'city' => $jsonComlog->Plaats,
                        'country' => 'NL'
                    ]
                );
            }

            if ($jsonComlog->SoortRelatie == 'C') {
                preg_match('/^([\p{L}\s]+)(?:\s(\d+\s?[A-Za-z]*))?$/u', $jsonComlog->Adres, $matches);


                Customer::firstOrCreate(
                    ['exact_id' => $jsonComlog->cmp_code],
                    [
                        'company_name' => $jsonComlog->Naam,
                        'name' => $jsonComlog->Naam,
                        'street_name' => $matches[1] ?? '',
                        'housenumber' => $matches[2] ?? '',
                        'postal_code' => isset($jsonComlog->Postcode) ? $jsonComlog->Postcode : '',
                        'city' => $jsonComlog->Plaats,
                        'country' => 'NL'
                    ]
                );
            }
        }
    }
}
