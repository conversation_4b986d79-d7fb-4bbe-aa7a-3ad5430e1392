<?php

namespace App\Models;

use App\Services\GeocodeService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Supplier extends Model
{
    use HasFactory, LogsActivity;

    protected $guarded = [];

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($supplier) {
            // Check if address fields have changed
            if ($supplier->isDirty('street_name') ||
                $supplier->isDirty('housenumber') ||
                $supplier->isDirty('postal_code') ||
                $supplier->isDirty('city') ||
                $supplier->isDirty('country')) {

                // Geocode the address
                $geocodeService = new GeocodeService();
                $coordinates = $geocodeService->geocode(
                    $supplier->street_name,
                    $supplier->housenumber,
                    $supplier->postal_code,
                    $supplier->city,
                    $supplier->country
                );

                if ($coordinates) {
                    $supplier->latitude = $coordinates['latitude'];
                    $supplier->longitude = $coordinates['longitude'];
                }
            }
        });
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logUnguarded();
    }

    public function tasks()
    {
        return $this->morphMany(Task::class, 'taskable');
    }

    public function actions()
    {
        return $this->morphMany(Action::class, 'actionable');
    }

    public function assignedTo()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function ownedBy()
    {
        return $this->belongsTo(User::class, 'owned_by');
    }

    public function contactPersons()
    {
        return $this->morphMany(ContactPerson::class, 'contactable');
    }

    public function interactions()
    {
        return $this->morphMany(Interaction::class, 'interactable');
    }

    public function notes()
    {
        return $this->morphMany(Note::class, 'noteable');
    }

    public function productGroups()
    {
        return $this->morphToMany(ProductGroup::class, 'product_groupable');
    }

    public function attachments()
    {
        return $this->morphMany(Attachment::class, 'attachable');
    }

    public function planTasks()
    {
        return $this->morphMany(PlanTask::class, 'plannable');
    }

    public function products()
    {
        return $this->belongsToMany(Product::class, 'supplier_products')
                    ->withPivot('unit_price', 'unit_type', 'end_date', 'default_qty')
                    ->withTimestamps();
    }

    public function addresses()
    {
        return $this->hasMany(SupplierAddress::class);
    }

    /**
     * Get active products (non-expired price agreements)
     * Returns the most recent active price agreement for each product
     */
    public function activeProducts()
    {
        return $this->belongsToMany(Product::class, 'supplier_products')
                    ->withPivot('unit_price', 'unit_type', 'end_date', 'default_qty', 'id')
                    ->withTimestamps()
                    ->where(function($query) {
                        $query->whereNull('supplier_products.end_date')
                              ->orWhere('supplier_products.end_date', '>=', now()->toDateString());
                    })
                    ->orderBy('supplier_products.created_at', 'desc');
    }
}
