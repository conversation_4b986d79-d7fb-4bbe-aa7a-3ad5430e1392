<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductGroup extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function customers()
    {
        return $this->morphedByMany(Customer::class, 'product_groupable');
    }

    public function suppliers()
    {
        return $this->morphedByMany(Supplier::class, 'product_groupable');
    }

    public function prospects()
    {
        return $this->morphedByMany(Prospect::class, 'product_groupable');
    }
}
