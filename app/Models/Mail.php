<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Tonysm\RichTextLaravel\Models\Traits\HasRichText;

class Mail extends Model
{
    protected $guarded = [];

    // Commenting out HasRichText for now as it might be causing issues
    // use HasRichText;
    // protected $richTextAttributes = ['mail_message'];

    protected $casts = [
        'attachments' => 'array',
    ];

    public function purchase()
    {
        return $this->belongsTo(Purchase::class);
    }
}
