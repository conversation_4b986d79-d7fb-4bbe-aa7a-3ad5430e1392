<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PlanTask extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    public function plannable()
    {
        return $this->morphTo();
    }

    public function sharedPlanList()
    {
        return $this->belongsTo(SharedPlanList::class, 'shared_plan_list_id');
    }

    public function assignedToUser()
    {
        return $this->belongsTo(User::class, 'assigned_to_user_id');
    }
}
