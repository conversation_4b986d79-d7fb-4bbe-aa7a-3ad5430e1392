<?php

namespace App\Models;

use App\Services\GeocodeService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SupplierAddress extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($address) {
            // Check if address fields have changed
            if ($address->isDirty('street') ||
                $address->isDirty('housenumber') ||
                $address->isDirty('postal_code') ||
                $address->isDirty('city') ||
                $address->isDirty('country')) {

                // Geocode the address
//                $geocodeService = new GeocodeService();
//                $coordinates = $geocodeService->geocode(
//                    $address->street,
//                    $address->housenumber,
//                    $address->postal_code,
//                    $address->city,
//                    $address->country
//                );

//                if ($coordinates) {
//                    $address->latitude = $coordinates['latitude'];
//                    $address->longitude = $coordinates['longitude'];
//                }
            }
        });
    }

    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }
}
