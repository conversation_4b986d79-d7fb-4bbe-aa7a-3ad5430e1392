<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Prospect extends Model
{
    use HasFactory, LogsActivity;

    protected $guarded = [];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logUnguarded();
    }

    public function tasks()
    {
        return $this->morphMany(Task::class, 'taskable');
    }

    public function actions()
    {
        return $this->morphMany(Action::class, 'actionable');
    }

    public function assignedTo()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function ownedBy()
    {
        return $this->belongsTo(User::class, 'owned_by');
    }

    public function contactPersons()
    {
        return $this->morphMany(ContactPerson::class, 'contactable');
    }

    public function interactions()
    {
        return $this->morphMany(Interaction::class, 'interactable');
    }

    public function notes()
    {
        return $this->morphMany(Note::class, 'noteable');
    }

    public function productGroups()
    {
        return $this->morphToMany(ProductGroup::class, 'product_groupable');
    }

    public function attachments()
    {
        return $this->morphMany(Attachment::class, 'attachable');
    }

    public function planTasks()
    {
        return $this->morphMany(PlanTask::class, 'plannable');
    }
}
