<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Purchase extends Model
{
    use HasFactory;

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($purchase) {
            $purchase->purchase_number = static::generatePurchaseNumber();
        });
    }

    public static function generatePurchaseNumber()
    {
        // Get the current year's last two digits
        $currentYear = date('y'); // e.g., '25' for 2025

        // Find the last purchase number for the current year
        $lastPurchase = self::where('purchase_number', 'like', $currentYear . '%')
            ->orderBy('purchase_number', 'desc')
            ->first();

        if ($lastPurchase) {
            // Extract the sequence number (last 4 digits)
            $lastSequence = (int) substr($lastPurchase->purchase_number, 2);
            $newSequence = $lastSequence + 1;
        } else {
            // Start with 0001 for the first purchase of the year
            $newSequence = 1;
        }

        // Format: YY + 4-digit sequence number (e.g., 250001, 250002, etc.)
        return $currentYear . str_pad($newSequence, 4, '0', STR_PAD_LEFT);
    }

    protected $guarded = [];

    protected $casts = [
        'signature_received_at' => 'datetime',
        'load_weight' => 'decimal:2',
        'unload_weight' => 'decimal:2',
    ];

    public function sales()
    {
        return $this->hasMany(Sale::class);
    }

    public function transport()
    {
        return $this->hasOne(Transport::class);
    }

    public function product()
    {
        return $this->hasOne(Product::class, 'id', 'product_id');
    }

    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    public function shipper()
    {
        return $this->hasOne(Supplier::class, 'id', 'shipper_id');
    }

    public function comment()
    {
        return $this->hasMany(PurchaseComment::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

}
