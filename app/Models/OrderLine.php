<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderLine extends Model
{
    use HasFactory;

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

//    public function product()
//    {
//        return $this->belongsTo(Product::class);
//    }

    public function shipment()
    {
        return $this->belongsTo(ShipmentLine::class);
    }
}
