<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TruckLocation extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'device_id',
        'number_plate',
        'current_state',
        'latitude',
        'longitude',
        'timestamp',
        'accuracy',
        'altitude',
        'speed',
        'heading',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'latitude' => 'float',
        'longitude' => 'float',
        'timestamp' => 'integer',
        'accuracy' => 'float',
        'altitude' => 'float',
        'speed' => 'float',
        'heading' => 'float',
    ];

    /**
     * Get the user that owns the location.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the formatted date from the timestamp.
     *
     * @return string
     */
    public function getFormattedDateAttribute()
    {
        // Convert milliseconds to seconds for PHP timestamp
        $timestamp = $this->timestamp / 1000;
        return date('Y-m-d H:i:s', $timestamp);
    }
}
