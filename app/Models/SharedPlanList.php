<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SharedPlanList extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($sharedPlanList) {
            $sharedPlanList->tasks()->delete();
        });
    }

    public function tasks()
    {
        return $this->hasMany(PlanTask::class, 'shared_plan_list_id');
    }

    public function currentWeekTasks()
    {
        return $this->hasMany(PlanTask::class)
            ->where(function($query) {
                $startOfWeek = Carbon::now()->startOfWeek();
                $endOfWeek = Carbon::now()->endOfWeek();

                $query->whereBetween('due_date', [$startOfWeek, $endOfWeek])
                    ->orWhere('due_date', '<', $startOfWeek);
            });
    }

    public function users()
    {
        return $this->belongsToMany(User::class)->withTimestamps();
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by_user_id');
    }
}
