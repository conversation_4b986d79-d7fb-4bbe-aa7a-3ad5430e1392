<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Interaction extends Model
{
    use HasFactory;

    public $guarded = [];

    public function interactable()
    {
        return $this->morphTo();
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function getTypeAttribute($value)
    {
        $types = [
            'call' => 'Telefoon',
            'meeting' => 'Bezoek',
            'email' => 'Email',
        ];

        return isset($types[$value]) ? $types[$value] : $value;
    }
}
