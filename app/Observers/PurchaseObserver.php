<?php

namespace App\Observers;

use App\Models\Purchase;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class PurchaseObserver
{
    /**
     * Handle the Purchase "created" event.
     */
    public function created(Purchase $purchase): void
    {
        $this->logChange($purchase, 'Inkoop aangemaakt', [
            'purchase_number' => $purchase->purchase_number,
            'supplier' => $purchase->supplier->company_name ?? 'Onbekend',
            'product' => $purchase->product->name ?? 'Onbekend',
            'quantity' => $purchase->quantity . ' ' . $purchase->qty_type,
            'price' => '€' . number_format($purchase->price, 2),
            'total' => '€' . number_format($purchase->total, 2)
        ]);
    }

    /**
     * Handle the Purchase "updated" event.
     */
    public function updated(Purchase $purchase): void
    {
        $changes = $purchase->getChanges();
        $original = $purchase->getOriginal();

        // Skip if only log field was updated to prevent infinite loop
        if (count($changes) === 1 && isset($changes['log'])) {
            return;
        }

        // Skip if only updated_at was changed
        if (count($changes) === 1 && isset($changes['updated_at'])) {
            return;
        }

        foreach ($changes as $field => $newValue) {
            // Skip certain fields that shouldn't be logged
            if (in_array($field, ['updated_at', 'log'])) {
                continue;
            }

            $oldValue = $original[$field] ?? null;
            $this->logFieldChange($purchase, $field, $oldValue, $newValue);
        }
    }

    /**
     * Handle the Purchase "deleted" event.
     */
    public function deleted(Purchase $purchase): void
    {
        $this->logChange($purchase, 'Inkoop verwijderd', [
            'purchase_number' => $purchase->purchase_number
        ]);
    }

    /**
     * Log a field change with appropriate Dutch labels and formatting
     */
    private function logFieldChange(Purchase $purchase, string $field, $oldValue, $newValue): void
    {
        $fieldLabels = [
            'supplier_id' => 'Leverancier',
            'product_id' => 'Product',
            'quantity' => 'Hoeveelheid',
            'price' => 'Prijs',
            'total' => 'Totaal',
            'load_date' => 'Laaddatum',
            'delivery_date' => 'Losdatum',
            'supplier_purchase_number' => 'Leverancier inkoopnummer',
            'load_weight' => 'Laadgewicht',
            'unload_weight' => 'Losgewicht',
            'ready_for_planning' => 'Klaar voor planning',
            'completed' => 'Voltooid',
            'shipper_id' => 'Vervoerder',
            'administration' => 'Administratie',
            'sale_exact' => 'Verkoop exact',
            'exact' => 'Exact',
            'signature' => 'Handtekening',
            'signature_received_at' => 'Handtekening ontvangen op',
            'supplier_street' => 'Leverancier straat',
            'supplier_housenumber' => 'Leverancier huisnummer',
            'supplier_postal_code' => 'Leverancier postcode',
            'supplier_city' => 'Leverancier plaats',
            'supplier_country' => 'Leverancier land',
            'custom_supplier_address' => 'Aangepast leverancier adres',
            'comments' => 'Opmerkingen',
            'description' => 'Beschrijving',
            'qty_type' => 'Eenheid'
        ];

        $fieldLabel = $fieldLabels[$field] ?? ucfirst($field);
        $formattedOldValue = $this->formatValue($field, $oldValue, $purchase);
        $formattedNewValue = $this->formatValue($field, $newValue, $purchase);

        $message = "{$fieldLabel} gewijzigd van '{$formattedOldValue}' naar '{$formattedNewValue}'";
        
        $this->logChange($purchase, $message);
    }

    /**
     * Format values for display in logs
     */
    private function formatValue(string $field, $value, Purchase $purchase): string
    {
        if ($value === null) {
            return 'leeg';
        }

        switch ($field) {
            case 'load_date':
            case 'delivery_date':
                return $value ? Carbon::parse($value)->format('d-m-Y') : 'leeg';
            
            case 'signature_received_at':
                return $value ? Carbon::parse($value)->format('d-m-Y H:i') : 'leeg';
            
            case 'price':
            case 'total':
                return '€' . number_format((float)$value, 2);
            
            case 'load_weight':
            case 'unload_weight':
                return $value ? number_format((float)$value, 2) . ' kg' : 'leeg';
            
            case 'quantity':
                return $value . ' ' . ($purchase->qty_type ?? '');
            
            case 'ready_for_planning':
            case 'completed':
            case 'sale_exact':
            case 'exact':
            case 'custom_supplier_address':
                return $value ? 'ja' : 'nee';
            
            case 'supplier_id':
                if ($value) {
                    $supplier = \App\Models\Supplier::find($value);
                    return $supplier ? $supplier->company_name : "ID: {$value}";
                }
                return 'leeg';
            
            case 'product_id':
                if ($value) {
                    $product = \App\Models\Product::find($value);
                    return $product ? $product->name : "ID: {$value}";
                }
                return 'leeg';
            
            case 'shipper_id':
                if ($value) {
                    $shipper = \App\Models\Supplier::find($value);
                    return $shipper ? $shipper->company_name : "ID: {$value}";
                }
                return 'leeg';
            
            case 'signature':
                return $value ? 'Handtekening toegevoegd' : 'leeg';
            
            default:
                return (string)$value;
        }
    }

    /**
     * Add a log entry to the purchase
     */
    private function logChange(Purchase $purchase, string $message, array $details = []): void
    {
        $timestamp = Carbon::now()->format('d-m-Y H:i:s');
        $userName = Auth::user()->name ?? 'Systeem';
        
        $logEntry = "[{$timestamp}] {$userName}: {$message}";
        
        if (!empty($details)) {
            $detailsString = implode(', ', array_map(
                fn($key, $value) => "{$key}: {$value}",
                array_keys($details),
                $details
            ));
            $logEntry .= " ({$detailsString})";
        }

        $currentLog = $purchase->log ?: '';
        
        if ($currentLog) {
            $purchase->log = $currentLog . "\n" . $logEntry;
        } else {
            $purchase->log = $logEntry;
        }

        // Save without triggering the observer again
        $purchase->saveQuietly();
    }
}
