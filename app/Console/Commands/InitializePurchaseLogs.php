<?php

namespace App\Console\Commands;

use App\Models\Purchase;
use Carbon\Carbon;
use Illuminate\Console\Command;

class InitializePurchaseLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'purchase:init-logs {--dry-run : Show what would be updated without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Initialize log entries for existing purchases that have empty logs';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        
        $purchasesWithoutLogs = Purchase::whereNull('log')
            ->orWhere('log', '')
            ->with(['supplier', 'product', 'user'])
            ->get();

        if ($purchasesWithoutLogs->isEmpty()) {
            $this->info('All purchases already have log entries.');
            return 0;
        }

        $this->info("Found {$purchasesWithoutLogs->count()} purchases without log entries.");

        if ($isDryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
            $this->table(
                ['ID', 'Purchase Number', 'Supplier', 'Product', 'Created At'],
                $purchasesWithoutLogs->map(function ($purchase) {
                    return [
                        $purchase->id,
                        $purchase->purchase_number,
                        $purchase->supplier->company_name ?? 'Unknown',
                        $purchase->product->name ?? 'Unknown',
                        $purchase->created_at->format('d-m-Y H:i:s')
                    ];
                })->toArray()
            );
            return 0;
        }

        $bar = $this->output->createProgressBar($purchasesWithoutLogs->count());
        $bar->start();

        $updated = 0;

        foreach ($purchasesWithoutLogs as $purchase) {
            try {
                $createdAt = $purchase->created_at->format('d-m-Y H:i');
                $userName = $purchase->user->name ?? 'Systeem';
                $supplierName = $purchase->supplier->company_name ?? 'Onbekend';
                $productName = $purchase->product->name ?? 'Onbekend';

                $initialLog = "<li>{$createdAt} - {$userName}: Inkoop aangemaakt (purchase_number: {$purchase->purchase_number}, supplier: {$supplierName}, product: {$productName}, quantity: {$purchase->quantity} {$purchase->qty_type}, price: €" . number_format($purchase->price, 2) . ", total: €" . number_format($purchase->total, 2) . ")</li>";

                // Use saveQuietly to avoid triggering the observer
                $purchase->log = $initialLog;
                $purchase->saveQuietly();

                $updated++;
            } catch (\Exception $e) {
                $this->error("Failed to update purchase {$purchase->id}: " . $e->getMessage());
            }

            $bar->advance();
        }

        $bar->finish();
        $this->newLine();

        $this->info("Successfully initialized logs for {$updated} purchases.");

        return 0;
    }
}
