<?php

namespace App\Console\Commands;

use App\Models\Purchase;
use App\Models\ShippingAddress;
use App\Models\Supplier;
use App\Services\GeocodeService;
use Illuminate\Console\Command;

class GeocodeAddresses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'geocode:addresses {--type=all : Type of addresses to geocode (all, suppliers, purchases, shipping)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Geocode addresses in the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->option('type');
        $geocodeService = new GeocodeService();
        
        if ($type === 'all' || $type === 'suppliers') {
            $this->info('Geocoding supplier addresses...');
            $suppliers = Supplier::whereNull('latitude')->orWhereNull('longitude')->get();
            $bar = $this->output->createProgressBar(count($suppliers));
            
            foreach ($suppliers as $supplier) {
                $coordinates = $geocodeService->geocode(
                    $supplier->street_name,
                    $supplier->housenumber,
                    $supplier->postal_code,
                    $supplier->city,
                    $supplier->country
                );
                
                if ($coordinates) {
                    $supplier->latitude = $coordinates['latitude'];
                    $supplier->longitude = $coordinates['longitude'];
                    $supplier->save();
                }
                
                $bar->advance();
                // Add a small delay to avoid hitting rate limits
                usleep(500000); // 0.5 seconds
            }
            
            $bar->finish();
            $this->newLine();
            $this->info('Supplier geocoding completed.');
        }
        
        if ($type === 'all' || $type === 'purchases') {
            $this->info('Geocoding custom supplier addresses in purchases...');
            $purchases = Purchase::where('custom_supplier_address', true)
                ->whereNull('supplier_latitude')
                ->orWhereNull('supplier_longitude')
                ->get();
            $bar = $this->output->createProgressBar(count($purchases));
            
            foreach ($purchases as $purchase) {
                $coordinates = $geocodeService->geocode(
                    $purchase->supplier_street,
                    $purchase->supplier_housenumber,
                    $purchase->supplier_postal_code,
                    $purchase->supplier_city,
                    $purchase->supplier_country
                );
                
                if ($coordinates) {
                    $purchase->supplier_latitude = $coordinates['latitude'];
                    $purchase->supplier_longitude = $coordinates['longitude'];
                    $purchase->save();
                }
                
                $bar->advance();
                usleep(500000); // 0.5 seconds
            }
            
            $bar->finish();
            $this->newLine();
            $this->info('Purchase custom supplier address geocoding completed.');
        }
        
        if ($type === 'all' || $type === 'shipping') {
            $this->info('Geocoding shipping addresses...');
            $addresses = ShippingAddress::whereNull('latitude')->orWhereNull('longitude')->get();
            $bar = $this->output->createProgressBar(count($addresses));
            
            foreach ($addresses as $address) {
                $coordinates = $geocodeService->geocode(
                    $address->street,
                    $address->housenumber,
                    $address->postal_code,
                    $address->city,
                    $address->country
                );
                
                if ($coordinates) {
                    $address->latitude = $coordinates['latitude'];
                    $address->longitude = $coordinates['longitude'];
                    $address->save();
                }
                
                $bar->advance();
                usleep(500000); // 0.5 seconds
            }
            
            $bar->finish();
            $this->newLine();
            $this->info('Shipping address geocoding completed.');
        }
        
        $this->info('All geocoding tasks completed.');
        
        return Command::SUCCESS;
    }
}
