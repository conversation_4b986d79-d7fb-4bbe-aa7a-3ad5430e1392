<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class CheckTwoFactorStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auth:check-2fa {--show-users : Show users without 2FA enabled}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check two-factor authentication status for all users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $totalUsers = User::count();
        $usersWithout2FA = User::whereNull('two_factor_secret')
            ->orWhereNull('two_factor_confirmed_at')
            ->count();
        $usersWith2FA = $totalUsers - $usersWithout2FA;

        $this->info("Two-Factor Authentication Status Report");
        $this->info("=====================================");
        $this->info("Total users: {$totalUsers}");
        $this->info("Users with 2FA enabled: {$usersWith2FA}");
        $this->info("Users without 2FA: {$usersWithout2FA}");

        if ($usersWithout2FA > 0) {
            $this->warn("\n⚠️  {$usersWithout2FA} users do not have 2FA enabled and will be blocked from accessing the application.");
        } else {
            $this->info("\n✅ All users have 2FA enabled.");
        }

        if ($this->option('show-users') && $usersWithout2FA > 0) {
            $this->info("\nUsers without 2FA:");
            $this->info("==================");
            
            $users = User::whereNull('two_factor_secret')
                ->orWhereNull('two_factor_confirmed_at')
                ->get(['id', 'name', 'email']);

            $this->table(
                ['ID', 'Name', 'Email'],
                $users->map(function ($user) {
                    return [$user->id, $user->name, $user->email];
                })->toArray()
            );

            $this->info("\nThese users will need to enable 2FA in their profile settings to access the application.");
        }

        return 0;
    }
}
