<?php

namespace App\Console\Commands;

use App\Mail\DailySalesSummary;
use App\Models\Purchase;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendDailySalesSummary extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:daily-sales-summary {--date= : Specific date to generate summary for (Y-m-d format)} {--purchase= : Test with specific purchase number}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send daily sales summary emails to all active users (use --purchase=P001 for testing with specific purchase)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting daily sales summary email process...');

        // Check if testing with specific purchase number
        $purchaseNumber = $this->option('purchase');

        if ($purchaseNumber) {
            return $this->handleTestMode($purchaseNumber);
        }

        // Get the date for the summary (yesterday by default, or specified date)
        $summaryDate = $this->option('date')
            ? Carbon::createFromFormat('Y-m-d', $this->option('date'))
            : Carbon::yesterday();

        $this->info("Generating summary for date: {$summaryDate->format('Y-m-d')}");

        // Get all active users who have created sales
        $activeUsers = User::whereHas('sales')
            ->where('email', '!=', null)
            ->where('email', '!=', '')
            ->get();

        $this->info("Found {$activeUsers->count()} active users with sales history");

        $successCount = 0;
        $errorCount = 0;

        foreach ($activeUsers as $user) {
            try {
                $this->info("Processing user: {$user->name} ({$user->email})");

                // Get completed purchases from the summary date with user's sales
                $completedPurchases = $this->getCompletedPurchasesForUser($user, $summaryDate);

                $this->info("Found {$completedPurchases->count()} completed purchases with user's sales");

                // Send email to user
                Mail::to($user->email)->send(new DailySalesSummary($user, $completedPurchases, $summaryDate));

                $successCount++;
                $this->info("✓ Email sent successfully to {$user->name}");

                // Log successful email
                Log::info("Daily sales summary email sent successfully", [
                    'user_id' => $user->id,
                    'user_email' => $user->email,
                    'summary_date' => $summaryDate->format('Y-m-d'),
                    'purchases_count' => $completedPurchases->count()
                ]);

            } catch (\Exception $e) {
                $errorCount++;
                $this->error("✗ Failed to send email to {$user->name}: {$e->getMessage()}");

                // Log error
                Log::error("Failed to send daily sales summary email", [
                    'user_id' => $user->id,
                    'user_email' => $user->email,
                    'summary_date' => $summaryDate->format('Y-m-d'),
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }

        // Summary
        $this->info("\n" . str_repeat('=', 50));
        $this->info("Daily Sales Summary Email Process Complete");
        $this->info("Date: {$summaryDate->format('Y-m-d')}");
        $this->info("Total users processed: {$activeUsers->count()}");
        $this->info("Successful emails: {$successCount}");
        $this->info("Failed emails: {$errorCount}");
        $this->info(str_repeat('=', 50));

        // Log summary
        Log::info("Daily sales summary email process completed", [
            'summary_date' => $summaryDate->format('Y-m-d'),
            'total_users' => $activeUsers->count(),
            'successful_emails' => $successCount,
            'failed_emails' => $errorCount
        ]);

        return $errorCount === 0 ? 0 : 1;
    }

    /**
     * Handle test mode with specific purchase number
     */
    private function handleTestMode(string $purchaseNumber)
    {
        $this->info("🧪 TEST MODE: Using purchase number {$purchaseNumber}");

        // Find the purchase by purchase number
        $purchase = Purchase::with(['supplier', 'product', 'sales.customer', 'sales.shippingAddress', 'sales.user'])
            ->where('purchase_number', $purchaseNumber)
            ->first();

        if (!$purchase) {
            $this->error("❌ Purchase with number '{$purchaseNumber}' not found!");
            return 1;
        }

        $this->info("✓ Found purchase: {$purchase->purchase_number}");
        $this->info("  - Supplier: " . ($purchase->supplier->company_name ?? 'Unknown'));
        $this->info("  - Product: " . ($purchase->product->name ?? 'Unknown'));
        $this->info("  - Completed: " . ($purchase->completed ? 'Yes' : 'No'));
        $this->info("  - Total Sales: {$purchase->sales->count()}");

        if ($purchase->sales->isEmpty()) {
            $this->warn("⚠️  This purchase has no sales associated with it.");
            return 0;
        }

        // Get unique users who created sales for this purchase
        $usersWithSales = $purchase->sales->groupBy('user_id')->keys();
        $this->info("  - Users with sales: {$usersWithSales->count()}");

        $successCount = 0;
        $errorCount = 0;

        foreach ($usersWithSales as $userId) {
            $user = User::find($userId);

            if (!$user || !$user->email) {
                $this->warn("⚠️  Skipping user ID {$userId} - no valid email");
                continue;
            }

            try {
                $this->info("📧 Sending test email to: {$user->name} ({$user->email})");

                // Create a collection with just this purchase for the user
                $testPurchase = clone $purchase;
                $testPurchase->userSales = $purchase->sales->where('user_id', $userId);
                $completedPurchases = collect([$testPurchase]);

                // Use today as summary date for testing
                $summaryDate = Carbon::today();

                // Send email
                Mail::to($user->email)->send(new DailySalesSummary($user, $completedPurchases, $summaryDate));

                $successCount++;
                $this->info("✅ Test email sent successfully to {$user->name}");

                // Show user's sales details
                $userSales = $purchase->sales->where('user_id', $userId);
                $this->info("   Sales for this user:");
                foreach ($userSales as $sale) {
                    $this->info("   - Customer: " . ($sale->customer->company_name ?? 'Unknown'));
                    $this->info("     Quantity: {$sale->quantity}, Price: €{$sale->price}, Transport: €{$sale->transport_cost}");
                }

            } catch (\Exception $e) {
                $errorCount++;
                $this->error("❌ Failed to send test email to {$user->name}: {$e->getMessage()}");

                Log::error("Failed to send test daily sales summary email", [
                    'user_id' => $user->id,
                    'user_email' => $user->email,
                    'purchase_number' => $purchaseNumber,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Test summary
        $this->info("\n" . str_repeat('=', 50));
        $this->info("🧪 TEST MODE SUMMARY");
        $this->info("Purchase: {$purchaseNumber}");
        $this->info("Users processed: " . $usersWithSales->count());
        $this->info("Successful emails: {$successCount}");
        $this->info("Failed emails: {$errorCount}");
        $this->info(str_repeat('=', 50));

        return $errorCount === 0 ? 0 : 1;
    }

    /**
     * Get completed purchases for a specific user and date
     */
    private function getCompletedPurchasesForUser(User $user, Carbon $summaryDate): \Illuminate\Database\Eloquent\Collection
    {
        return Purchase::with([
            'supplier',
            'product',
            'sales' => function ($query) use ($user) {
                $query->where('user_id', $user->id)
                      ->with(['customer', 'shippingAddress']);
            }
        ])
        ->where('completed', true)
        ->whereDate('updated_at', $summaryDate->format('Y-m-d'))
        ->whereHas('sales', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })
        ->get()
        ->map(function ($purchase) use ($user) {
            // Add a filtered sales collection for the user
            $purchase->userSales = $purchase->sales->where('user_id', $user->id);
            return $purchase;
        });
    }
}
