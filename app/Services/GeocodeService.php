<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GeocodeService
{
    /**
     * Geocode an address to get latitude and longitude
     *
     * @param string $street Street name
     * @param string $housenumber House number
     * @param string $postalCode Postal code
     * @param string $city City
     * @param string $country Country
     * @return array|null Returns [latitude, longitude] or null if geocoding failed
     */
    public function geocode(string $street, string $housenumber, string $postalCode, string $city, string $country = 'NL'): ?array
    {
        try {
            // Format the address for the geocoding API
            $address = urlencode("{$street} {$housenumber}, {$postalCode} {$city}, {$country}");
            
            // Using OpenStreetMap Nominatim API (free and doesn't require API key)
            $response = Http::get("https://nominatim.openstreetmap.org/search?format=json&q={$address}&limit=1");
            
            if ($response->successful() && count($response->json()) > 0) {
                $result = $response->json()[0];
                return [
                    'latitude' => (float) $result['lat'],
                    'longitude' => (float) $result['lon']
                ];
            }
            
            return null;
        } catch (\Exception $e) {
            Log::error('Geocoding error: ' . $e->getMessage());
            return null;
        }
    }
}
