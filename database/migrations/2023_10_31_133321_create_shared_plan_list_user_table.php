<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shared_plan_list_user', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('shared_plan_list_id');
            $table->unsignedBigInteger('user_id');
            $table->timestamps();

            $table->foreign('shared_plan_list_id')->references('id')->on('shared_plan_lists')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shared_plan_list_user');
    }
};
