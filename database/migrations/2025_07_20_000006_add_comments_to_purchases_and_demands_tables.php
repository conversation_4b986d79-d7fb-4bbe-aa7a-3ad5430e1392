<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('purchases', function (Blueprint $table) {
            $table->text('comments')->nullable()->after('supplier_purchase_number');
        });

        Schema::table('demands', function (Blueprint $table) {
            $table->text('comments')->nullable()->after('customer_purchase_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('purchases', function (Blueprint $table) {
            $table->dropColumn('comments');
        });

        Schema::table('demands', function (Blueprint $table) {
            $table->dropColumn('comments');
        });
    }
};
