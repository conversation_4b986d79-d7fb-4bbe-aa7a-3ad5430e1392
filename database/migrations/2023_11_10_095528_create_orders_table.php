<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('customer_id');
            $table->unsignedInteger('user_id');
            $table->unsignedInteger('shipping_address_id');
            $table->float('cost')->default(0.00);
            $table->float('total_ex_vat')->default(0.00);
            $table->float('total_incl_vat')->default(0.00);
            $table->float('vat')->default(0.00);
            $table->float('transport_cost')->default(0.00);
            $table->float('transport_price')->default(0.00);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
