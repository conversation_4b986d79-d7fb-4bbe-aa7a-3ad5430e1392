<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add geolocation to suppliers
        Schema::table('suppliers', function (Blueprint $table) {
            $table->decimal('latitude', 10, 7)->nullable();
            $table->decimal('longitude', 10, 7)->nullable();
        });

        // Add geolocation to purchases (for custom supplier addresses)
        Schema::table('purchases', function (Blueprint $table) {
            $table->decimal('supplier_latitude', 10, 7)->nullable();
            $table->decimal('supplier_longitude', 10, 7)->nullable();
        });

        // Add geolocation to shipping addresses (delivery addresses)
        Schema::table('shipping_addresses', function (Blueprint $table) {
            $table->decimal('latitude', 10, 7)->nullable();
            $table->decimal('longitude', 10, 7)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('suppliers', function (Blueprint $table) {
            $table->dropColumn(['latitude', 'longitude']);
        });

        Schema::table('purchases', function (Blueprint $table) {
            $table->dropColumn(['supplier_latitude', 'supplier_longitude']);
        });

        Schema::table('shipping_addresses', function (Blueprint $table) {
            $table->dropColumn(['latitude', 'longitude']);
        });
    }
};
