<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customer_products', function (Blueprint $table) {
            $table->string('unit_type')->default('TON')->after('unit_price');
        });

        Schema::table('supplier_products', function (Blueprint $table) {
            $table->string('unit_type')->default('TON')->after('unit_price');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customer_products', function (Blueprint $table) {
            $table->dropColumn('unit_type');
        });

        Schema::table('supplier_products', function (Blueprint $table) {
            $table->dropColumn('unit_type');
        });
    }
};
