<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('purchases', function (Blueprint $table) {
            $table->string('supplier_street')->nullable();
            $table->string('supplier_housenumber')->nullable();
            $table->string('supplier_postal_code')->nullable();
            $table->string('supplier_city')->nullable();
            $table->string('supplier_country')->nullable();
            $table->boolean('custom_supplier_address')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('purchases', function (Blueprint $table) {
            $table->dropColumn([
                'supplier_street',
                'supplier_housenumber',
                'supplier_postal_code',
                'supplier_city',
                'supplier_country',
                'custom_supplier_address'
            ]);
        });
    }
};
