<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Manual step required: Run these SQL commands in your database:
        // ALTER TABLE supplier_products DROP INDEX supplier_products_supplier_id_product_id_unique;
        // ALTER TABLE customer_products DROP INDEX customer_products_customer_id_product_id_unique;

        echo "Please run the following SQL commands manually in your database:\n";
        echo "ALTER TABLE supplier_products DROP INDEX supplier_products_supplier_id_product_id_unique;\n";
        echo "ALTER TABLE customer_products DROP INDEX customer_products_customer_id_product_id_unique;\n";
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Re-add unique constraints
        // Note: This will only work if there are no duplicate entries
        try {
            Schema::table('supplier_products', function (Blueprint $table) {
                $table->unique(['supplier_id', 'product_id']);
            });
        } catch (\Exception $e) {
            // If constraint can't be added due to duplicates, log it
            \Log::warning('Could not re-add unique constraint to supplier_products: ' . $e->getMessage());
        }

        try {
            Schema::table('customer_products', function (Blueprint $table) {
                $table->unique(['customer_id', 'product_id']);
            });
        } catch (\Exception $e) {
            // If constraint can't be added due to duplicates, log it
            \Log::warning('Could not re-add unique constraint to customer_products: ' . $e->getMessage());
        }
    }

    /**
     * Drop unique constraint if it exists
     */
    private function dropUniqueConstraintIfExists($tableName, $constraintName)
    {
        // Check if the constraint exists
        $constraintExists = DB::select("
            SELECT CONSTRAINT_NAME 
            FROM information_schema.TABLE_CONSTRAINTS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = ? 
            AND CONSTRAINT_NAME = ?
        ", [$tableName, $constraintName]);

        if (!empty($constraintExists)) {
            try {
                DB::statement("ALTER TABLE {$tableName} DROP INDEX {$constraintName}");
                echo "Dropped unique constraint {$constraintName} from {$tableName}\n";
            } catch (\Exception $e) {
                echo "Could not drop constraint {$constraintName} from {$tableName}: " . $e->getMessage() . "\n";
                
                // Try alternative approach - check if it's a unique key vs index
                try {
                    DB::statement("ALTER TABLE {$tableName} DROP KEY {$constraintName}");
                    echo "Dropped unique key {$constraintName} from {$tableName}\n";
                } catch (\Exception $e2) {
                    echo "Could not drop key {$constraintName} from {$tableName}: " . $e2->getMessage() . "\n";
                }
            }
        } else {
            echo "Unique constraint {$constraintName} does not exist on {$tableName}\n";
        }
    }
};
