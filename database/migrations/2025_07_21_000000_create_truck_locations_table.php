<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('truck_locations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable(); // Driver ID if authenticated
            $table->string('device_id')->nullable(); // Device identifier if no user
            $table->decimal('latitude', 10, 7);
            $table->decimal('longitude', 10, 7);
            $table->bigInteger('timestamp'); // Unix timestamp in milliseconds
            $table->decimal('accuracy', 8, 2)->nullable(); // Accuracy in meters
            $table->decimal('altitude', 8, 2)->nullable(); // Altitude in meters
            $table->decimal('speed', 8, 2)->nullable(); // Speed in meters/second
            $table->decimal('heading', 5, 2)->nullable(); // Direction in degrees
            $table->timestamps();
            
            // Index for faster queries
            $table->index(['user_id', 'timestamp']);
            $table->index(['device_id', 'timestamp']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('truck_locations');
    }
};
