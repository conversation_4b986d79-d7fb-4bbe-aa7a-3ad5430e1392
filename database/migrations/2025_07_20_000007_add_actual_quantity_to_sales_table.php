<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sales', function (Blueprint $table) {
            $table->float('original_quantity')->nullable()->after('quantity');
            $table->float('actual_quantity')->nullable()->after('original_quantity');
        });

        // Copy existing quantity values to original_quantity for existing records
        DB::statement('UPDATE sales SET original_quantity = quantity WHERE original_quantity IS NULL');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sales', function (Blueprint $table) {
            $table->dropColumn(['original_quantity', 'actual_quantity']);
        });
    }
};
