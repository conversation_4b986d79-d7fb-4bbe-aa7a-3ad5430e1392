<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Backup existing data
        $supplierProducts = DB::table('supplier_products')->get();
        $customerProducts = DB::table('customer_products')->get();

        // Drop and recreate supplier_products table without unique constraint
        Schema::dropIfExists('supplier_products');
        Schema::create('supplier_products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('supplier_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->decimal('unit_price', 10, 2); // Unit price per product
            $table->string('unit_type')->default('TON');
            $table->date('end_date')->nullable();
            $table->integer('default_qty')->nullable();
            $table->timestamps();
            
            // No unique constraint - allow multiple price agreements
        });

        // Drop and recreate customer_products table without unique constraint
        Schema::dropIfExists('customer_products');
        Schema::create('customer_products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->decimal('unit_price', 10, 2); // Unit price per product
            $table->string('unit_type')->default('TON');
            $table->date('end_date')->nullable();
            $table->integer('default_qty')->nullable();
            $table->timestamps();
            
            // No unique constraint - allow multiple price agreements
        });

        // Restore data
        foreach ($supplierProducts as $record) {
            DB::table('supplier_products')->insert((array) $record);
        }

        foreach ($customerProducts as $record) {
            DB::table('customer_products')->insert((array) $record);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Backup existing data
        $supplierProducts = DB::table('supplier_products')->get();
        $customerProducts = DB::table('customer_products')->get();

        // Drop and recreate supplier_products table with unique constraint
        Schema::dropIfExists('supplier_products');
        Schema::create('supplier_products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('supplier_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->decimal('unit_price', 10, 2); // Unit price per product
            $table->string('unit_type')->default('TON');
            $table->date('end_date')->nullable();
            $table->integer('default_qty')->nullable();
            $table->timestamps();
            
            // Add unique constraint back
            $table->unique(['supplier_id', 'product_id']);
        });

        // Drop and recreate customer_products table with unique constraint
        Schema::dropIfExists('customer_products');
        Schema::create('customer_products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->decimal('unit_price', 10, 2); // Unit price per product
            $table->string('unit_type')->default('TON');
            $table->date('end_date')->nullable();
            $table->integer('default_qty')->nullable();
            $table->timestamps();
            
            // Add unique constraint back
            $table->unique(['customer_id', 'product_id']);
        });

        // Restore data (only unique combinations will be restored)
        $uniqueSupplierProducts = collect($supplierProducts)->unique(function ($item) {
            return $item->supplier_id . '-' . $item->product_id;
        });

        $uniqueCustomerProducts = collect($customerProducts)->unique(function ($item) {
            return $item->customer_id . '-' . $item->product_id;
        });

        foreach ($uniqueSupplierProducts as $record) {
            DB::table('supplier_products')->insert((array) $record);
        }

        foreach ($uniqueCustomerProducts as $record) {
            DB::table('customer_products')->insert((array) $record);
        }
    }
};
