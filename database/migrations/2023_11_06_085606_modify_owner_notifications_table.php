<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('owner_notifications', function (Blueprint $table) {
            $table->unsignedBigInteger('notifiable_id')->nullable()->change();
            $table->string('notifiable_type')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('owner_notifications', function (Blueprint $table) {
            //
        });
    }
};
