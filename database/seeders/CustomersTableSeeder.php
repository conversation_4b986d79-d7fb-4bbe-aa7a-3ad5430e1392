<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Seeder;
use Faker\Factory as Faker;

class CustomersTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = Faker::create('nl_NL');

        foreach(range(1, 100) as $index)
        {
            DB::table('customers')->insert([
                'company_name' => $faker->company,
                'name' => $faker->name,
                'street_name' => $faker->streetName,
                'housenumber' => $faker->buildingNumber,
                'postal_code' => $faker->postcode,
                'city' => $faker->city,
                'country' => 'NL',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
