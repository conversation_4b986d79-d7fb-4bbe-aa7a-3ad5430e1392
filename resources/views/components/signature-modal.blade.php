@props(['purchase'])

@if($purchase->signature)
<div x-data="{ showSignature: false }" class="inline-block">
    <!-- Trigger Button -->
    <button @click="showSignature = true" 
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 hover:bg-green-200 transition-colors duration-150">
        <i class="fas fa-signature mr-1"></i>
        Bekijk Handtekening
    </button>

    <!-- Modal -->
    <div x-show="showSignature" 
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-50 overflow-y-auto" 
         style="display: none;">
        
        <!-- Background overlay -->
        <div class="fixed inset-0 bg-black bg-opacity-50" @click="showSignature = false"></div>
        
        <!-- Modal content -->
        <div class="flex items-center justify-center min-h-screen p-4">
            <div x-show="showSignature"
                 x-transition:enter="ease-out duration-300"
                 x-transition:enter-start="opacity-0 transform scale-95"
                 x-transition:enter-end="opacity-100 transform scale-100"
                 x-transition:leave="ease-in duration-200"
                 x-transition:leave-start="opacity-100 transform scale-100"
                 x-transition:leave-end="opacity-0 transform scale-95"
                 class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
                
                <!-- Modal Header -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-signature text-blue-600 mr-2"></i>
                        Digitale Handtekening - {{ $purchase->purchase_number }}
                    </h3>
                    <button @click="showSignature = false" 
                            class="text-gray-400 hover:text-gray-600 transition-colors duration-150">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                
                <!-- Modal Body -->
                <div class="p-6">
                    <div class="space-y-4">
                        <!-- Signature Image -->
                        <div class="border-2 border-gray-200 rounded-lg p-4 bg-gray-50 text-center">
                            <img src="{{ $purchase->signature }}" 
                                 alt="Digitale Handtekening" 
                                 class="max-w-full h-auto border border-gray-300 rounded bg-white shadow-sm mx-auto"
                                 style="max-height: 300px;">
                        </div>
                        
                        <!-- Signature Details -->
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Ontvangen:</span>
                                    <span class="font-medium">
                                        {{ $purchase->signature_received_at ? $purchase->signature_received_at->format('d-m-Y H:i') : 'Onbekend' }}
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Status:</span>
                                    <span class="text-green-600 font-medium">Geldig</span>
                                </div>
                            </div>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Aankoop:</span>
                                    <span class="font-medium">{{ $purchase->purchase_number }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Leverancier:</span>
                                    <span class="font-medium">{{ $purchase->supplier->company_name ?? 'Onbekend' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Modal Footer -->
                <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
                    <button @click="showSignature = false" 
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-150">
                        Sluiten
                    </button>
                    <button onclick="downloadSignatureModal('{{ $purchase->signature }}', '{{ $purchase->purchase_number }}')"
                            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 transition-colors duration-150">
                        <i class="fas fa-download mr-2"></i>
                        Download
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function downloadSignatureModal(signatureData, purchaseNumber) {
    const link = document.createElement('a');
    link.href = signatureData;
    link.download = `handtekening-${purchaseNumber}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
</script>
@endif
