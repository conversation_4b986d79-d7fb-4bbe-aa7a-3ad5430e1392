<div>
  <div class="flex gap-2 mb-4 flex-wrap">
      @foreach($productGroups as $productGroup)
        <div class="bg-indigo-300 rounded-3 cursor-pointer text-white relative" wire:click="editProductGroup({{ $productGroup->id }})" style="padding-left: 7px; padding-top: 5px; padding-bottom: 5px; padding-right: 25px;">
          {{ $productGroup->name }}
          <i class="fa-solid fa-trash-alt text-black absolute cursor-pointer" wire:click.stop="confirmDelete({{ $productGroup->id }})" style="top: 5px; right: 5px"></i>
        </div>
      @endforeach
  </div>
  <x-button wire:click="$set('showModal', true)"><i class="fa-solid fa-plus" style="color: #ffffff; margin-right: 4px;"></i> Groep toevoegen</x-button>

  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Product groep toevoegen
    </x-slot>

    <x-slot name="content">
      <div class="flex items-center mb-6">
        <div class="w-1/3">
          <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="name" value="{{ __('Naam') }}" />
        </div>
        <div class="w-2/3">
          <x-input id="name" class="block mt-1 w-full" type="text" name="company_name" wire:model.live="name" />
          @error('name') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
        </div>
      </div>
    </x-slot>

    <x-slot name="footer">
      <x-button wire:click="saveProductGroup" class="ml-2">Opslaan</x-button>
    </x-slot>
  </x-dialog-modal>

  <x-dialog-modal wire:model.live="showDeleteModal">
    <x-slot name="title">
      Weet je het zeker?
    </x-slot>

    <x-slot name="content">
      Deze productgroep wordt hiermee verwijdert.
    </x-slot>

    <x-slot name="footer">
      <x-button wire:click="deleteProductGroup" class="text-red-500">Verwijder</x-button>
    </x-slot>
  </x-dialog-modal>

</div>
