@php
  use Illuminate\Support\Str;
@endphp
<div>
  <div class="flex justify-between">
    <h4>A<PERSON>bod</h4>
    @if(count($selectedPurchases) > 0)
      <div class="flex gap-4 items-center">
        <div>
          <label class="block text-sm font-medium text-gray-700">Laaddatum</label>
          <input
            type="date"
            class="mt-1 border-gray-300 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 rounded-md shadow-sm"
            wire:model="tempLoadDate"
          >
        </div>
        <div class="flex gap-2">
          <x-button wire:click="updateSelectedLoadDates">
            Update
          </x-button>
          <x-button class="bg-red-500 hover:bg-red-700" wire:click="deleteSelected">
            Delete
          </x-button>
        </div>
      </div>
    @endif
    <x-button wire:click="$set('showModal', true)"><i class="fa-solid fa-plus" style="color: #ffffff; margin-right: 4px;"></i> Aanbod toevoegen</x-button>
  </div>

  <table class="w-full border-separate border-spacing-y-1">
    <thead>
      <tr>
        <th class="text-left p-2"></th>
        <th class="text-left p-2">Ritnummer</th>
        <th class="text-left p-2">Leverancier</th>
        <th class="text-left p-2">Leverancier inkoopnr.</th>
        <th class="text-left p-2">Product</th>
        <th class="text-left p-2">Aantal</th>
        <th class="text-left p-2">Laden</th>
        <th class="text-left p-2">Opmerkingen</th>
      </tr>
    </thead>
    <tbody>
      @foreach($purchases as $purchase)
        <tr
          wire:click="selectPurchase({{ $purchase->id }})"
          class="cursor-pointer {{ $selectedPurchase && $selectedPurchase->id === $purchase->id ? 'bg-green-200' : 'bg-gray-200' }} rounded"
        >
          <td class="p-2" onclick="event.stopPropagation()">
            <input type="checkbox"
                   wire:model.live="selectedPurchases"
                   value="{{ $purchase->id }}"
            >
          </td>
          <td class="p-2">{{ $purchase->purchase_number }}</td>
          <td class="p-2">{{ $purchase->supplier->company_name }}</td>
          <td class="p-2">{{ $purchase->supplier_purchase_number ?: '-' }}</td>
          <td class="p-2">{{ $purchase->product->sku }} - {{ $purchase->product->name }}</td>
          <td class="p-2">{{ $purchase->quantity }} {{ $purchase->qty_type }}</td>
          <td class="p-2">{{ \Carbon\Carbon::parse($purchase->load_date)->format('d-m-Y') }}</td>
          <td class="p-2">{{ $purchase->comments ? Str::limit($purchase->comments, 50) : '-' }}</td>
        </tr>
      @endforeach
    </tbody>
  </table>
  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Aanbod toevoegen
    </x-slot>
    <x-slot name="content">
      <div class="mb-4">
        <x-label for="product" value="Administratie" />
        <select id="type" class="form-input rounded-md shadow-sm mt-1 block w-49" wire:model.live="administration">
          <option value="">Selecteer administratie</option>
          <option value="030-Fourgahandel Bus">030 Fouragehandel Bus</option>
          <option value="033-Logi.Span Resthout">033 Logi.Span Resthout</option>
          <option value="036-Logi.Span Retail">036 Logi.Span Retail</option>
          <option value="037-Logi.Span Biomass">037 Logi.Span Biomass</option>
        </select>
      </div>
      <!-- Selecteer Supplier -->
      <div class="mb-4">
        <strong>Leverancier</strong><br>
        @if(!$selectedSupplier)
          <input
                  type="text"
                  wire:model.live.debounce.500ms="search"
                  placeholder="Zoek leverancier..."
                  style="margin-top: .25rem"
          />
          @unless($suppliers->isEmpty())
            <div class="bg-gray-100" style="max-height: 400px; overflow-x: scroll; border: 1px solid #4b5563">
              @foreach($suppliers as $supplier)
                <p
                        wire:click="selectSupplier({{ $supplier->id }})"
                        class="cursor-pointer hover:bg-gray-50 py-2 px-2"
                        style="margin-bottom: 0px !important;"
                >
                  {{ $supplier->company_name }} ({{$supplier->exact_id}})
                </p>
              @endforeach
            </div>
          @endunless
        @endif
        @if(!empty($selectedSupplier))
          <div class="flex justify-between items-center mb-2">
            <strong>Laadadres</strong>
            <x-button wire:click="$set('showAddressModal', true)" class="text-xs px-2 py-1">
              Adresboek
            </x-button>
          </div>

          @if($selectedAddressId && count($supplierAddresses) > 0)
            @php
              $address = collect($supplierAddresses)->firstWhere('id', $selectedAddressId);
            @endphp
            @if($address)
              <div id="billing mb-4" class="p-3 border border-gray-300 rounded-md mb-3">
                <div class="font-semibold">{{ $address->name }}</div>
                {{ $selectedSupplier->exact_id }}<br>
                {{ $selectedSupplier->company_name }}<br>
                {{ $address->street }} {{ $address->housenumber }}<br>
                {{ $address->postal_code }} {{ $address->city }}<br>
                {{ $selectedSupplier->email }}
              </div>
            @endif
          @else
            <div id="billing mb-4" class="p-3 border border-gray-300 rounded-md mb-3">
              {{ $selectedSupplier->exact_id }}<br>
              {{ $selectedSupplier->company_name }}<br>
              {{ $selectedSupplier->street_name }} {{ $selectedSupplier->housenumber }}<br>
              {{ $selectedSupplier->postal_code }} {{ $selectedSupplier->city }}<br>
              {{ $selectedSupplier->email }}
            </div>
          @endif

          <div class="mt-4 mb-4">
            <x-label for="loadingTimes" value="Laadtijden" />
            <textarea
              id="loadingTimes"
              class="block mt-1 w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
              wire:model.live="loadingTimes"
              rows="3"
              placeholder="Voer laadtijden in (bijv. Ma-Vr: 08:00-16:00)"
            ></textarea>
            <p class="mt-1 text-sm text-gray-500">Deze informatie wordt opgeslagen bij het geselecteerde adres en is beschikbaar voor toekomstige aankopen.</p>
          </div>
        @endif
      </div>
      <!-- End Selecteer Supplier -->
      <!-- Selecteer Product -->
      @if (!empty($selectedSupplier))
      <div class="mb-4">
        <x-label for="product" value="Product" />
        <div class="@if(!empty($selectedProduct)) hidden @endif">
          <div class="flex gap-2">
            <select
              id="product"
              wire:model.live="selectedProductId"
              wire:change="selectProduct($event.target.value)"
              class="block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
            >
              <option value="">-- Selecteer een product --</option>
              @if($supplierProducts && count($supplierProducts) > 0)
                @foreach($supplierProducts as $index => $product)
                  <option value="{{ $index }}">
                    {{ $product['sku'] }} - {{ $product['name'] }}
                    @if(isset($product['pivot']) && isset($product['pivot']['unit_price']))
                      - €{{ number_format($product['pivot']['unit_price'], 2, ',', '.') }}
                    @endif
                    @if(isset($product['pivot']) && isset($product['pivot']['end_date']))
                      ({{ \Carbon\Carbon::parse($product['pivot']['end_date'])->format('d-m-Y') }})
                    @endif
                  </option>
                @endforeach
              @else
                <option value="" disabled>Geen producten met prijzen gevonden voor deze leverancier</option>
              @endif
            </select>

            @if($selectedSupplier)
              <button
                type="button"
                wire:click="openAddProductModal"
                class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:border-indigo-700 focus:shadow-outline-indigo active:bg-indigo-700 transition ease-in-out duration-150"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Product toevoegen
              </button>
            @endif
          </div>

          @if($selectedSupplier && empty($supplierProducts))
            <div class="mt-2 p-3 bg-yellow-100 text-yellow-800 rounded-md">
              <p>Er zijn geen producten met prijzen gevonden voor deze leverancier.</p>
              <p class="mt-1">
                <a href="{{ route('supplier.edit', $selectedSupplier->id) }}" class="text-blue-600 hover:underline">
                  Klik hier om producten en prijzen toe te voegen aan deze leverancier
                </a>
              </p>
            </div>
          @endif
        </div>
        @if(!empty($selectedProduct))
          {{ $selectedProduct->sku ?? 'Unknown' }} - {{ $selectedProduct->name ?? 'Unknown' }}
        @endif
      </div>
      @if(!empty($selectedProduct))
        <div class="mb-4">
          <x-label for="aantal" value="Aantal {{ $purchaseQtyType }}" />
          <div class="flex gap-4">
            @if($purchaseQtyType === 'VOLLE VRACHT' || $purchaseQtyType === 'HALVE VRACHT')
              <div class="mt-1 text-sm text-gray-500">
                Aantal is altijd 1 voor {{ $purchaseQtyType }}
              </div>
            @else
              <x-input
                id="aantal"
                class="block mt-1 w-1/3"
                type="text"
                wire:model.live="purchaseQty"
              />
            @endif
          </div>
        </div>
        <div class="mb-4">
          <x-label for="price" value="Inkoopprijs per {{$purchaseQtyType}}" />
          <x-input id="price" class="block mt-1 w-1/4" type="text" wire:model.live="purchasePrice" />
          @if($message)
            <small>{{ $message }}</small>
          @endif
        </div>
        <div class="mb-4">
          <x-label for="laaddatum" value="Laden" />
          <x-input id="laaddatum" class="block mt-1 w-25" type="date" wire:model.live="loadDate" />
        </div>
        <div class="mb-4">
          <x-label for="supplierPurchaseNumber" value="Leverancier inkoopnummer" />
          <x-input id="supplierPurchaseNumber" class="block mt-1 w-full" type="text" wire:model.live="supplierPurchaseNumber" placeholder="Optioneel" />
        </div>

        <div class="mb-4">
          <x-label for="comments" value="Interne opmerkingen" />
          <textarea
            id="comments"
            class="block mt-1 w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
            wire:model.live="comments"
            rows="3"
            placeholder="Voeg interne opmerkingen toe (alleen zichtbaar voor gebruikers van het systeem)"
          ></textarea>
        </div>


      @endif
      @endif
      <!-- End Selecteer Product -->

    </x-slot>
    <x-slot name="footer">
      <div class="flex justify-between w-full">
        <x-secondary-button wire:click="cancelPurchase">Annuleren</x-secondary-button>
        <x-button wire:click="savePurchase">Opslaan</x-button>
      </div>
    </x-slot>
  </x-dialog-modal>

  <!-- Address Book Modal -->
  <x-dialog-modal wire:model.live="showAddressModal">
    <x-slot name="title">
      <div class="flex justify-between items-center">
        <span>Adresboek</span>
        <x-button wire:click="openAddAddressModal" class="text-xs px-2 py-1">
          Nieuw adres
        </x-button>
      </div>
    </x-slot>
    <x-slot name="content">
      <div class="max-h-96 overflow-y-auto">
        @if(count($supplierAddresses) > 0)
          @foreach($supplierAddresses as $address)
            <div
              wire:click="selectAddress({{ $address->id }})"
              class="p-3 border border-gray-300 rounded-md mb-3 cursor-pointer hover:bg-gray-100 {{ $selectedAddressId == $address->id ? 'bg-blue-50 border-blue-300' : '' }}"
            >
              <div class="font-semibold">{{ $address->name }}</div>
              <div>{{ $address->street }} {{ $address->housenumber }}</div>
              <div>{{ $address->postal_code }} {{ $address->city }}</div>
              @if($address->loading_times)
                <div class="mt-2 text-sm text-gray-600">
                  <strong>Laadtijden:</strong> {{ $address->loading_times }}
                </div>
              @endif
            </div>
          @endforeach
        @else
          <p>Geen adressen gevonden.</p>
        @endif
      </div>
    </x-slot>
    <x-slot name="footer">
      <div class="flex justify-end w-full">
        <x-secondary-button wire:click="$set('showAddressModal', false)">
          Sluiten
        </x-secondary-button>
      </div>
    </x-slot>
  </x-dialog-modal>

  <!-- Add Address Modal -->
  <x-dialog-modal wire:model.live="showAddAddressModal">
    <x-slot name="title">
      Nieuw adres toevoegen
    </x-slot>
    <x-slot name="content">
      <div class="mb-4">
        <x-label for="newAddressName" value="Naam" />
        <x-input id="newAddressName" class="block mt-1 w-full" type="text" wire:model.live="newAddressName" placeholder="Bijv. Hoofdlocatie, Magazijn, etc." />
      </div>

      <div class="mb-4">
        <x-label for="newAddressStreet" value="Straat" />
        <x-input id="newAddressStreet" class="block mt-1 w-full" type="text" wire:model.live="newAddressStreet" />
      </div>

      <div class="mb-4">
        <x-label for="newAddressHousenumber" value="Huisnummer" />
        <x-input id="newAddressHousenumber" class="block mt-1 w-full" type="text" wire:model.live="newAddressHousenumber" />
      </div>

      <div class="mb-4">
        <x-label for="newAddressPostalCode" value="Postcode" />
        <x-input id="newAddressPostalCode" class="block mt-1 w-full" type="text" wire:model.live="newAddressPostalCode" />
      </div>

      <div class="mb-4">
        <x-label for="newAddressCity" value="Plaats" />
        <x-input id="newAddressCity" class="block mt-1 w-full" type="text" wire:model.live="newAddressCity" />
      </div>

      <div class="mb-4">
        <x-label for="newAddressCountry" value="Land" />
        <x-input id="newAddressCountry" class="block mt-1 w-full" type="text" wire:model.live="newAddressCountry" />
      </div>

      <div class="mb-4">
        <x-label for="newAddressLoadingTimes" value="Laadtijden" />
        <textarea
          id="newAddressLoadingTimes"
          class="block mt-1 w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
          wire:model.live="newAddressLoadingTimes"
          rows="3"
          placeholder="Voer laadtijden in (bijv. Ma-Vr: 08:00-16:00)"
        ></textarea>
      </div>
    </x-slot>
    <x-slot name="footer">
      <div class="flex justify-between w-full">
        <x-secondary-button wire:click="$set('showAddAddressModal', false)">
          Annuleren
        </x-secondary-button>
        <x-button wire:click="saveNewAddress">
          Opslaan
        </x-button>
      </div>
    </x-slot>
  </x-dialog-modal>

  <!-- Add Product Modal -->
  <x-dialog-modal wire:model.live="showAddProductModal">
    <x-slot name="title">
      Product toevoegen aan leverancier
    </x-slot>
    <x-slot name="content">
      <div class="mb-4">
        <x-label for="addProductSearch" value="Zoek product" />
        <div class="relative">
          <x-input
            id="addProductSearch"
            class="block mt-1 w-full"
            type="text"
            wire:model.live.debounce.300ms="addProductSearch"
            placeholder="Zoek op naam of SKU..."
          />

          @if($searchResults && count($searchResults) > 0)
            <div class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-300 max-h-60 overflow-y-auto">
              @foreach($searchResults as $product)
                <div wire:click="selectNewProduct({{ $product->id }})" class="p-2 hover:bg-gray-100 cursor-pointer">
                  <div class="font-medium">{{ $product->name }}</div>
                  <div class="text-xs text-gray-500">{{ $product->sku }}</div>
                </div>
              @endforeach
            </div>
          @endif

          @if($newSelectedProduct)
            <div class="mt-2 p-2 bg-blue-50 rounded text-sm">
              Geselecteerd: <strong>{{ $newSelectedProduct->name }}</strong> ({{ $newSelectedProduct->sku }})
            </div>
          @endif
        </div>
      </div>

      @if($newSelectedProduct)
        <div class="mb-4">
          <x-label for="newProductPrice" value="Prijs per eenheid" />
          <div class="mt-1 relative rounded-md shadow-sm">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span class="text-gray-500 sm:text-sm">€</span>
            </div>
            <x-input
              id="newProductPrice"
              class="pl-7 block w-full"
              type="number"
              step="0.01"
              wire:model.live="newProductPrice"
              placeholder="0.00"
            />
          </div>
        </div>

        <div class="mb-4">
          <x-label for="newProductUnitType" value="Eenheid type" />
          <select
            id="newProductUnitType"
            wire:model.live="newProductUnitType"
            class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
          >
            <option value="TON">TON</option>
            <option value="M3">M3</option>
            <option value="RIT">RIT</option>
            <option value="ZAK">ZAK</option>
            <option value="STUK">STUK</option>
            <option value="VOLLE VRACHT">VOLLE VRACHT</option>
            <option value="HALVE VRACHT">HALVE VRACHT</option>
          </select>
        </div>

        <div class="mb-4">
          <x-label for="newProductEndDate" value="Einddatum (optioneel)" />
          <x-input
            id="newProductEndDate"
            class="mt-1 block w-full"
            type="date"
            wire:model.live="newProductEndDate"
          />
          <p class="mt-1 text-sm text-gray-500">Laat leeg voor onbeperkte geldigheid</p>
        </div>

        <div class="mb-4">
          <x-label for="newProductDefaultQty" value="Standaard hoeveelheid (optioneel)" />
          <x-input
            id="newProductDefaultQty"
            class="mt-1 block w-full"
            type="number"
            min="1"
            wire:model.live="newProductDefaultQty"
            placeholder="Bijv. 25"
          />
          <p class="mt-1 text-sm text-gray-500">Standaard hoeveelheid voor nieuwe aankopen</p>
        </div>
      @endif
    </x-slot>
    <x-slot name="footer">
      <div class="flex justify-between w-full">
        <x-secondary-button wire:click="$set('showAddProductModal', false)">Annuleren</x-secondary-button>
        <x-button
          wire:click="addProductToSupplier"
        >
          Toevoegen
        </x-button>
      </div>
    </x-slot>
  </x-dialog-modal>
</div>
