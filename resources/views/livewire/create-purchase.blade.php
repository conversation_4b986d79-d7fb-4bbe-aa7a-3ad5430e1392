<div>
  <div id="purchase-top" class="flex gap-5">
    <div id="supplier">
      <strong>Leverancier</strong><br>
      @if(!$selectedSupplier)
        <input
                type="text"
                wire:model.live.debounce.500ms="search"
                placeholder="<PERSON><PERSON> leverancier..."
                style="margin-top: .25rem"
        />
        @unless($suppliers->isEmpty())
          <div class="bg-gray-100" style="max-height: 400px; overflow-x: scroll; border: 1px solid #4b5563">
            @foreach($suppliers as $supplier)
              <p
                      wire:click="selectSupplier({{ $supplier->id }})"
                      class="cursor-pointer hover:bg-gray-50 py-2 px-2"
                      style="margin-bottom: 0px !important;"
              >
                {{ $supplier->company_name }} ({{$supplier->exact_id}})
              </p>
            @endforeach
          </div>
        @endunless
      @endif
      @if(!empty($selectedSupplier))
        <div id="billing">
          {{ $selectedSupplier->exact_id }}<br>
          {{ $selectedSupplier->company_name }}<br>
          {{ $selectedSupplier->street_name }} {{ $selectedSupplier->housenumber }}<br>
          {{ $selectedSupplier->postal_code }} {{ $selectedSupplier->city }}<br>
          {{ $selectedSupplier->email }}
        </div>
      @endif
    </div>
    <div id="load-date">
      <div class="mb-4">
        <strong>Laaddatum</strong>
        <x-input id="aantal" class="block mt-1 w-full" type="date" wire:model.live="loadDate" />
      </div>
    </div>
    <div id="administration">
      <div class="mb-4" style="width: 220px">
        <strong>Administratie</strong>
        <select id="type" class="form-input rounded-md shadow-sm mt-1 block w-full" wire:model.live="administration">
          <option value="">Selecteer administratie</option>
          <option value="030">030 Fouragehandel Bus</option>
          <option value="033">033 Logi.Span Resthout</option>
          <option value="036">036 Logi.Span Retail</option>
          <option value="037">037 Logi.Span Biomass</option>
        </select>
        @if($errorAdmin)
          <div class="text-red-500 p-1">
            <small>{{ $errorAdmin }}</small>
          </div>
        @endif
      </div>
    </div>
  </div>

  <div id="purchase-products">
    @if(!$productSelected && !empty($selectedSupplier))
      <x-button wire:click="$set('showModal', true)" class="mt-3">Regel toevoegen</x-button>
    @endif
    <table style="width: 100%">
      <thead>
      <tr  style="border-bottom: 2px solid #000; height: 45px;">
        <th style="width: 20%">SKU</th>
        <th style="width: 40%">Product</th>
        <th style="width: 10%">Aantal</th>
        <th style="width: 10%">Prijs</th>
        <th style="width: 10%">Totaal</th>
        <th style="width: 10%"></th>
      </tr>
      </thead>
      <tbody>

        @if($productSelected)
          <tr style="border-bottom: 1px solid #ccc; height: 45px;">
            <td>{{ $selectedProduct->sku }}</td>
            <td>{{ $selectedProduct->name }}</td>
            <td>{{ $purchaseQty }} {{ $purchaseQtyType }}</td>
            <td>&euro; {{ $purchasePrice }}</td>
            <td>&euro; {{ round($purchasePrice * $purchaseQty, 2) }}</td>
            <td><i class="fa-solid fa-trash" wire:click="removeProduct" style="color: #dd2222;"></i></td>
          </tr>
        @endif
      </tbody>
    </table>
    <x-button wire:click="savePurchase" class="mt-4">Opslaan</x-button>
  </div>


  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Regel toevoegen
    </x-slot>

    <x-slot name="content">
      <div class="mb-4">
        <x-label for="product" value="Product" />
        <div class="@if(!empty($selectedProduct)) hidden @endif">
          <input
                  type="text"
                  wire:model.live.debounce.500ms="productSearch"
                  placeholder="Zoek product..."
          />
          @unless($products->isEmpty())
            <div class="bg-gray-100" style="max-height: 400px; overflow-x: scroll; border: 1px solid #4b5563">
              @foreach($products as $product)
                <p
                        wire:click="selectProduct({{ $product->id }})"
                        class="cursor-pointer hover:bg-gray-50 py-2 px-2"
                        style="margin-bottom: 0px !important;"
                >
                  {{ $product->sku }} - {{ $product->name }}
                </p>
              @endforeach
            </div>
          @endunless
        </div>
        @if(!empty($selectedProduct))
          {{ $selectedProduct->name }}
        @endif
      </div>
      @if(!empty($selectedProduct))
      <div class="mb-4">
        <x-label for="aantal" value="Aantal" />
        <div class="flex gap-4">
          <x-input id="aantal" class="block mt-1 w-1/3" type="text" wire:model.live="purchaseQty" />
          <select id="qty-type" wire:model.live="purchaseQtyType" class="mt-1 block w-1/4 border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm">
            <option value="TON">TON</option>
            <option value="M3">M3</option>
            <option value="RIT">RIT</option>
            <option value="ZAK">ZAK</option>
            <option value="STUK">STUK</option>
          </select>
        </div>
      </div>
      <div class="mb-4">
        <x-label for="price" value="Inkoopprijs per {{$purchaseQtyType}}" />
        <x-input id="price" class="block mt-1 w-1/4" type="text" wire:model.live="purchasePrice" />
        @if($message)
          <small>{{ $message }}</small>
        @endif
      </div>
      <div class="mb-4">
        <div>
          <label class="toggle">
            <input class="toggle-checkbox" type="checkbox">
            <div class="toggle-switch"></div>
            <span class="toggle-label">Herhalen</span>
          </label>
        </div>
      </div>
      <div class="mb-4">
        <div class="flex gap-4">
          <div>
            <x-label for="price" value="Elke" />
            <x-input id="price" class="block mt-1 w-1/4" type="text" wire:model.live="days" /> dagen
          </div>
          <div>
            <x-label for="price" value="Voor" />
            <x-input id="price" class="block mt-1 w-1/4" type="text" wire:model.live="times" /> keer
          </div>
        </div>
      </div>
      @if($errorMessage)
        <div class="bg-red-100 p-4">
          {{ $errorMessage }}
        </div>
      @endif
        @endif
    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="$set('showModal', false)" wire:loading.attr="disabled">
        Annuleren
      </x-secondary-button>

      <x-button class="ml-2" wire:click.prevent="addProduct" wire:loading.attr="disabled">
        Opslaan
      </x-button>
    </x-slot>
  </x-dialog-modal>


</div>
