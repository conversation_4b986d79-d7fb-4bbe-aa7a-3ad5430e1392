<div class="mx-auto sm:px-6 lg:px-8 flex mb-4 justify-center">
  <div x-data="{ open: true }" class="w-full bg-white overflow-hidden shadow-xl sm:rounded-lg">
    <div class="p-4 cursor-pointer flex justify-between items-center">
      <h4>Planbaar</h4>
      @if(count($selectedCargo) > 0)
        <div class="mb-4 p-4 bg-gray-100 rounded flex gap-4 items-center">
          <div>
            <label class="block text-sm font-medium text-gray-700">Laaddatum</label>
            <input
                    type="date"
                    class="mt-1 border-gray-300 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 rounded-md shadow-sm"
                    wire:model="tempLoadDate"
            >
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Losdatum</label>
            <input
                    type="date"
                    class="mt-1 border-gray-300 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 rounded-md shadow-sm"
                    wire:model="tempDeliveryDate"
            >
          </div>
          <div class="flex items-end gap-2">
            <x-button wire:click="updateSelected">
              Update
            </x-button>
            <x-danger-button wire:click="deleteSelected" wire:confirm="Weet je zeker dat je de geselecteerde aankopen wilt verwijderen? Dit verwijdert ook alle bijbehorende verkopen.">
              Verwijderen
            </x-danger-button>
          </div>
        </div>
      @endif
      <div @click="open = !open" >
      <svg x-show="!open" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
      </svg>
      <svg x-show="open" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
      </svg>
      </div>
    </div>
    <div x-show="open"
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="transition ease-in duration-100"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95"
         class="p-6 border-t">

      <div class="max-h-[500px] overflow-y-auto">
        <table class="w-full border-separate border-spacing-y-1">
          <thead class="sticky top-0 bg-white z-10">
            <tr>
              <th class="text-left p-2"></th>
              <th class="text-left p-2">Ritnummer</th>
              <th class="text-left p-2">Laden</th>
              <th class="text-left p-2">Laaddatum</th>
              <th class="text-left p-2">Lossen</th>
              <th class="text-left p-2">Losdatum</th>
              <th class="text-left p-2">Transport</th>
              <th class="text-left p-2">Transporteur</th>
              <th class="text-left p-2">Handtekening</th>
              <th class="text-left p-2">Acties</th>
            </tr>
          </thead>
          <tbody>
          @foreach($plannableCargo as $cargo)
            <tr class="bg-gray-200 rounded">
              <td class="p-2">
                <input type="checkbox"
                       wire:model.live="selectedCargo"
                       value="{{ $cargo->id }}"
                >
              </td>
              <td class="p-2">{{ $cargo->purchase_number }}</td>
              <td class="p-2">{{ $cargo->supplier->company_name }} - {{ $cargo->supplier->city }} - {{ $cargo->supplier->country }}</td>
              <td class="p-2">
                <input type="date"
                       class="border-gray-300 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 rounded-md shadow-sm"
                       value="{{ \Carbon\Carbon::parse($cargo->load_date)->format('Y-m-d') }}"
                       wire:change="updateLoadDate({{ $cargo->id }}, $event.target.value)"
                >
              </td>
              <td class="p-2">
                @foreach($cargo->sales as $sale)
                  <div class="flex justify-between items-center mb-1">
                    <span>{{ $sale->customer->company_name }} - {{ $sale->customer->city }} - {{ $sale->customer->country }}</span>
                    <button wire:click.stop="confirmDetachSale({{ $sale->id }})" class="text-red-500 hover:text-red-700">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                @endforeach
              </td>
              <td class="p-2">
                <input type="date"
                       class="border-gray-300 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 rounded-md shadow-sm"
                       value="{{ \Carbon\Carbon::parse($cargo->delivery_date)->format('Y-m-d') }}"
                       wire:change="updateDeliveryDate({{ $cargo->id }}, $event.target.value)"
                >
              </td>

              <td class="p-2">
                @if($cargo->own_transport)
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Eigen transport
                  </span>
                @else
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    Externe transport
                  </span>
                @endif
              </td>

               <td class="p-2">
                 @if($cargo->own_transport)
                   <span class="text-green-600 font-semibold">Eigen transport</span>
                 @else
                   {{ $cargo->shipper->company_name ?? 'Geen transporteur' }}
                 @endif
               </td>

               <td class="p-2">
                 @if($cargo->signature)
                   <x-signature-modal :purchase="$cargo" />
                 @else
                   <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                     <i class="fas fa-clock mr-1"></i>
                     Wachtend
                   </span>
                 @endif
               </td>

               <td class="p-2 flex space-x-2">
                 <x-danger-button wire:click="confirmDelete({{ $cargo->id }})" class="px-2 py-1 text-xs">
                   Verwijderen
                 </x-danger-button>
                 <x-button wire:click="viewPurchase({{ $cargo->id }})" class="px-2 py-1 text-xs">
                   Tonen
                 </x-button>
               </td>
            </tr>
          @endforeach
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Detach Sale Confirmation Modal -->
  <div x-data="{ show: @entangle('showDetachSaleConfirmation') }"
       x-show="show"
       x-cloak
       class="fixed inset-0 z-50 overflow-y-auto"
       aria-labelledby="modal-title"
       role="dialog"
       aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div x-show="show"
           x-transition:enter="ease-out duration-300"
           x-transition:enter-start="opacity-0"
           x-transition:enter-end="opacity-100"
           x-transition:leave="ease-in duration-200"
           x-transition:leave-start="opacity-100"
           x-transition:leave-end="opacity-0"
           class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
           aria-hidden="true">
      </div>

      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

      <div x-show="show"
           x-transition:enter="ease-out duration-300"
           x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
           x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
           x-transition:leave="ease-in duration-200"
           x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
           x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
           class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
              <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
              <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                Verkoop loskoppelen
              </h3>
              <div class="mt-2">
                <p class="text-sm text-gray-500">
                  Weet je zeker dat je deze verkoop wilt loskoppelen? De verkoop wordt teruggezet naar Vraag en als dit de laatste verkoop is, gaat de aankoop terug naar Aanbod.
                </p>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button type="button" wire:click="detachSale" class="mt-3 w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
            Loskoppelen</button>
          <button type="button" wire:click="cancelDetachSale" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            Annuleren
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Delete Confirmation Modal -->
  <div x-data="{ show: @entangle('showDeleteConfirmation') }"
       x-show="show"
       x-cloak
       class="fixed inset-0 z-50 overflow-y-auto"
       aria-labelledby="modal-title"
       role="dialog"
       aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div x-show="show"
           x-transition:enter="ease-out duration-300"
           x-transition:enter-start="opacity-0"
           x-transition:enter-end="opacity-100"
           x-transition:leave="ease-in duration-200"
           x-transition:leave-start="opacity-100"
           x-transition:leave-end="opacity-0"
           class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
           aria-hidden="true">
      </div>

      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

      <div x-show="show"
           x-transition:enter="ease-out duration-300"
           x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
           x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
           x-transition:leave="ease-in duration-200"
           x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
           x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
           class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
              <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
              <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                Rit verwijderen
              </h3>
              <div class="mt-2">
                <p class="text-sm text-gray-500">
                  Weet je zeker dat je deze rit wilt verwijderen? Dit verwijdert ook alle bijbehorende vraag en aanbod. Deze actie kan niet ongedaan worden gemaakt.
                </p>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button wire:click="deletePurchase" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
            Verwijderen
          </button>
          <button wire:click="cancelDelete" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            Annuleren
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
