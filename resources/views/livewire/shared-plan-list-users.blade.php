@php use Illuminate\Support\Facades\Auth; @endphp
<div>
  <div class="flex align-items-baseline">
    <i class="fa-solid fa-users mr-3"></i>
    @foreach ($planlist->users as $user)
      <div class="rounded-3 bg-gray-100 pt-2 pb-2 mr-3" style="padding-left: 10px; padding-right: 10px">
        {{ $user->name }}
        @if($planlist->created_by_user_id == Auth::id())
          <i class="fa-regular fa-circle-xmark cursor-pointer" wire:click="removeUser({{ $user->id }})"></i>
        @endif
      </div>
    @endforeach
    <i class="fa-solid fa-circle-plus ml-2 cursor-pointer" wire:click="$set('showModal', true)"></i>
  </div>
  <x-dialog-modal  wire:model.live="showModal">
    <x-slot name="title">Planlijst delen</x-slot>
    <x-slot name="content">
      <div class="w-full mt-8">
        <x-label for="interactionNote" value="{{ __('Delen met') }}" />
        <select wire:model.live="shareWith">
          <option value="">Kies gebruiker</option>
          @foreach($shareWithUsers as $shareUser)
            <option value="{{ $shareUser->id }}">{{ $shareUser->name }}</option>
          @endforeach
        </select>
      </div>
    </x-slot>
    <x-slot name="footer">
      <x-button wire:click="addUser">Opslaan</x-button>
    </x-slot>
  </x-dialog-modal>
</div>
