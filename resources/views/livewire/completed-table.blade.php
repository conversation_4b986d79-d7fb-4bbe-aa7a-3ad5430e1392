<div>
  <table id="purchase-table" class="table"></table>

  @push('scripts')
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <script src="https://cdn.datatables.net/1.10.23/js/jquery.dataTables.min.js"></script>
    <script>
      document.addEventListener('livewire:init', function () {
        var table = $('#purchase-table').DataTable({
          processing: true,
          serverSide: true,
          stateSave: true,
          ajax: '{{ route('livewire.planning-table.get-purchases') }}',
          columns: [
            { data: 'id', name: 'id', width: '40px' },
            { data: 'purchase_number', name: 'purchase_number', width: '100px'},
            { data: 'product.name', name: 'product.name' },
            {data: 'quantity', name: 'quantity'},
            { data: 'supplier.name', name: 'supplier.name' },
            {
              data: 'load_date',
              name: 'load_date',
              width: '100px',
              render: function (data, type, row) {
                var date = new Date(data);

                var day = ("0" + date.getDate()).slice(-2);
                var month = ("0" + (date.getMonth() + 1)).slice(-2); // getMonth() is zero-based
                var year = date.getFullYear();

                return day + '-' + month + '-' + year;
              }
            },
          ],
          columnDefs: [
            { title: "ID", targets: 0 },
            { title: "Inkoopnummer", targets: 1 },
            { title: "Product", targets: 2 },
            { title: "Aantal",
              targets: 3,
              render: function (data, type, row) {
                  return row.quantity + ' ' + row.qty_type;
              }
            },
            { title: "Leverancier", targets: 4 },
            { title: "Laden", targets: 5 },
          ],
          order: [[0, 'desc']],
          rowCallback: function(row, data) {
            $(row).on('click', function() {
              // Redirect to the edit page using the 'id' of the clicked row
              window.location.href = '/handel/completed/' + data.id;
            });
          }
        });

      });
    </script>
  @endpush
</div>
