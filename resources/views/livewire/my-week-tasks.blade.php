<div>
  <x-button wire:click="$set('showNewActionModal', true)"><PERSON><PERSON> toe<PERSON>egen</x-button>
  <div class="flex justify-between align-items-baseline mb-2">
    <h4>Mijn week ({{ count($actions) }})</h4>
    <div>Tot Datum: <input type="date" wire:model.live="selectedEndDate"></div>
  </div>


  <div class="flex-column cursor-pointer">
    @foreach ($actions as $action)
      <div class="rounded bg-gray-200 p-2 mb-2 hover:bg-gray-400" wire:click="showModal({{ $action }})">
        <div class="flex justify-between mb-1">
          <div>{!! nl2br(e($action->description)) !!}</div>
        </div>
        <hr>
        Datum: {{ $action->due_date }} <br>
        @if($action->actionable)
          <strong>{{ $action->actionable->company_name }}</strong> <br>
        @endif
      </div>
    @endforeach
  </div>

  <x-dialog-modal wire:model.live="isModalOpen">
    <x-slot name="title">
      Taak Details
    </x-slot>

    <x-slot name="content">
        @if($modalTask)
          <p>@php
              $routeName = '';
              switch($modalTask->actionable_type) {
                  case \App\Models\Customer::class:
                      $routeName = 'customer.edit';
                      $routeArg = 'customer';
                      break;
                  case \App\Models\Supplier::class:
                      $routeName = 'supplier.edit';
                      $routeArg = 'supplier';
                      break;
                  case \App\Models\Prospect::class:
                      $routeName = 'prospect.edit';
                      $routeArg = 'prospect';
                      break;
                  default:
                      break;
              }
            @endphp

            @if($routeName)
              <a href="{{ route($routeName, [$routeArg => $modalTask->actionable->id]) }}">
                {{ $modalTask->actionable->company_name }}
              </a><br>
              <i class="fa-solid fa-phone"></i> {{ $modalTask->actionable->telephone }}<br>
          </p>
        @endif
        <p><strong>Type:</strong><br>
          {{ ucfirst($modalTask->type) }}
        </p>
        <div class="mb-4">
          <x-label for="due-date" value="Datum" />
          <x-input id="due-date" class="block mt-1" style="width: 9rem;" type="date" wire:model.live="dueDate" />
        </div>
        <div class="w-full mt-8">
          <x-label for="interactionNote" value="{{ __('Toegewezen aan') }}" />
          <select wire:model.live="ownerId">
            <option value="" {{ empty($ownerId) ? 'selected' : '' }}>Kies gebruiker</option>
            @foreach($users as $user)
              <option value="{{ $user->id }}" {{ $ownerId == $user->id ? 'selected' : '' }}>{{ $user->name }}</option>
            @endforeach
          </select>
        </div>
        <div class="mt-4">
          <x-label for="note" value="Omschrijving" />
          <textarea rows="4" wire:model.live="description" class="peer h-full min-h-[100px] w-full resize-none rounded-[7px] border border-blue-gray-200 border-t-transparent bg-transparent px-3 py-2.5 font-sans text-sm font-normal text-blue-gray-700 outline outline-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 focus:border-2 focus:border-pink-500 focus:border-t-transparent focus:outline-0 disabled:resize-none disabled:border-0 disabled:bg-blue-gray-50"></textarea>
          <small>Type @@ voor datum</small>
        </div>
      @endif

      <x-button wire:click="addTaskNote" class="mt-2">Opslaan</x-button>
    </x-slot>

    <x-slot name="footer">
      <div class="w-full flex justify-between">
      <x-danger-button wire:click="deleteTask">Verwijder</x-danger-button>
        <div>
          <x-secondary-button wire:click="$set('isModalOpen', false)" wire:loading.attr="disabled">
            Sluiten
          </x-secondary-button>
          <x-button wire:click="completeTask" class="ml-2">Actie afronden</x-button>
        </div>
      </div>
    </x-slot>
  </x-dialog-modal>

  <x-dialog-modal wire:model.live="showNewActionModal">
    <x-slot name="title">
      Nieuwe actie
    </x-slot>
    <x-slot name="content">
      <div class="mb-4">
        <x-label for="assignedTo" value="Type" />
        <select id="type" class="form-input rounded-md shadow-sm mt-1 block w-full" wire:model.live="type">
          @foreach($types as $taskType)
            <option value="{{ $taskType }}">{{ ucfirst($taskType) }}</option>
          @endforeach
        </select>
        @error('type') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>

      <div class="mb-4">
        <x-label for="assignedTo" value="Toegewezen aan" />
        <select id="assignedTo" class="form-input rounded-md shadow-sm mt-1 block w-full" wire:model.live="assignedTo">
          @foreach($users as $user)
            <option value="{{ $user->id }}">{{ $user->name }}</option>
          @endforeach
        </select>
        @error('assignedTo') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>

      <div class="mb-4">
        <x-label for="due-date" value="Datum" />
        <x-input id="due-date" class="block mt-1 w-full" type="date" wire:model.live="newDueDate" />
        @error('dueDate') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>
      <div class="mb-4">
        <x-label for="description" value="Omschrijving" />
        <textarea rows="4" wire:model.live="newDescription" class="peer h-full min-h-[100px] w-full resize-none rounded-[7px] border border-blue-gray-200 border-t-transparent bg-transparent px-3 py-2.5 font-sans text-sm font-normal text-blue-gray-700 outline outline-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 focus:border-2 focus:border-pink-500 focus:border-t-transparent focus:outline-0 disabled:resize-none disabled:border-0 disabled:bg-blue-gray-50"></textarea>
        @error('description') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
        <small>*Gebruik @@-spatie om de datum in de tekst te krijgen.</small>
      </div>
    </x-slot>
    <x-slot name="footer">
      <x-secondary-button wire:click="cancelNewAction" wire:loading.attr="disabled">
        Sluiten
      </x-secondary-button>
      <x-button wire:click="saveNewAction" class="ml-2">Actie opslaan</x-button>
    </x-slot>
  </x-dialog-modal>
</div>
