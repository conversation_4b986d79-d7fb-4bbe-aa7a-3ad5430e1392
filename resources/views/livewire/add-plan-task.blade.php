<div>
  <h3><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> planlijst</h3>
  <div class="flex justify-between">
    <x-button wire:click="$set('showModal', true)"><i class="fa-solid fa-plus" style="color: #ffffff; margin-right: 4px;"></i> Taak toevoegen</x-button>
    <div>Tot Datum: <input type="date" wire:model.live="showTo"></div>
  </div>
  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Taak toevoegen
    </x-slot>
    <x-slot name="content">
      <div class="mb-4">
        <x-label for="assignedTo" value="Relatie" />
        <input type="text" wire:model.live.debounce.300ms="search" placeholder="Relatie..." class="input-style">

        @if(!empty($results))
          <div class="absolute z-10 mt-2 w-2/3 bg-white border border-gray-300 rounded shadow-md">
            <ul>
              @foreach($results as $result)
                <li wire:click="selectEntity({{$result['id']}}, '{{$result['classname']}}')" class="px-4 py-2 hover:bg-gray-200 cursor-pointer">{{ $result['company_name'] }}</li>
              @endforeach
            </ul>
          </div>
        @endif
      </div>
      <div class="mb-4">
        <x-label for="assignedTo" value="Toewijzen aan" />
        <select id="assignedTo" class="form-input rounded-md shadow-sm mt-1 block w-full" wire:model.live="assignedTo">
          @foreach($users as $user)
            <option value="{{ $user->id }}">{{ $user->name }}</option>
          @endforeach
        </select>
        @error('assignedTo') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>

      <div class="mb-4">
        <x-label for="due-date" value="Datum" />
        <x-input id="due-date" class="block mt-1 w-full" type="date" wire:model.live="dueDate" autofocus />
        @error('dueDate') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>
      <div class="mb-4">
        <x-label for="description" value="Omschrijving" />
        <textarea rows="4" wire:model.live="description" class="peer h-full min-h-[100px] w-full resize-none rounded-[7px] border border-blue-gray-200 border-t-transparent bg-transparent px-3 py-2.5 font-sans text-sm font-normal text-blue-gray-700 outline outline-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 focus:border-2 focus:border-pink-500 focus:border-t-transparent focus:outline-0 disabled:resize-none disabled:border-0 disabled:bg-blue-gray-50"></textarea>
        @error('description') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>
      @if($errorMessage)
        <div class="bg-red-100 p-4">
          {{ $errorMessage }}
        </div>
      @endif
    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="$set('showModal', false)" wire:loading.attr="disabled">
        Annuleren
      </x-secondary-button>

      <x-button class="ml-2" wire:click.prevent="saveTask" wire:loading.attr="disabled">
        Opslaan
      </x-button>
    </x-slot>
  </x-dialog-modal>
</div>
