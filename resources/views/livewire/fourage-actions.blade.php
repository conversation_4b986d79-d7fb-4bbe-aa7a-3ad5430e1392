<div>
    <div class="mb-4 flex justify-between">
      <div>
        <span>Toon:</span>
        <select name="foruser" wire:model.live="selectedUser">
          <option value="all">Allen</option>
          <option value="3">Wouter</option>
          <option value="5"><PERSON><PERSON></option>
          <option value="10">Henk</option>
          <option value="9">Frank</option>
        </select>
      </div>
      <div>
        <span>Vanaf datum:</span>
        <input type="date" wire:model.live="selectedFromDate">
      </div>
    </div>
    @foreach($actions as $action)
    <div class="rounded bg-gray-200 p-2 mb-2 hover:bg-gray-400">
      <div class="flex justify-between mb-1">
        <div>{!! nl2br(e($action->description)) !!}</div>
      </div>
      <hr>
      Datum: {{ $action->updated_at }} <br>
      Door: {{ $action->createdBy->name }} <br>
      @if($action->actionable)
        <strong>{{ $action->actionable->company_name }}</strong> <br>
      @endif
    </div>
    @endforeach
</div>
