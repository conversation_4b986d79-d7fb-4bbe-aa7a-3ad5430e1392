<div>
  <div class="flex align-items-baseline">
    <div class="mr-3"><strong>Toon taken voor: </strong></div>
    <select wire:model.live="userId">
      <option value="" {{ empty($userId) ? 'selected' : '' }}>Ki<PERSON> geb<PERSON>iker</option>
      @foreach($users as $user)
        <option value="{{ $user->id }}" {{ $userId == $user->id ? 'selected' : '' }}>{{ $user->name }}</option>
      @endforeach
    </select>
  </div>
<div class="mt-4">
  @if($tasks)
  @foreach ($tasks as $task)
    <div class="rounded bg-gray-200 p-2 mb-2 hover:bg-gray-400" wire:click="showModal({{ $task }})">
      <div class="flex justify-between">
        <div>{{ $task->title }} Datum: @if(strtotime($task->due_date) < strtotime(now()))
            <span style="color: red; font-weight: bold">{{ $task->due_date }}</span>
          @else
            {{ $task->due_date }}
          @endif</div> @if($task->note)<i class="fa-solid fa-note-sticky"></i>@endif
      </div>

      <strong>{{ $task->taskable->company_name }}</strong> <br>
      {{ $task->description }}
    </div>
  @endforeach
    @endif
</div>
</div>
