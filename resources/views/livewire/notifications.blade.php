<div>
      <div class="flex justify-between">
            <h4>Notificaties</h4> <a wire:click="clearAll" class="cursor-pointer">(wis alle notificaties)</a>
      </div>
    @foreach($notifications as $notification)
      <div class="rounded bg-gray-200 p-2 mb-2 relative flex gap-2">
            <div>
                  <small>{{ $notification->created_at }}</small><br>
                  {{ $notification->details }}<br>
                  @php
                        $routeName = '';
                        switch($notification->notifiable_type) {
                            case \App\Models\Customer::class:
                                $routeName = 'customer.edit';
                                $routeArg = 'customer';
                                break;
                            case \App\Models\Supplier::class:
                                $routeName = 'supplier.edit';
                                $routeArg = 'supplier';
                                break;
                            case \App\Models\Prospect::class:
                                $routeName = 'prospect.edit';
                                $routeArg = 'prospect';
                                break;
                            default:
                                break;
                        }
                  @endphp
                  <a href="{{ route($routeName, [$routeArg => $notification->notifiable->id]) }}">
                        {{ $notification->notifiable->company_name }}
                  </a>
            </div>
            <div class="absolute" style="right: 10px; top: 10px;">
                  <i wire:click="dismissNotification({{ $notification }})" class="fa-regular fa-circle-xmark cursor-pointer"></i>
            </div>
      </div>
    @endforeach
</div>
