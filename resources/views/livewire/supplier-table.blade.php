<div>
  <label class="toggle">
    <input class="toggle-checkbox" type="checkbox" wire:model.live="mySuppliers" id="myProspects">
    <div class="toggle-switch"></div>
    <span class="toggle-label">Toon enkel mijn leveranciers</span>
  </label>
  <table id="suppliers-table" class="table">
    <thead>
    <tr>
      <th>ExactID</th>
      <th>Bedrijf</th>
      <th>Naam</th>
      <th>Straat</th>
      <th>Huisnummer</th>
      <th>Postcode</th>
      <th>Plaats</th>
      <th>G<PERSON>epen</th>
    </tr>
    </thead>
  </table>

  @push('scripts')
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <script src="https://cdn.datatables.net/1.10.23/js/jquery.dataTables.min.js"></script>
    <script>
      document.addEventListener('livewire:init', function () {
        $('#suppliers-table').DataTable({
          stateSave: true,
          processing: true,
          serverSide: true,
          ajax: '{{ route('livewire.supplier-table.get-suppliers') }}',
          columns: [
            { data: 'exact_id', name: 'exact_id' },
            { data: 'company_name', name: 'company_name' },
            { data: 'name', name: 'name' },
            { data: 'street_name', name: 'street_name' },
            { data: 'housenumber', name: 'housenumber' },
            { data: 'postal_code', name: 'postal_code' },
            { data: 'city', name: 'city' },
            { data: 'product_groups', name: 'product_groups' },
          ],
          rowCallback: function(row, data) {
            $(row).on('click', function() {
              // Redirect to the edit page using the 'id' of the clicked row
              window.location.href = '/supplier/edit/' + data.id;
            });
          }
        });
      });
      document.addEventListener('refresh', function() {
        window.location.reload();
      });
    </script>
  @endpush
</div>
