<div>
  <div class="flex flex-column">
    @if ($owner)
      <small>(Eigenaar: {{ $owner }})</small>
    @endif
    <x-button wire:click="$set('showModal', true)">Wi<PERSON>zig eigenaar</x-button>
  </div>
  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Eigenaar wijzigen
    </x-slot>
    <x-slot name="content">
      <select wire:model.live="ownerId">
        <option value="" {{ empty($ownerId) ? 'selected' : '' }}>Kies eigenaar</option>
        @foreach($users as $user)
          <option value="{{ $user->id }}" {{ $ownerId == $user->id ? 'selected' : '' }}>{{ $user->name }}</option>
        @endforeach
      </select>
    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="$set('showModal', false)" wire:loading.attr="disabled">
        Annuleren
      </x-secondary-button>

      <x-button class="ml-2" wire:click.prevent="updateOwner" wire:loading.attr="disabled">
        Opslaan
      </x-button>
    </x-slot>
  </x-dialog-modal>
</div>
