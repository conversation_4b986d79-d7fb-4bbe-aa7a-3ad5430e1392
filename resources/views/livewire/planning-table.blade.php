<div class="w-full overflow-x-auto">
  <style>
    /* Make DataTables use full width */
    .dataTables_wrapper {
      width: 100%;
    }
    .dataTables_scrollHeadInner, .dataTables_scrollHeadInner table {
      width: 100% !important;
    }
    .dataTables_scrollBody {
      width: 100% !important;
    }
    .dataTables_scrollBody table {
      width: 100% !important;
    }
  </style>
  <table id="purchase-table" class="table table-striped w-full"></table>

  @push('scripts')
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <script src="https://cdn.datatables.net/1.10.23/js/jquery.dataTables.min.js"></script>
    <script>
      document.addEventListener('livewire:init', function () {
        var table = $('#purchase-table').DataTable({
          processing: true,
          serverSide: true,
          stateSave: true,
          scrollX: true,
          responsive: true,
          autoWidth: false,
          ajax: '{{ route('livewire.planning-table.get-purchases') }}',
          columns: [
            { data: 'id', name: 'id', width: '40px' },
            { data: 'purchase_number', name: 'purchase_number', width: '80px'},
            { data: 'product.name', name: 'product.name', width: '120px' },
            { data: 'quantity', name: 'quantity', width: '80px'},
            { data: 'supplier.name', name: 'supplier.name', width: '120px' },
            { data: 'supplier.city', name: 'supplier.city', width: '100px'},
            {
              data: 'load_date',
              name: 'load_date',
              width: '90px',
              render: function (data, type, row) {
                var date = new Date(data);

                var day = ("0" + date.getDate()).slice(-2);
                var month = ("0" + (date.getMonth() + 1)).slice(-2); // getMonth() is zero-based
                var year = date.getFullYear();

                return day + '-' + month + '-' + year;
              }
            },
            {
              data: 'customers',
              name: 'customers',
              title: 'Klanten',
              width: '150px'
            },
            {
              data: 'addresses',
              name: 'addresses',
              title: 'Lossen',
              width: '150px'
            },
            {
              data: 'delivery_date',
              name: 'delivery_date',
              width: '90px',
              render: function (data, type, row) {
                var date = new Date(data);

                var day = ("0" + date.getDate()).slice(-2);
                var month = ("0" + (date.getMonth() + 1)).slice(-2); // getMonth() is zero-based
                var year = date.getFullYear();

                return day + '-' + month + '-' + year;
              }
            }
          ],
          columnDefs: [
            { title: "ID", targets: 0 },
            { title: "Rit nummer", targets: 1 },
            { title: "Product", targets: 2 },
            { title: "Aantal",
              targets: 3,
              render: function (data, type, row) {
                  return row.quantity + ' ' + row.qty_type;
              }
            },
            { title: "Leverancier", targets: 4 },
            { title: "Laden", targets: 5 },
            { title: "Laad datum", targets: 6 },
            { title: 'Klanten', target: 7},
            { title: 'Lossen', target: 8},
            { title: 'Los datum', target: 9},
            { title: 'Acties', target: 10, width: '80px'}
          ],
          order: [[0, 'desc']],
          rowCallback: function(row, data) {

            $(row).on('click', function() {
              // Redirect to the edit page using the 'id' of the clicked row
              window.location.href = '/handel/edit/' + data.id;
            });
          }
        });

      });
    </script>
  @endpush
</div>
