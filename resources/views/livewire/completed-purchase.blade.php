<div>
  @php use Carbon\Carbon; @endphp
  @if ($purchase->ready_for_planning == 1)
    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 mb-5">
      <h4>Transport</h4>
      <div class="flex justify-between">
        <div>
          @if ($selectedShipper)
            <strong>Transporteur</strong><br>
            {{ $selectedShipper->company_name }}<br>
            {{ $selectedShipper->street_name }} {{ $selectedShipper->housenumber }}<br>
            {{ $selectedShipper->postal_code }} {{ $selectedShipper->city }}<br>
          @else
            <x-button wire:click="$set('showShipperModal', true)">Transporteur selecteren</x-button>
          @endif
        </div>
        <div>
          @if ($purchase->planned == 1)

              <x-secondary-button>Stuur mail</x-secondary-button>

          @else

          @endif
          <table>
            <tr>
              <td class="pr-4"><strong>Exact</strong></td>
              <td><i class="fa-solid fa-xmark" style="color: #d51a2d;"></i></td>
            </tr>
            <tr>
              <td class="pr-4"><strong>Gefactureerd</strong></td>
              <td><i class="fa-solid fa-xmark" style="color: #d51a2d;"></i></td>
            </tr>
          </table>
        </div>
      </div>

    </div>
  @endif

  <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 mb-5">
    <h4>Inkoop</h4>
  <div class="flex justify-between">
    <div id="billing">
      <strong>Leverancier</strong><br>
      {{ $purchase->supplier->exact_id }}<br>
      {{ $purchase->supplier->name }}<br>
      {{ $purchase->supplier->street_name }} {{ $purchase->supplier->housenumber }}<br>
      {{ $purchase->supplier->postal_code }} {{ $purchase->supplier->city }}<br>
      {{ $purchase->supplier->email }}
    </div>
    <div>
      <strong><i class="fa-solid fa-arrow-down"></i> Laaddatum</strong>
{{--      <x-input id="laaddatum" class="block mt-1 w-full" type="date" wire:model.live="loadDate" />--}}
      {{ $loadDate }}
    </div>

    @if($purchase->load_weight || $purchase->unload_weight)
{{--    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">--}}
{{--      @if($purchase->load_weight)--}}
{{--      <div>--}}
{{--        <strong><i class="fa-solid fa-weight-hanging"></i> Laadgewicht</strong>--}}
{{--        <p>{{ number_format($purchase->load_weight, 2) }} kg</p>--}}
{{--      </div>--}}
{{--      @endif--}}

{{--      @if($purchase->unload_weight)--}}
{{--      <div>--}}
{{--        <strong><i class="fa-solid fa-weight-hanging"></i> Losgewicht</strong>--}}
{{--        <p>{{ number_format($purchase->unload_weight, 2) }} kg</p>--}}
{{--      </div>--}}
{{--      @endif--}}
{{--    </div>--}}
    @endif

  </div>
  <div id="purchase-products" class="mb-6">
    <table style="width: 100%">
      <thead>
      <tr  style="border-bottom: 2px solid #000; height: 45px;">
        <th style="width: 20%">SKU</th>
        <th style="width: 40%">Product</th>
        <th style="width: 10%">Aantal</th>
        <th style="width: 10%">Prijs</th>
        <th style="width: 10%">Totaal</th>
      </tr>
      </thead>
      <tbody>
        <tr style="border-bottom: 1px solid #ccc; height: 45px;">
          <td>{{ $purchase->product->sku }}</td>
          <td>{{ $purchase->product->name }}</td>
          <td>{{ $purchase->quantity }} {{ $purchase->qty_type }}</td>
          <td>&euro; {{ $purchase->price }}</td>
          <td>&euro; {{ $purchase->total }}</td>
        </tr>
      </tbody>
    </table>
  </div>
  </div>

  <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 mb-5">
    <h4>Verkoop</h4>
  <div id="customers">
    <div class="flex justify-between">
      <div>
        @if ($purchase->ready_for_planning == 0)
        <x-button wire:click="$set('showModal', true)">Nieuwe verkoop</x-button>
        @endif
      </div>
      <div>
        <strong><i class="fa-solid fa-arrow-up"></i> Losdatum</strong>
{{--        <x-input id="losdatum" class="block mt-1 w-full" type="date" wire:model.live="deliveryDate" />--}}
        {{ $deliveryDate }}
      </div>
    </div>
    <div id="sales" class="mb-6 mt-3">
      <table style="width: 100%">
        <thead>
        <tr  style="border-bottom: 2px solid #000; height: 45px;">
          <th style="width: 20%">Lossen</th>
          <th style="width: 10%">Aantal</th>
          <th style="width: 10%">Prijs per {{ $purchase->qty_type }}</th>
          <th style="width: 10%">Transport</th>
          <th style="width: 10%">Totaal</th>
        </tr>
        </thead>
        <tbody>
        @foreach ($purchase->sales as $sale)
          <tr style="border-bottom: 1px solid #ccc;">
            <td class="pt-3 pb-3">
              {{ $sale->customer->company_name }}<br>
              {{ $sale->address->street }} {{ $sale->address->housenumber }}<br>
              {{ $sale->address->postal_code }} {{ $sale->address->city }}
            </td>
            <td>
              @if($sale->actual_quantity && $sale->actual_quantity != $sale->quantity)
                <span class="line-through text-gray-500">{{ $sale->quantity }}</span>
                <span class="font-bold">{{ $sale->actual_quantity }}</span> {{ $purchase->qty_type }}
                <div class="text-xs text-gray-500">(Oorspronkelijk: {{ $sale->quantity }} {{ $purchase->qty_type }})</div>
              @else
                {{ $sale->quantity }} {{ $purchase->qty_type }}
              @endif
            </td>
            <td>&euro; {{ $sale->price }}</td>
            <td>&euro; {{ $sale->transport_cost }}</td>
            <td>&euro; {{ $sale->total_price + $sale->transport_cost }}</td>
          </tr>
        @endforeach
        </tbody>
      </table>
      <hr>
    </div>
  </div>
  <div class="flex font-bold">
    <div class="w-3/4"></div>
    <div class="w-1/4">
      <div class="flex justify-between">
        <div>
        Totaal inkoop
        </div>
        <div>
          &euro; {{ $purchase->total }}
        </div>
      </div>
      <div class="flex justify-between">
        <div>Totaal transport</div>
        <div>&euro; {{ $totalTransport }}</div>
      </div>
      <div class="flex justify-between">
        <div>Totaal verkoop</div>
        <div>&euro; {{ $totalPrice + $totalTransport }}</div>
      </div>
      @if ($purchase->ready_for_planning == 0)
        <div class="pt-3 flex justify-content-end">
          <x-button wire:click="sendToTransport">Stuur naar logistiek</x-button>
        </div>
      @endif
    </div>
  </div>
  </div>

  @if($purchase->signature)
    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 mb-5">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-lg font-semibold text-gray-900">
          <i class="fas fa-signature text-blue-600 mr-2"></i>
          Digitale Handtekening
        </h4>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <i class="fas fa-check-circle mr-1"></i>
          Ontvangen
        </span>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Signature Image -->
        <div class="lg:col-span-2">
          <div class="border-2 border-gray-200 rounded-lg p-4 bg-gray-50">
            <img src="{{ $purchase->signature }}"
                 alt="Digitale Handtekening"
                 class="max-w-full h-auto border border-gray-300 rounded bg-white shadow-sm"
                 style="max-height: 200px;">
          </div>
        </div>

        <!-- Signature Details -->
        <div class="space-y-4">
          <div>
            <h5 class="text-sm font-medium text-gray-700 mb-2">Details</h5>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-500">Ontvangen:</span>
                <span class="font-medium">
                  {{ $purchase->signature_received_at ? $purchase->signature_received_at->format('d-m-Y') : 'Onbekend' }}
                </span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">Tijd:</span>
                <span class="font-medium">
                  {{ $purchase->signature_received_at ? $purchase->signature_received_at->format('H:i') : 'Onbekend' }}
                </span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">Status:</span>
                <span class="text-green-600 font-medium">Geldig</span>
              </div>
            </div>
          </div>

          <div class="pt-4 border-t border-gray-200">
            <button onclick="downloadSignatureCompleted()" class="w-full px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition duration-150 ease-in-out">
              <i class="fas fa-download mr-2"></i>
              Download Handtekening
            </button>
          </div>
        </div>
      </div>
    </div>

    <script>
      function downloadSignatureCompleted() {
        const link = document.createElement('a');
        link.href = '{{ $purchase->signature }}';
        link.download = 'handtekening-{{ $purchase->purchase_number }}.png';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    </script>
  @endif
    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 mb-5">
      <h4>Correspondentie</h4>
      <hr>
      @foreach ($shipperMails as $shipperMail)
        <div>
          <strong>{{ Carbon::create($shipperMail->created_at)->format('d-m-Y H:i') }}</strong><br>
          <strong>Aan: </strong> {{ $shipperMail->mail_to }} <br><br>
          <strong>Bericht</strong><br/>
          {{ $shipperMail->mail_message }}<br><br>
          <strong>Bijlages</strong><br><br>
          <div class="flex">
            <div class="flex flex-column">
              <i class="fa-solid fa-file-pdf fa-2xl" style="height: 18px;"></i>
              <small><strong>CMR</strong></small>
            </div>
          </div>
        </div>
        <hr>
      @endforeach
    </div>

  <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 mb-5">
    <h4>Bewerkingen</h4>
    <ul class="list-disc">
      {!! $log !!}
    </ul>
  </div>



  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Verkoop toevoegen
    </x-slot>

    <x-slot name="content">
      <div class="mb-4">
        <x-label for="customer" value="Klant" />
        <div class="@if(!empty($selectedCustomer)) hidden @endif">
          <input
                  type="text"
                  wire:model.live.debounce.500ms="search"
                  placeholder="Zoek klant..."
          />
          @unless($customers->isEmpty())
            <div class="bg-gray-100" style="max-height: 400px; overflow-x: scroll; border: 1px solid #4b5563">
              @foreach($customers as $customer)
                <p
                        wire:click="selectCustomer({{ $customer->id }})"
                        class="cursor-pointer hover:bg-gray-50 py-2 px-2"
                        style="margin-bottom: 0px !important;"
                >
                  {{ $customer->exact_id }} - {{ $customer->company_name }} ({{$customer->city}})
                </p>
              @endforeach
            </div>
          @endunless
        </div>
        @if(!empty($selectedCustomer))
          <h5>{{ $selectedCustomer->exact_id }} - {{ $selectedCustomer->company_name }} ({{$selectedCustomer->city}})</h5>
          <h5>Losadres</h5>
          <div class="mb-4 w-2/3">
            <x-label for="straat" value="Straat + huisnummer" />
            <div class="flex gap-4">
              <x-input type="text" id="straat" wire:model.live="street" class="w-2/3" />
              <x-input type="text" id="housenumber" wire:model.live="housenumber" class="w-1/3"/>
            </div>
          </div>
          <div class="mb-4 w-2/3">
            <x-label for="postal" value="Postcode + plaats" />
            <div class="flex gap-4">
              <x-input type="text" id="postal" wire:model.live="postalCode" class="w-1/3" />
              <x-input type="text" id="city" wire:model.live="city" class="w-2/3"/>
            </div>
          </div>
        @endif
      </div>
      <hr>
      <div class="mb-4">
        <x-label for="aantal" value="Aantal" />
        <div class="flex align-items-baseline gap-4">
          <x-input id="aantal" class="block mt-1 w-1/3" type="text" wire:model.live="saleQty" /> <div><strong>{{ $purchase->qty_type }}</strong></div>
        </div>
      </div>
      <div class="mb-4">
        <x-label for="price" value="Prijs per {{$purchase->qty_type}}" />
        <x-input id="price" class="block mt-1 w-1/4" type="text" wire:model.live="salePrice" />
      </div>
      <div class="mb-4">
        <x-label for="transport" value="Transport prijs" />
        <x-input id="transport" class="block mt-1 w-1/4" type="text" wire:model.live="transportPrice" />
      </div>
      @if($errorMessage)
        <div class="bg-red-100 p-4">
          {{ $errorMessage }}
        </div>
      @endif
    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="$set('showModal', false)" wire:loading.attr="disabled">
        Annuleren
      </x-secondary-button>

      <x-button class="ml-2" wire:click.prevent="addSale" wire:loading.attr="disabled">
        Opslaan
      </x-button>
    </x-slot>
  </x-dialog-modal>

    <x-dialog-modal wire:model.live="showShipperModal">
      <x-slot name="title">
        Transporteur selecteren
      </x-slot>

      <x-slot name="content">
        <div class="mb-4">
          <x-label for="transporter" value="Transporteur" />
          <div class="@if(!empty($selectedShipper)) hidden @endif">
            <input
                    type="text"
                    wire:model.live.debounce.500ms="shipperSearch"
                    placeholder="Zoek transporteur..."
            />
            @unless($shippers->isEmpty())
              <div class="bg-gray-100" style="max-height: 400px; overflow-x: scroll; border: 1px solid #4b5563">
                @foreach($shippers as $shipper)
                  <p
                          wire:click="selectShipper({{ $shipper->id }})"
                          class="cursor-pointer hover:bg-gray-50 py-2 px-2"
                          style="margin-bottom: 0px !important;"
                  >
                    {{ $shipper->company_name }} ({{$shipper->city}})
                  </p>
                @endforeach
              </div>
            @endunless
          </div>
          @if(!empty($selectedShipper))
            <h5>{{ $selectedShipper->company_name }} ({{$selectedShipper->city}})</h5>
          @endif
        </div>
        <hr>
      </x-slot>

      <x-slot name="footer">
        <x-secondary-button wire:click="$set('showShipperModal', false)" wire:loading.attr="disabled">
          Annuleren
        </x-secondary-button>

        <x-button class="ml-2" wire:click.prevent="createAndSelectShipper" wire:loading.attr="disabled">
          Selecteer Transporteur
        </x-button>
      </x-slot>
    </x-dialog-modal>

    <x-dialog-modal wire:model.live="showShipperMailModal">
      <x-slot name="title">
        Transporteur mailen
      </x-slot>

      <x-slot name="content">
        <div class="mb-4">
          <x-label for="mailto" value="Email adres" />
          <x-input id="transport" class="block mt-1 w-full" type="text" wire:model.live="shipperMail" />
        </div>
        <div class="mb-4">
{{--          <x-label for="message" value="Bericht" />--}}
{{--          <textarea wire:model="messageTextShipper" class="w-full" rows="5"></textarea>--}}
          <x-trix-input-livewire id="description" :toolbar="'minimal'" name="description"  autocomplete="off" />
        </div>
        <div>
          <label class="toggle">
            <input class="toggle-checkbox" type="checkbox">
            <div class="toggle-switch"></div>
            <span class="toggle-label">Maak CMR en voeg bij als bijlage</span>
          </label>
        </div>
        <br>
        <x-filepond::upload wire:model="file" />
      </x-slot>

      <x-slot name="footer">
        <x-secondary-button wire:click="$set('showShipperMailModal', false)" wire:loading.attr="disabled">
          Annuleren
        </x-secondary-button>

        <x-button class="ml-2" wire:click.prevent="sendShipperMail" wire:loading.attr="disabled">
          Verstuur
        </x-button>
      </x-slot>
    </x-dialog-modal>

  <x-dialog-modal wire:model.live="showCompleteModal">
    <x-slot name="title">
      Transporteur mailen
    </x-slot>

    <x-slot name="content">
      <div class="mb-4">
        <x-label for="transport" value="Werkelijke transportkosten" />
        <x-input id="transport" class="block mt-1 w-full" type="text" wire:model.live="totalTransport" />
      </div>
    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="$set('showCompleteModal', false)" wire:loading.attr="disabled">
        Annuleren
      </x-secondary-button>

      <x-button class="ml-2" wire:click.prevent="complete" wire:loading.attr="disabled">
        Verstuur
      </x-button>
    </x-slot>
  </x-dialog-modal>


</div>
