<div>
  <div class="mb-4">
    @foreach($selectedGroups as $group)
      @php
        $groupModel = \App\Models\ProductGroup::find($group);
      @endphp
      <span class="bg-indigo-300 rounded-3 text-white mr-2 relative" style="padding-left: 5px; padding-top: 5px; padding-right: 25px; padding-bottom: 5px;">
                {{ $groupModel->name }}
                <i class="fa-regular fa-trash-can absolute cursor-pointer" wire:click="removeProductGroup({{ $group }})" style="top: 6px; right: 5px;"></i>
            </span>
    @endforeach
  </div>

  <input type="hidden" name="product_groups" wire:model.live="selectedGroupsForInput">
  <select wire:model.live="dropdownValue" wire:change="addProductGroup" class="block mt-1 w-1/3 rounded-md shadow-sm">
    <option value="">Productgroep toevoegen...</option>
    @foreach($productGroups as $group)
      @if(!in_array($group->id, $selectedGroups))
        <option value="{{ $group->id }}">{{ $group->name }}</option>
      @endif
    @endforeach
  </select>
</div>
