@php
  use Illuminate\Support\Facades\Auth;
@endphp
<div>
  <div class="flex justify-between">
    <h4>Communicatie</h4>
    <x-button wire:click="$set('showModal', true)"><i class="fa-solid fa-plus" style="color: #ffffff; margin-right: 4px;"></i> Communicatie toevoegen</x-button>
  </div>
  <div class="flex">
    <div class="mr-3">
      <input type="date" wire:model.live="startDate">
    </div>
    <div class="mr-3">
      <label class="mr-2">Tot:</label>
      <input type="date" wire:model.live="endDate">
    </div>
  </div>
  <div class="mt-4">
    @if ($this->interactions)
      @foreach($this->interactions as $interaction)
        <div class="rounded bg-gray-200 p-4 mb-4 w-49 relative">
          @if(Auth::id() === $interaction->user_id)
            <div class="absolute" style="right: 10px; top: 10px;">
              <i wire:click="removeInteraction({{ $interaction->id }})"
                 class="fa-solid fa-trash-can cursor-pointer"></i>
            </div>
          @endif
          <p>{{ $interaction->note }}</p>
          <small>{{ $interaction->type }} | {{ $interaction->created_at }} | {{ $interaction->user->name }}</small>
        </div>
      @endforeach
    @endif
  </div>
  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Communicatie toevoegen
    </x-slot>

    <x-slot name="content">
      <div class="flex flex-wrap">
        <div class="w-full mt-4">
          <x-label for="interactionType" value="{{ __('Type') }}"/>
          <select id="interactionType" wire:model.live="interactionType" class="mt-1 block w-full">
            <option value="">-- Select Type --</option>
            <option value="call">Telefoon</option>
            <option value="meeting">Gesprek</option>
            <option value="email">Email</option>
          </select>
          @error('interactionType') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
        </div>
        <div class="w-full mt-4">
          <x-label for="interactionNote" value="{{ __('Notitie') }}"/>
          <textarea rows="4" wire:model.live="interactionNote"
                    class="peer h-full min-h-[100px] w-full resize-none rounded-[7px] border border-blue-gray-200 border-t-transparent bg-transparent px-3 py-2.5 font-sans text-sm font-normal text-blue-gray-700 outline outline-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 focus:border-2 focus:border-pink-500 focus:border-t-transparent focus:outline-0 disabled:resize-none disabled:border-0 disabled:bg-blue-gray-50 mb-4"></textarea>
          @error('interactionNote') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
        </div>
        <div class="w-full mt-8">
          <x-label for="interactionNote" value="{{ __('Stuur notificatie') }}"/>
          <x-checkbox name="notify" wire:model.live="notify"></x-checkbox>
          <span style="padding-left: 10px; padding-right: 10px;"> naar </span>
          <select wire:model.live="ownerId">
            <option value="" {{ empty($ownerId) ? 'selected' : '' }}>Kies gebruiker</option>
            @foreach($users as $user)
              <option value="{{ $user->id }}" {{ $ownerId == $user->id ? 'selected' : '' }}>{{ $user->name }}</option>
            @endforeach
          </select>
        </div>
      </div>
    </x-slot>

    <x-slot name="footer">
      <x-button wire:click="save">Opslaan</x-button>
    </x-slot>
  </x-dialog-modal>
</div>
