<div>
  <div x-data="fileUpload()">
    <input type="file" x-ref="file" @change="uploadFile">

    <!-- Progress bar (optional) -->
    <div x-show="uploadProgress">
      <div style="width: x-text=`${uploadProgress}%`">Upload Progress: x-text="uploadProgress"%</div>
    </div>
  </div>
  <div class="mt-4">
    <h5>Bijlagen:</h5>
    <ul>
      @foreach($attachments as $attachment)
        <li>
          {{ preg_replace('/^\d+_/', '', basename($attachment->filename)) }}
          <a href="{{ $attachment->downloadUrl }}" target="_blank">Download {{ $attachment->name }}</a>
          <button class="text-danger ml-4" wire:click="deleteAttachment({{ $attachment->id }})">Delete</button>
        </li>
      @endforeach
    </ul>
  </div>
  <script>
    function fileUpload() {
      return {
        uploadProgress: 0,
        entityId: '{{ $model->id }}',
        type: '{{ $type }}',

        uploadFile() {
          Vapor.store(this.$refs.file.files[0], {
            progress: progress => {
              this.uploadProgress = Math.round(progress * 100);
            }
          }).then(response => {
            axios.post('/api/fileupload', {
              uuid: response.uuid,
              key: response.key,
              bucket: response.bucket,
              name: this.$refs.file.files[0].name,
              content_type: this.$refs.file.files[0].type,
              entity_id: this.entityId,
              type: this.type,
            })
                .then(response => {
                  this.uploadProgress = 0;
                })
                .catch(error => {
                  this.uploadProgress = 0;
                });
          });
        }
      }
    }
  </script>
</div>
