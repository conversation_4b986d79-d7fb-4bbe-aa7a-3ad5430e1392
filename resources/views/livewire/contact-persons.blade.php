<div>
  <div>
    <div class="flex justify-between">
      <h4>Contactpersonen</h4>
      <x-button wire:click="$set('showModal', true)"><i class="fa-solid fa-plus" style="color: #ffffff; margin-right: 4px;"></i> Contactpersoon toevoegen</x-button>
    </div>

    <div class="w-full mt-2 flex justify-between flex-wrap">
      @foreach($contactable->contactPersons as $contactPerson)
        <div class="rounded bg-gray-200 p-4 mb-4 w-49 relative">
          <i class="fa-solid fa-user"></i> {{ $contactPerson->name }}<br>
          <i class="fa-solid fa-at"></i> {{ $contactPerson->email }}<br>
          <i class="fa-solid fa-phone"></i> {{ $contactPerson->phone_number }}<br>
          <i class="fa-solid fa-mobile-screen"></i> {{ $contactPerson->mobile_phone_number }}<br>
          <i class="fa-solid fa-note-sticky"></i> {{ $contactPerson->note }}
          <i class="fa-regular fa-pen-to-square absolute cursor-pointer" style="top: 10px; right: 10px;" wire:click="edit({{ $contactPerson->id }})"></i>
        </div>
      @endforeach
    </div>

  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Contactpersoon toevoegen
    </x-slot>

    <x-slot name="content">
      <div class="mb-4">
        <x-label for="name" value="Naam" />
        <x-input id="name" class="block mt-1 w-full" type="text" wire:model.live="name" autofocus />
        @error('name') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>
      <div class="mb-4">
        <x-label for="email" value="Email" />
        <x-input id="email" class="block mt-1 w-full" type="text" wire:model.live="email" />
        @error('email') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>
      <div class="mb-4">
        <x-label for="phoneNumber" value="Telefoonnummer" />
        <x-input id="phoneNumber" class="block mt-1 w-full" type="text" wire:model.live="phoneNumber" />
        @error('phoneNumber') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>
      <div class="mb-4">
        <x-label for="mobileNumber" value="Mobiel" />
        <x-input id="mobileNumber" class="block mt-1 w-full" type="text" wire:model.live="mobileNumber" />
      </div>
      <div class="mb-4">
        <x-label for="note" value="Notitie" />
        <textarea rows="4" wire:model.live="note" class="peer h-full min-h-[100px] w-full resize-none rounded-[7px] border border-blue-gray-200 border-t-transparent bg-transparent px-3 py-2.5 font-sans text-sm font-normal text-blue-gray-700 outline outline-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 focus:border-2 focus:border-pink-500 focus:border-t-transparent focus:outline-0 disabled:resize-none disabled:border-0 disabled:bg-blue-gray-50 mb-4"></textarea>
      </div>
    </x-slot>

    <x-slot name="footer">
      <x-button wire:click="save">Opslaan</x-button>
    </x-slot>
  </x-dialog-modal>
    <x-dialog-modal wire:model.live="showEditModal">
      <x-slot name="title">
        Contactpersoon wijzigen
      </x-slot>

      <x-slot name="content">
        <div class="mb-4">
          <x-label for="name" value="Naam" />
          <x-input id="name" class="block mt-1 w-full" type="text" wire:model.live="editName" autofocus />
        </div>
        <div class="mb-4">
          <x-label for="email" value="Email" />
          <x-input id="email" class="block mt-1 w-full" type="text" wire:model.live="editEmail" />
        </div>
        <div class="mb-4">
          <x-label for="phoneNumber" value="Telefoonnummer" />
          <x-input id="phoneNumber" class="block mt-1 w-full" type="text" wire:model.live="editPhoneNumber" />
        </div>
        <div class="mb-4">
          <x-label for="mobileNumber" value="Mobiel" />
          <x-input id="mobileNumber" class="block mt-1 w-full" type="text" wire:model.live="editMobileNumber" />
        </div>
        <div class="mb-4">
          <x-label for="note" value="Notitie" />
          <textarea rows="4" wire:model.live="editNote" class="peer h-full min-h-[100px] w-full resize-none rounded-[7px] border border-blue-gray-200 border-t-transparent bg-transparent px-3 py-2.5 font-sans text-sm font-normal text-blue-gray-700 outline outline-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 focus:border-2 focus:border-pink-500 focus:border-t-transparent focus:outline-0 disabled:resize-none disabled:border-0 disabled:bg-blue-gray-50 mb-4"></textarea>
        </div>
      </x-slot>

      <x-slot name="footer">
        <div class="flex justify-between" style="width: 100%;">
          <x-danger-button wire:click="toggleDeleteModal" wire:loading.attr="disabled">
            <i class="fa-solid fa-trash-can" style="color: #ffffff; margin-right: 4px;"></i> Verwijder
          </x-danger-button>
          <x-button wire:click="update">Opslaan</x-button>
        </div>
      </x-slot>
    </x-dialog-modal>
    <x-dialog-modal wire:model.live="showRemoveModal">
      <x-slot name="title">
        Contactpersoon verwijderen
      </x-slot>

      <x-slot name="content">
        Weet je zeker dat je deze contactpersoon wil verwijderen?
      </x-slot>

      <x-slot name="footer">
        <x-danger-button wire:click="removeContact">Ja, verwijder</x-danger-button>
      </x-slot>
    </x-dialog-modal>
  </div>
</div>
