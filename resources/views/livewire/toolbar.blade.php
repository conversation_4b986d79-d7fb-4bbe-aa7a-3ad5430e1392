<div class="flex w-full">
    <div class="flex align-items-baseline w-1/3">
      <x-checkbox wire:model.live="until" style="margin-right: 4px;" />
      <x-label for="tonen" value="t/m" class="mr-3"/>
      <button
          type="button"
          class="px-2 py-1 rounded-l {{ $until ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-300' }}"
          wire:click="previousDay"

      >
          <i class="fas fa-chevron-left"></i>
      </button>
      <x-input id="tonen" class="block mt-1 rounded-none" type="date" wire:model.live="showDate" />
      <button
          type="button"
          class="px-2 py-1 rounded-r {{ $until ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-300' }}"
          wire:click="nextDay"

      >
          <i class="fas fa-chevron-right"></i>
      </button>
    </div>
    <div class="flex align-items-baseline w-1/3">
      <x-label for="administratie" class="mr-3" value="Administratie" />
      <select id="administratie" class="form-input rounded-md shadow-sm mt-1 block" wire:model.live="administration">
        <option value="">Selecteer administratie</option>
        <option value="30">030 Fouragehandel Bus</option>
        <option value="33">033 Logi.Span Resthout</option>
        <option value="36">036 Logi.Span Retail</option>
        <option value="37">037 Logi.Span Biomass</option>
      </select>
    </div>
    <div class="flex align-items-baseline w-1/3 justify-end gap-2">

        <x-button wire:click="show">Toon</x-button>
    </div>
</div>

