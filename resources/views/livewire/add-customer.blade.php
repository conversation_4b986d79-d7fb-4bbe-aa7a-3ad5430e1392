<div>
  <x-button wire:click="$set('showModal', true)"><i class="fa-solid fa-plus" style="color: #ffffff; margin-right: 4px;"></i> <PERSON><PERSON> toevoegen</x-button>
  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      <PERSON><PERSON> toevoegen
    </x-slot>

    <x-slot name="content">
      <div class="flex items-center mb-6">
        <div class="w-1/3">
          <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="exact_id" value="{{ __('Exact ID') }}" />
        </div>
        <div class="w-2/3">
          <x-input id="exact_id" class="block mt-1 w-full" type="text" name="exact_id" wire:model.live="exactId" />
          @error('exactId') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
        </div>
      </div>
      <div class="flex items-center mb-6">
        <div class="w-1/3">
          <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="company_name" value="{{ __('Bedrijfsnaam') }}" />
        </div>
        <div class="w-2/3">
          <x-input id="company_name" class="block mt-1 w-full" type="text" name="company_name" wire:model.live="name" />
          @error('name') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
        </div>
      </div>
      <div class="flex items-center mb-6">
        <div class="w-1/3">
          <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="name" value="{{ __('Contactpersoon') }}" />
        </div>
        <div class="w-2/3">
          <x-input id="name" class="block mt-1 w-full" type="text" name="name" wire:model.live="contactPerson"/>
          @error('contactPerson') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
        </div>
      </div>
      <div class="flex items-center mb-6">
        <div class="w-1/3">
          <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="telephone" value="{{ __('Telefoon nummer') }}" />
        </div>
        <div class="w-2/3">
          <x-input id="telephone" class="block mt-1 w-full" type="text" name="telephone" wire:model.live="telephone" />
          @error('telephone') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
        </div>
      </div>
      <div class="flex items-center mb-6">
        <div class="w-1/3">
          <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="street" value="{{ __('Straat + huisnummer') }}" />
        </div>
        <div class="w-2/3 flex">
          <x-input id="street" class="block mt-1 w-full mr-2" type="text" name="street" wire:model.live="street" />
          @error('street') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
          <x-input id="housenumber" class="block mt-1 w-25" type="text" name="housenumber" wire:model.live="housenumber" />
          @error('housenumber') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
        </div>
      </div>
      <div class="flex items-center mb-6">
        <div class="w-1/3">
          <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="postal_code" value="{{ __('Postcode + Plaats') }}" />
        </div>
        <div class="w-2/3 flex">
          <x-input id="postal_code" class="block mt-1 w-25 mr-2" type="text" name="postal_code" wire:model.live="postalCode" />
          @error('postalCode') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
          <x-input id="city" class="block mt-1 w-full" type="text" name="city" wire:model.live="city" />
          @error('city') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
        </div>
      </div>
      <div class="flex items-center mb-6">
        <div class="w-1/3">
          <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4" for="product_groups" value="{{ __('Product Groepen') }}" />
        </div>
        <div class="w-2/3">
          <select id="product_groups" name="product_groups[]" wire:model.live="selectedProductGroups" multiple>
            @foreach($allProductGroups as $group)
              <option value="{{ $group->id }}">{{ $group->name }}</option>
            @endforeach
          </select>
          @error('selectedProductGroups') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
        </div>
      </div>
      @if($errorMessage)
        <div class="bg-red-100 p-4">
          {{ $errorMessage }}
        </div>
      @endif
    </x-slot>

    <x-slot name="footer">
      <x-button wire:click="saveCustomer" class="ml-2">Opslaan</x-button>
    </x-slot>
  </x-dialog-modal>
</div>
