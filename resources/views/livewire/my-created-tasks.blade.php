<div>
  <div class="flex-column cursor-pointer">
    @foreach ($actions as $action)
      <div class="rounded bg-gray-200 p-2 mb-2 hover:bg-gray-400" wire:click="showModal({{ $action }})">
        <div class="flex justify-between mb-2">
          {!! nl2br(e($action->description)) !!}
        </div>
        <hr>
        <div>Datum: @if(strtotime($action->due_date) < strtotime(now()))
            <span style="color: red; font-weight: bold">{{ $action->due_date }}</span>
          @else
            {{ $action->due_date }}
          @endif</div>
        <span>Toegewezen aan: {{ $action->assignedTo->name }}</span><br>
        <strong>{{ $action->actionable->company_name }}</strong> <br>
      </div>
    @endforeach
  </div>

  <x-dialog-modal wire:model.live="isModalOpen">
    <x-slot name="title">
      Taak Details
    </x-slot>

    <x-slot name="content">
      @if($modalTask)
        <p>@php
            $routeName = '';
            switch($modalTask->actionable_type) {
                case \App\Models\Customer::class:
                    $routeName = 'customer.edit';
                    $routeArg = 'customer';
                    break;
                case \App\Models\Supplier::class:
                    $routeName = 'supplier.edit';
                    $routeArg = 'supplier';
                    break;
                case \App\Models\Prospect::class:
                    $routeName = 'prospect.edit';
                    $routeArg = 'prospect';
                    break;
                default:
                    break;
            }
          @endphp

          @if($routeName)
            <a href="{{ route($routeName, [$routeArg => $modalTask->actionable->id]) }}">
              {{ $modalTask->actionable->company_name }}
            </a><br>
            <i class="fa-solid fa-phone"></i> {{ $modalTask->actionable->telephone }}<br>
        </p>
      @endif
      <p><strong>Type:</strong><br>
        {{ ucfirst($modalTask->type) }}
      </p>
      <div class="mb-4">
        <x-label for="due-date" value="Datum" />
        <x-input id="due-date" class="block mt-1" style="width: 9rem;" type="date" wire:model.live="dueDate" />
      </div>
      <div class="w-full mt-4">
        <x-label for="interactionNote" value="{{ __('Toegewezen aan') }}" />
        <select wire:model.live="ownerId">
          <option value="" {{ empty($ownerId) ? 'selected' : '' }}>Kies gebruiker</option>
          @foreach($users as $user)
            <option value="{{ $user->id }}" {{ $ownerId == $user->id ? 'selected' : '' }}>{{ $user->name }}</option>
          @endforeach
        </select>
      </div>
      <div class="mt-4">
        <x-label for="note" value="Omschrijving" />
        <textarea rows="4" wire:model.live="description" class="peer h-full min-h-[100px] w-full resize-none rounded-[7px] border border-blue-gray-200 border-t-transparent bg-transparent px-3 py-2.5 font-sans text-sm font-normal text-blue-gray-700 outline outline-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 focus:border-2 focus:border-pink-500 focus:border-t-transparent focus:outline-0 disabled:resize-none disabled:border-0 disabled:bg-blue-gray-50 mb-2"></textarea>
        <small>Type @@ voor datum</small>
      </div>
      @endif

      <x-button wire:click="addTaskNote">Opslaan</x-button>
    </x-slot>

    <x-slot name="footer">
      <div class="w-full flex justify-between">
        <x-danger-button wire:click="deleteTask">Verwijder</x-danger-button>
        <div>
          <x-secondary-button wire:click="$set('isModalOpen', false)" wire:loading.attr="disabled">
            Sluiten
          </x-secondary-button>
          <x-button wire:click="completeTask" class="ml-2">Actie afronden</x-button>
        </div>
      </div>
    </x-slot>
  </x-dialog-modal>
</div>
