<div>
  @php use Carbon\Carbon; @endphp

  @if (session()->has('message'))
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
      <span class="block sm:inline">{{ session('message') }}</span>
    </div>
  @endif

  @if (session()->has('error'))
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
      <span class="block sm:inline">{{ session('error') }}</span>
    </div>
  @endif

  @if ($purchase->ready_for_planning == 1)
    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 mb-5">
      <h4>Transport</h4>
      <div class="flex justify-between">
        <div>
          <!-- Own Transport Option -->
          <div class="mb-4">
            <label class="flex items-center">
              <input type="checkbox" wire:model.live="ownTransport" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
              <span class="ml-2 text-sm text-gray-600">Eigen transport</span>
            </label>
            <p class="mt-1 text-xs text-gray-500">Vink aan als het bedrijf zelf het transport verzorgt</p>
          </div>

          @if($ownTransport)
            <!-- Own Transport Selected -->
            <div class="p-3 border border-green-300 rounded-md bg-green-50">
              <strong class="text-green-800">Eigen transport</strong><br>
              <span class="text-green-600">Het bedrijf verzorgt zelf het transport</span>
            </div>
          @else
            <!-- Shipper Selection -->
            @if ($selectedShipper)
              <strong>Transporteur</strong><br>
              {{ $selectedShipper->company_name }}<br>
              {{ $selectedShipper->street_name }} {{ $selectedShipper->housenumber }}<br>
              {{ $selectedShipper->postal_code }} {{ $selectedShipper->city }}<br>
              <strong>Email:</strong> {{ $selectedShipper->email_address ?: 'Geen email' }}<br>
              <button wire:click="$set('showShipperModal', true)" class="mt-2 text-sm text-blue-600 hover:text-blue-800">
                Wijzig transporteur
              </button>
            @else
              <x-button wire:click="$set('showShipperModal', true)">Transporteur selecteren</x-button>
            @endif
          @endif
        </div>
        <div>
          @if ($purchase->planned == 1)

              <x-secondary-button>Stuur mail</x-secondary-button>

          @else
            @if ($selectedShipper || $ownTransport)
              <div class="flex flex-column column-gap-1">
                <x-button class="mb-2" wire:click="$set('showCompleteModal', true)">Complete</x-button>
                <x-secondary-button wire:click="$set('showShipperMailModal', true)">Mail Transporteur</x-secondary-button>
              </div>
            @endif
          @endif
        </div>
      </div>

    </div>
  @endif

  <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 mb-5">
    <h4>Inkoop</h4>
  <div class="flex justify-between">
    <div id="billing">
      <div class="flex justify-between items-start mb-2">
        <strong>Leverancier</strong>
      </div>
      {{ $purchase->supplier->exact_id }}<br>
      {{ $purchase->supplier->company_name }}<br>
      @if($customSupplierAddress)
        {{ $purchase->supplier_street }} {{ $purchase->supplier_housenumber }}<br>
        {{ $purchase->supplier_postal_code }} {{ $purchase->supplier_city }}<br>
        {{ $purchase->supplier_country }}
        @if($purchase->supplier_latitude && $purchase->supplier_longitude)
          <br><span class="text-xs text-gray-500">GPS: {{ number_format($purchase->supplier_latitude, 5) }}, {{ number_format($purchase->supplier_longitude, 5) }}</span>
        @endif
      @else
        {{ $purchase->supplier->street_name }} {{ $purchase->supplier->housenumber }}<br>
        {{ $purchase->supplier->postal_code }} {{ $purchase->supplier->city }}<br>
        {{ $purchase->supplier->country }}
        @if($purchase->supplier->latitude && $purchase->supplier->longitude)
          <br><span class="text-xs text-gray-500">GPS: {{ number_format($purchase->supplier->latitude, 5) }}, {{ number_format($purchase->supplier->longitude, 5) }}</span>
        @endif
      @endif
      <br>
      {{ $purchase->supplier->email }}

    </div>

    <div>
      <strong><i class="fa-solid fa-arrow-down"></i> Laaddatum</strong>
      <x-input id="laaddatum" class="block mt-1 w-full" type="date" wire:model.live="loadDate" />
    </div>

{{--    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">--}}
{{--      <div>--}}
{{--        <strong><i class="fa-solid fa-weight-hanging"></i> Laadgewicht</strong>--}}
{{--        <div class="flex items-center">--}}
{{--          <x-input id="loadWeight" class="block mt-1 w-full" type="number" step="0.01" wire:model.live="loadWeight" placeholder="0.00" />--}}
{{--          <span class="ml-2 text-sm text-gray-600"></span>--}}
{{--        </div>--}}
{{--      </div>--}}

{{--      <div>--}}
{{--        <strong><i class="fa-solid fa-weight-hanging"></i> Losgewicht</strong>--}}
{{--        <div class="flex items-center">--}}
{{--          <x-input id="unloadWeight" class="block mt-1 w-full" type="number" step="0.01" wire:model.live="unloadWeight" placeholder="0.00" />--}}
{{--          <span class="ml-2 text-sm text-gray-600"></span>--}}
{{--        </div>--}}
{{--      </div>--}}
{{--    </div>--}}

  </div>
  <div id="purchase-products" class="mb-6">
    <table style="width: 100%">
      <thead>
      <tr  style="border-bottom: 2px solid #000; height: 45px;">
        <th style="width: 20%">SKU</th>
        <th style="width: 40%">Product</th>
        <th style="width: 10%">Aantal</th>
        <th style="width: 10%">Prijs</th>
        <th style="width: 10%">Totaal</th>
      </tr>
      </thead>
      <tbody>
        <tr style="border-bottom: 1px solid #ccc; height: 45px;">
          <td>{{ $purchase->product->sku }}</td>
          <td>{{ $purchase->product->name }}</td>
          <td>
            <div class="flex items-center space-x-2">
              <x-input
                type="number"
                step="0.01"
                min="0.01"
                wire:model.live="purchaseQuantity"
                wire:loading.attr="disabled"
                wire:target="updatedPurchaseQuantity"
                class="w-20 text-sm"
                title="Klik om hoeveelheid te wijzigen"
              />
              <span class="text-sm">{{ $purchase->qty_type }}</span>
            </div>
          </td>
          <td>&euro; {{ $purchase->price }}</td>
          <td>&euro; {{ number_format((float)$purchase->quantity * (float)$purchase->price, 2, ',', '.') }}</td>
        </tr>
      </tbody>
    </table>
  </div>
  </div>

  <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 mb-5">

  <div id="customers">
    <div class="flex justify-between">
      <h4>Verkoop</h4>
      <div>
        <strong><i class="fa-solid fa-arrow-up"></i> Losdatum</strong>
        <x-input id="losdatum" class="block mt-1 w-full" type="date" wire:model.live="deliveryDate" />
      </div>
    </div>
    <div id="sales" class="mb-6 mt-3">
      <table style="width: 100%">
        <thead>
        <tr  style="border-bottom: 2px solid #000; height: 45px;">
          <th style="width: 20%">Lossen</th>
          <th style="width: 10%">Aantal</th>
          <th style="width: 10%">Prijs per {{ $purchase->qty_type }}</th>
          <th style="width: 10%">Transport</th>
          <th style="width: 10%">Totaal</th>
          <th style="width: 10%">Klant inkoopnr.</th>
          <th style="width: 3%"></th>
        </tr>
        </thead>
        <tbody>
        @foreach ($purchase->sales as $sale)
          <tr style="border-bottom: 1px solid #ccc;">
            <td class="pt-3 pb-3">
              {{ $sale->customer->company_name }}<br>
              {{ $sale->address->street }} {{ $sale->address->housenumber }}<br>
              {{ $sale->address->postal_code }} {{ $sale->address->city }}
              @if($sale->address->latitude && $sale->address->longitude)
                <br><span class="text-xs text-gray-500">GPS: {{ number_format($sale->address->latitude, 5) }}, {{ number_format($sale->address->longitude, 5) }}</span>
              @endif
            </td>
            <td>
              <div class="flex items-center space-x-2">
                <x-input
                  type="number"
                  step="0.01"
                  min="0.01"
                  wire:model.live="saleQuantities.{{ $sale->id }}"
                  wire:change="updateSaleQuantity({{ $sale->id }}, $event.target.value)"
                  wire:loading.attr="disabled"
                  wire:target="updateSaleQuantity"
                  class="w-20 text-sm"
                  title="Klik om hoeveelheid te wijzigen"
                />
                <span class="text-sm">{{ $purchase->qty_type }}</span>
              </div>
              @if($sale->actual_quantity && $sale->actual_quantity != $sale->quantity)
                <div class="text-xs text-gray-500 mt-1">
                  Werkelijk: {{ $sale->actual_quantity }} {{ $purchase->qty_type }}
                </div>
              @endif
            </td>
            <td>&euro; {{ $sale->price }}</td>
            <td>
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <span>&euro; {{ number_format($sale->transport_cost, 2, ',', '.') }}</span>
                  @if($sale->transport_cost > 0 && $this->isTransportCostPerUnit($sale))
                    <span class="ml-1 text-xs text-green-600" title="Transport kosten worden automatisch aangepast bij hoeveelheidswijziging">
                      📊
                    </span>
                  @endif
                </div>
                <button wire:click="openTransportCostModal({{ $sale->id }})"
                        class="ml-2 text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded"
                        title="Transport kosten instellen">
                  🚛
                </button>
              </div>
            </td>
            <td>&euro; {{ number_format((float)($saleQuantities[$sale->id] ?? $sale->quantity) * (float)$sale->price + (float)$sale->transport_cost, 2, ',', '.') }}</td>
            <td>{{ $sale->customer_purchase_number ?: '-' }}</td>
            <td><i class="fa-solid fa-trash" wire:click="removeSale({{$sale->id}})" style="color: #dd2222;"></i></td>
          </tr>
        @endforeach
        </tbody>
      </table>
      <hr>
    </div>
  </div>
  <div class="flex font-bold">
    <div class="w-3/4"></div>
    <div class="w-1/4">
      <div class="flex justify-between">
        <div>
        Totaal inkoop
        </div>
        <div>
          &euro; {{ $purchase->total }}
        </div>
      </div>
      <div class="flex justify-between">
        <div>Totaal transport</div>
        <div>&euro; {{ $totalTransport }}</div>
      </div>
      <div class="flex justify-between">
        <div>Totaal verkoop</div>
        <div>&euro; {{ $totalPrice + $totalTransport }}</div>
      </div>

    </div>
  </div>
  </div>

  @if($purchase->signature)
    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 mb-5">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-lg font-semibold text-gray-900">
          <i class="fas fa-signature text-blue-600 mr-2"></i>
          Digitale Handtekening
        </h4>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <i class="fas fa-check-circle mr-1"></i>
          Ontvangen
        </span>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Signature Image -->
        <div class="lg:col-span-2">
          <div class="border-2 border-gray-200 rounded-lg p-4 bg-gray-50">
            <img src="{{ $purchase->signature }}"
                 alt="Digitale Handtekening"
                 class="max-w-full h-auto border border-gray-300 rounded bg-white shadow-sm"
                 style="max-height: 200px;">
          </div>
        </div>

        <!-- Signature Details -->
        <div class="space-y-4">
          <div>
            <h5 class="text-sm font-medium text-gray-700 mb-2">Details</h5>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-500">Ontvangen:</span>
                <span class="font-medium">
                  {{ $purchase->signature_received_at ? $purchase->signature_received_at->format('d-m-Y') : 'Onbekend' }}
                </span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">Tijd:</span>
                <span class="font-medium">
                  {{ $purchase->signature_received_at ? $purchase->signature_received_at->format('H:i') : 'Onbekend' }}
                </span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">Status:</span>
                <span class="text-green-600 font-medium">Geldig</span>
              </div>
            </div>

            <!-- JavaScript to enhance file upload experience -->
            <script>
              document.addEventListener('DOMContentLoaded', function() {
                const fileInput = document.getElementById('file-upload-input');

                if (fileInput) {
                  // Reset file input after each upload to allow selecting the same file again
                  fileInput.addEventListener('change', function() {
                    if (this.files.length > 0) {
                      // Small delay to allow Livewire to process the file
                      setTimeout(() => {
                        this.value = '';
                      }, 1000);
                    }
                  });
                }
              });
            </script>
          </div>

          <div class="pt-4 border-t border-gray-200">
            <button onclick="downloadSignature()" class="w-full px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition duration-150 ease-in-out">
              <i class="fas fa-download mr-2"></i>
              Download Handtekening
            </button>
          </div>
        </div>
      </div>
    </div>

    <script>
      function downloadSignature() {
        const link = document.createElement('a');
        link.href = '{{ $purchase->signature }}';
        link.download = 'handtekening-{{ $purchase->purchase_number }}.png';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    </script>
  @endif
    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 mb-5">
      <h4>Correspondentie</h4>
      <hr>
      @foreach ($shipperMails as $shipperMail)
        <div>
          <strong>{{ Carbon::create($shipperMail->created_at)->format('d-m-Y H:i') }}</strong><br>
          <strong>Aan: </strong> {{ $shipperMail->mail_to }} <br><br>
          <strong>Bericht</strong><br/>
          {{ $shipperMail->mail_message }}<br><br>
          <strong>Bijlages</strong><br><br>
          <div class="flex flex-wrap gap-4">
            @if(!empty($shipperMail->attachments))
              @foreach($shipperMail->attachments as $attachment)
                <div class="flex flex-column items-center">
                  @php
                    $extension = pathinfo($attachment['name'], PATHINFO_EXTENSION);
                    $iconClass = 'fa-file';
                    if (in_array($extension, ['pdf'])) $iconClass = 'fa-file-pdf';
                    elseif (in_array($extension, ['doc', 'docx'])) $iconClass = 'fa-file-word';
                    elseif (in_array($extension, ['xls', 'xlsx'])) $iconClass = 'fa-file-excel';
                    elseif (in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) $iconClass = 'fa-file-image';
                  @endphp
                  <a href="#" wire:click.prevent="downloadAttachment('{{ $attachment['path'] }}', '{{ $attachment['name'] }}')">
                    <i class="fa-solid {{ $iconClass }} fa-2xl" style="height: 18px;"></i>
                    <small><strong>{{ $attachment['name'] }}</strong></small>
                  </a>
                </div>
              @endforeach
            @else
              <div class="text-gray-500">Geen bijlagen</div>
            @endif
          </div>
        </div>
        <hr>
      @endforeach
    </div>

  <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 mb-5 flex">
    <div class="w-1/2">
      <h4>Bewerkingen</h4>
      <ul class="list-disc">
        {!! $log !!}
      </ul>
    </div>
    <div class="w-1/2">
      <livewire:purchase-comments :purchase="$purchase" />
    </div>
  </div>



  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Verkoop toevoegen
    </x-slot>

    <x-slot name="content">
      <div class="mb-4">
        <x-label for="customer" value="Klant" />
        <div class="@if(!empty($selectedCustomer)) hidden @endif">
          <input
                  type="text"
                  wire:model.live.debounce.500ms="search"
                  placeholder="Zoek klant..."
          />
          @unless($customers->isEmpty())
            <div class="bg-gray-100" style="max-height: 400px; overflow-x: scroll; border: 1px solid #4b5563">
              @foreach($customers as $customer)
                <p
                        wire:click="selectCustomer({{ $customer->id }})"
                        class="cursor-pointer hover:bg-gray-50 py-2 px-2"
                        style="margin-bottom: 0px !important;"
                >
                  {{ $customer->exact_id }} - {{ $customer->company_name }} ({{$customer->city}})
                </p>
              @endforeach
            </div>
          @endunless
        </div>
        @if(!empty($selectedCustomer))
          <h5>{{ $selectedCustomer->exact_id }} - {{ $selectedCustomer->company_name }} ({{$selectedCustomer->city}})</h5>
          <h5>Losadres</h5>
          <div class="mb-4 w-2/3">
            <x-label for="straat" value="Straat + huisnummer" />
            <div class="flex gap-4">
              <x-input type="text" id="straat" wire:model.live="street" class="w-2/3" />
              <x-input type="text" id="housenumber" wire:model.live="housenumber" class="w-1/3"/>
            </div>
          </div>
          <div class="mb-4 w-2/3">
            <x-label for="postal" value="Postcode + plaats" />
            <div class="flex gap-4">
              <x-input type="text" id="postal" wire:model.live="postalCode" class="w-1/3" />
              <x-input type="text" id="city" wire:model.live="city" class="w-2/3"/>
            </div>
          </div>
        @endif
      </div>
      <hr>
      <div class="mb-4">
        <x-label for="aantal" value="Aantal" />
        <div class="flex align-items-baseline gap-4">
          <x-input id="aantal" class="block mt-1 w-1/3" type="text" wire:model.live="saleQty" /> <div><strong>{{ $purchase->qty_type }}</strong></div>
        </div>
      </div>
      <div class="mb-4">
        <x-label for="price" value="Prijs per {{$purchase->qty_type}}" />
        <x-input id="price" class="block mt-1 w-1/4" type="text" wire:model.live="salePrice" />
      </div>
      <div class="mb-4">
        <x-label for="transport" value="Transport prijs" />
        <x-input id="transport" class="block mt-1 w-1/4" type="text" wire:model.live="transportPrice" />
      </div>
      <div class="mb-4">
        <x-label for="customerPurchaseNumber" value="Klant inkoopnummer" />
        <x-input id="customerPurchaseNumber" class="block mt-1 w-1/2" type="text" wire:model.live="customerPurchaseNumber" placeholder="Optioneel" />
      </div>
      @if($errorMessage)
        <div class="bg-red-100 p-4">
          {{ $errorMessage }}
        </div>
      @endif
    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="$set('showModal', false)" wire:loading.attr="disabled">
        Annuleren
      </x-secondary-button>

      <x-button class="ml-2" wire:click.prevent="addSale" wire:loading.attr="disabled">
        Opslaan
      </x-button>
    </x-slot>
  </x-dialog-modal>

    <x-dialog-modal wire:model.live="showShipperModal">
      <x-slot name="title">
        Transporteur selecteren
      </x-slot>

      <x-slot name="content">
        <div class="mb-4">
          <x-label for="transporter" value="Transporteur" />
          <div class="@if(!empty($selectedShipper)) hidden @endif">
            <input
                    type="text"
                    wire:model.live.debounce.500ms="shipperSearch"
                    placeholder="Zoek transporteur..."
            />
            @unless($shippers->isEmpty())
              <div class="bg-gray-100" style="max-height: 400px; overflow-x: scroll; border: 1px solid #4b5563">
                @foreach($shippers as $shipper)
                  <p
                          wire:click="selectShipper({{ $shipper->id }})"
                          class="cursor-pointer hover:bg-gray-50 py-2 px-2"
                          style="margin-bottom: 0px !important;"
                  >
                    {{ $shipper->company_name }} ({{$shipper->city}})
                  </p>
                @endforeach
              </div>
            @endunless
          </div>
          @if(!empty($selectedShipper))
            <h5>{{ $selectedShipper->company_name }} ({{$selectedShipper->city}})</h5>
          @endif
        </div>
        <hr>
      </x-slot>

      <x-slot name="footer">
        <x-secondary-button wire:click="$set('showShipperModal', false)" wire:loading.attr="disabled">
          Annuleren
        </x-secondary-button>

        <x-button class="ml-2" wire:click.prevent="createAndSelectShipper" wire:loading.attr="disabled">
          Selecteer Transporteur
        </x-button>
      </x-slot>
    </x-dialog-modal>

    <x-dialog-modal wire:model.live="showShipperMailModal">
      <x-slot name="title">
        Transporteur mailen
      </x-slot>

      <x-slot name="content">
        <div class="mb-4">
          <x-label for="mailto" value="Email adres" />
          <x-input id="transport" class="block mt-1 w-full" type="text" wire:model.live="shipperMail" />
          @error('shipperMail') <span class="text-red-500 text-sm mt-1">{{ $message }}</span> @enderror
        </div>
        <div class="mb-4">
          <x-label for="message" value="Bericht" />
          <textarea wire:model="messageTextShipper" class="w-full border-gray-300 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 rounded-md shadow-sm" rows="5"></textarea>
          @error('messageTextShipper') <span class="text-red-500 text-sm mt-1">{{ $message }}</span> @enderror
        </div>
        <div>
          <label class="toggle">
            <input class="toggle-checkbox" type="checkbox" wire:model="includeCmr">
            <div class="toggle-switch"></div>
            <span class="toggle-label">Maak CMR en voeg bij als bijlage</span>
          </label>
        </div>
        <br>
        <div class="mb-4">
          <x-label value="Bijlagen" />

          @if(app()->environment('production'))
            <!-- Multiple single file uploads for production (S3/Vapor) -->
            <div class="space-y-4">
              <!-- File Upload Input -->
              <div class="flex items-center gap-2">
                <input type="file" wire:model="singleFileUpload" class="mt-1" id="file-upload-input" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif,.txt" />
                @error('singleFileUpload') <span class="text-red-500">{{ $message }}</span> @enderror
              </div>

              <!-- Debug Info (remove in production) -->
              @if(config('app.debug'))
                <div class="text-xs text-gray-500 mt-1">
                  Debug: singleFileUpload = {{ json_encode($singleFileUpload) }}
                </div>
              @endif
              <div class="mt-1 text-sm text-gray-600">
                <p>Selecteer één bestand per keer. Bestanden worden direct geüpload. U kunt meerdere bestanden uploaden door dit proces te herhalen.</p>
              </div>

              <!-- Manual Upload Button (alternative approach) -->
              @if($singleFileUpload)
                <div class="mt-2">
                  <button type="button"
                          wire:click="processManualUpload"
                          class="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600">
                    Upload Bestand
                  </button>
                </div>
              @endif

              <!-- Upload Progress -->
              <div wire:loading wire:target="singleFileUpload" class="flex items-center gap-2 text-blue-600">
                <svg class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Bestand uploaden...</span>
              </div>

              <!-- Uploaded Files List -->
              @if($uploadedFiles && count($uploadedFiles) > 0)
                <div class="border border-gray-200 rounded-lg p-4">
                  <h4 class="font-medium text-gray-900 mb-3">Geüploade bestanden ({{ count($uploadedFiles) }})</h4>
                  <div class="space-y-2">
                    @foreach($uploadedFiles as $index => $file)
                      <div class="flex items-center justify-between p-3 bg-gray-50 rounded border">
                        <div class="flex items-center space-x-3">
                          <!-- File Icon -->
                          <div class="w-8 h-8 flex items-center justify-center rounded text-white text-xs font-bold
                            @if(str_contains($file['name'], '.pdf')) bg-red-500
                            @elseif(str_contains($file['name'], '.jpg') || str_contains($file['name'], '.jpeg') || str_contains($file['name'], '.png') || str_contains($file['name'], '.gif')) bg-green-500
                            @elseif(str_contains($file['name'], '.doc') || str_contains($file['name'], '.docx')) bg-blue-500
                            @else bg-gray-500
                            @endif">
                            @if(str_contains($file['name'], '.pdf'))
                              PDF
                            @elseif(str_contains($file['name'], '.jpg') || str_contains($file['name'], '.jpeg') || str_contains($file['name'], '.png') || str_contains($file['name'], '.gif'))
                              IMG
                            @elseif(str_contains($file['name'], '.doc') || str_contains($file['name'], '.docx'))
                              DOC
                            @else
                              FILE
                            @endif
                          </div>

                          <!-- File Info -->
                          <div>
                            <p class="font-medium text-sm">{{ $file['name'] }}</p>
                            <p class="text-xs text-gray-500">{{ round($file['size'] / 1024) }} KB</p>
                          </div>
                        </div>

                        <!-- Actions -->
                        <div class="flex items-center space-x-2">
                          <!-- Download/View Button -->
                          <button type="button"
                                  wire:click="downloadAttachment('{{ $file['path'] }}', '{{ $file['name'] }}')"
                                  class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            Bekijken
                          </button>

                          <!-- Delete Button -->
                          <button type="button"
                                  wire:click="removeUploadedFile({{ $index }})"
                                  wire:confirm="Weet u zeker dat u dit bestand wilt verwijderen?"
                                  class="text-red-600 hover:text-red-800 text-sm font-medium">
                            Verwijderen
                          </button>
                        </div>
                      </div>
                    @endforeach
                  </div>
                </div>
              @endif

              <!-- Upload Another File Button -->
              @if($uploadedFiles && count($uploadedFiles) > 0)
                <div class="text-center">
                  <button type="button"
                          onclick="document.getElementById('file-upload-input').click()"
                          class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Nog een bestand uploaden
                  </button>
                </div>
              @endif
            </div>
          @else
            <!-- Multiple file upload for local development -->
            <input type="file" wire:model="tempAttachments" class="mt-1" multiple />
            @error('tempAttachments.*') <span class="text-red-500">{{ $message }}</span> @enderror
          @endif

          <!-- Show files that are being uploaded (local only) -->
          @if(!app()->environment('production') && !empty($tempAttachments))
            <div class="mt-2">
              <p class="font-medium">Geselecteerde bestanden:</p>
              <ul class="list-disc pl-5">
                @foreach($tempAttachments as $attachment)
                  <li>{{ $attachment->getClientOriginalName() }} ({{ round($attachment->getSize() / 1024) }} KB)</li>
                @endforeach
              </ul>
            </div>
          @endif

          <!-- Show already uploaded files -->
          @if(!empty($uploadedFiles))
            <div class="mt-2">
              <p class="font-medium">Geüploade bestanden:</p>
              <ul class="list-disc pl-5">
                @foreach($uploadedFiles as $index => $file)
                  <li class="flex items-center justify-between">
                    <span>{{ $file['name'] }} ({{ round($file['size'] / 1024) }} KB)</span>
                    <button type="button" wire:click="removeUploadedFile({{ $index }})" class="text-red-500 hover:text-red-700">
                      <i class="fa-solid fa-times"></i>
                    </button>
                  </li>
                @endforeach
              </ul>
            </div>
          @endif

          <div class="mt-2 text-sm text-gray-600">
            <p>Klik op het kruisje om een bestand te verwijderen.</p>
          </div>
        </div>
      </x-slot>

      <x-slot name="footer">
        <x-secondary-button wire:click="$set('showShipperMailModal', false)" wire:loading.attr="disabled">
          Annuleren
        </x-secondary-button>

        <x-button class="ml-2" wire:click.prevent="sendShipperMail" wire:loading.attr="disabled">
          Verstuur
        </x-button>
      </x-slot>
    </x-dialog-modal>

  <x-dialog-modal wire:model.live="showCompleteModal">
    <x-slot name="title">
      Afronden
    </x-slot>

    <x-slot name="content">
      <div class="mb-4">
        <x-label for="transport" value="Werkelijke transportkosten" />
        <x-input id="transport" class="block mt-1 w-full" type="text" wire:model.live="totalTransport" />
      </div>

      <div class="mb-4">
        <h3 class="font-bold mb-2">Werkelijke hoeveelheden</h3>
        <p class="text-sm text-gray-600 mb-3">Pas de werkelijke hoeveelheden aan indien deze afwijken van de oorspronkelijke hoeveelheden.</p>

        <div class="overflow-x-auto">
          <table class="min-w-full bg-white">
            <thead>
              <tr class="bg-gray-100 text-gray-700 text-sm leading-normal">
                <th class="py-2 px-4 text-left">Klant</th>
                <th class="py-2 px-4 text-left">Oorspronkelijke hoeveelheid</th>
                <th class="py-2 px-4 text-left">Werkelijke hoeveelheid</th>
              </tr>
            </thead>
            <tbody>
              @foreach($purchase->sales as $sale)
                <tr class="border-b border-gray-200 hover:bg-gray-50">
                  <td class="py-2 px-4">{{ $sale->customer->company_name }}</td>
                  <td class="py-2 px-4">{{ $sale->quantity }} {{ $sale->sale_qty_type }}</td>
                  <td class="py-2 px-4">
                    <x-input
                      type="number"
                      step="0.01"
                      class="w-24"
                      wire:model.live="actualQuantities.{{ $sale->id }}"
                    />
                    <span class="ml-2">{{ $sale->sale_qty_type }}</span>
                  </td>
                </tr>
              @endforeach
            </tbody>
          </table>
        </div>
      </div>
    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="$set('showCompleteModal', false)" wire:loading.attr="disabled">
        Annuleren
      </x-secondary-button>

      <x-button class="ml-2" wire:click.prevent="complete" wire:loading.attr="disabled">
        Afronden
      </x-button>
    </x-slot>
  </x-dialog-modal>

  <!-- Supplier Address Modal -->
  <x-dialog-modal wire:model.live="showSupplierAddressModal">
    <x-slot name="title">
      {{ __('Leverancier adres aanpassen') }}
    </x-slot>

    <x-slot name="content">
      <div class="mt-4">
        <x-label for="supplierStreet" value="{{ __('Straat') }}" />
        <x-input id="supplierStreet" class="block mt-1 w-full" type="text" wire:model="supplierStreet" />
      </div>

      <div class="mt-4">
        <x-label for="supplierHousenumber" value="{{ __('Huisnummer') }}" />
        <x-input id="supplierHousenumber" class="block mt-1 w-full" type="text" wire:model="supplierHousenumber" />
      </div>

      <div class="mt-4">
        <x-label for="supplierPostalCode" value="{{ __('Postcode') }}" />
        <x-input id="supplierPostalCode" class="block mt-1 w-full" type="text" wire:model="supplierPostalCode" />
      </div>

      <div class="mt-4">
        <x-label for="supplierCity" value="{{ __('Plaats') }}" />
        <x-input id="supplierCity" class="block mt-1 w-full" type="text" wire:model="supplierCity" />
      </div>

      <div class="mt-4">
        <x-label for="supplierCountry" value="{{ __('Land') }}" />
        <x-input id="supplierCountry" class="block mt-1 w-full" type="text" wire:model="supplierCountry" />
      </div>
    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="$set('showSupplierAddressModal', false)" wire:loading.attr="disabled">
        {{ __('Annuleren') }}
      </x-secondary-button>

      <x-button class="ml-3" wire:click="saveSupplierAddress" wire:loading.attr="disabled">
        {{ __('Opslaan') }}
      </x-button>
    </x-slot>
  </x-dialog-modal>

  <!-- Transport Cost Modal -->
  <x-dialog-modal wire:model.live="showTransportCostModal">
    <x-slot name="title">
      {{ __('Transport Kosten Instellen') }}
    </x-slot>

    <x-slot name="content">
      @if($selectedSaleForTransport)
        <div class="mb-6">
          <div class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
            <h4 class="font-medium text-blue-900">Transport kosten voor:</h4>
            <p class="text-blue-800">{{ $selectedSaleForTransport->customer->company_name }}</p>
            <p class="text-sm text-blue-700">
              {{ $selectedSaleForTransport->quantity }} {{ $selectedSaleForTransport->sale_qty_type }}
              @ €{{ number_format($selectedSaleForTransport->price, 2, ',', '.') }}
            </p>
          </div>

          <!-- Transport Cost Type Selection -->
          <div class="mb-6">
            <x-label value="Berekeningswijze" class="mb-2" />
            <div class="space-y-2">
              <label class="flex items-center">
                <input type="radio" wire:model.live="transportCostType" value="per_unit" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                <span class="ml-2 text-sm">Per {{ $selectedSaleForTransport->sale_qty_type }}</span>
              </label>
              <label class="flex items-center">
                <input type="radio" wire:model.live="transportCostType" value="total" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                <span class="ml-2 text-sm">Totaal bedrag</span>
              </label>
            </div>
          </div>

          <!-- Per Unit Cost Input -->
          @if($transportCostType === 'per_unit')
            <div class="mb-4">
              <x-label for="transportCostPerUnit" value="Kosten per {{ $selectedSaleForTransport->sale_qty_type }}" />
              <div class="mt-1 relative rounded-md shadow-sm">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span class="text-gray-500 sm:text-sm">€</span>
                </div>
                <x-input id="transportCostPerUnit"
                         class="pl-7"
                         type="number"
                         step="0.01"
                         min="0"
                         wire:model.live="transportCostPerUnit"
                         placeholder="0.00" />
              </div>
              @if($transportCostPerUnit)
                <p class="mt-1 text-sm text-gray-600">
                  Totaal transport kosten: €{{ number_format($this->calculateSaleTransportCost(), 2, ',', '.') }}
                  ({{ $selectedSaleForTransport->quantity }} {{ $selectedSaleForTransport->sale_qty_type }} × €{{ number_format($transportCostPerUnit, 2, ',', '.') }})
                </p>
              @endif
            </div>
          @endif

          <!-- Total Cost Input -->
          @if($transportCostType === 'total')
            <div class="mb-4">
              <x-label for="transportCostTotal" value="Totaal transport kosten" />
              <div class="mt-1 relative rounded-md shadow-sm">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span class="text-gray-500 sm:text-sm">€</span>
                </div>
                <x-input id="transportCostTotal"
                         class="pl-7"
                         type="number"
                         step="0.01"
                         min="0"
                         wire:model.live="transportCostTotal"
                         placeholder="0.00" />
              </div>
              @if($transportCostTotal)
                <p class="mt-1 text-sm text-gray-600">
                  Kosten per {{ $selectedSaleForTransport->sale_qty_type }}: €{{ number_format($transportCostTotal / $selectedSaleForTransport->quantity, 2, ',', '.') }}
                </p>
              @endif
            </div>
          @endif

          <!-- Current Calculation Display -->
          @php $calculatedCost = $this->calculateSaleTransportCost(); @endphp
          @if($calculatedCost > 0)
            <div class="bg-green-50 border border-green-200 rounded-md p-3">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-green-800">Berekende Transport Kosten</h3>
                  <div class="mt-1 text-sm text-green-700">
                    <p>Totaal: €{{ number_format($calculatedCost, 2, ',', '.') }}</p>
                    <p class="text-xs">Voor {{ $selectedSaleForTransport->quantity }} {{ $selectedSaleForTransport->sale_qty_type }}</p>
                  </div>
                </div>
              </div>
            </div>
          @endif
        </div>
      @endif
    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="closeTransportCostModal" wire:loading.attr="disabled">
        {{ __('Annuleren') }}
      </x-secondary-button>

      @if($selectedSaleForTransport && $selectedSaleForTransport->transport_cost > 0)
        <x-danger-button class="ml-3" wire:click="removeTransportCost" wire:loading.attr="disabled">
          {{ __('Verwijderen') }}
        </x-danger-button>
      @endif

      <x-button class="ml-3" wire:click="saveTransportCost" wire:loading.attr="disabled">
        {{ __('Opslaan') }}
      </x-button>
    </x-slot>
  </x-dialog-modal>

</div>
