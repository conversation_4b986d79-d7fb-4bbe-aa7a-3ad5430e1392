<div>
    <h3>(<PERSON><PERSON><PERSON><PERSON>) Plan- en verkooplijsten</h3>
    <x-button wire:click="$set('showModal', true)"><PERSON><PERSON><PERSON> (g<PERSON><PERSON><PERSON>) lijst</x-button>
    <div class="mt-4">
        <h5><PERSON>jn g<PERSON>elde plan- en verkooplijsten</h5>
        <div class="flex flex-row gap-4">
            @foreach($myPlanLists as $planList)
                <a class="w-25" href="{{ route('planlist.shared', ['planlist' => $planList->id]) }}" style="text-decoration: none; color: #000">
                <div class="bg-gray-200 shadow-sm rounded p-4 flex flex-column">
                    <div><strong>{{ $planList->name }}</strong></div>
                    <div class="text-sm">
                        Gedeeld met:
                        @foreach($planList->users as $sharedUser)
                            {{ $sharedUser->name }}
                        @endforeach
                    </div>
                </div>
                </a>
            @endforeach
        </div>
    </div>
    <div class="mt-4">
        <h5>Plan- en verkooplijsten gedeeld met mij</h5>
        <div class="flex flex-row gap-4">
            @foreach($sharedWithMePlanLists as $sharedPlanList)
                <a class="w-25" href="{{ route('planlist.shared', ['planlist' => $sharedPlanList->id]) }}" style="text-decoration: none; color: #000">
                    <div class="bg-gray-200 shadow-sm rounded p-4 flex flex-column">
                        <div><strong>{{ $sharedPlanList->name }}</strong></div>
                        <div class="text-sm">
                            Gedeeld met:
                            @foreach($sharedPlanList->users as $sharedPlanListUser)
                                {{ $sharedPlanListUser->name }}
                            @endforeach
                        </div>
                    </div>
                </a>
            @endforeach
        </div>
    </div>

    <x-dialog-modal wire:model.live="showModal">
        <x-slot name="title">
            Nieuwe gedeelde planlijst
        </x-slot>
        <x-slot name="content">
            <div class="mb-4">
                <x-label for="name" value="Naam" />
                <x-input id="name" class="block mt-1 w-full" type="text" wire:model.live="name" />
            </div>
        </x-slot>
        <x-slot name="footer">
            <x-button wire:click="savePlanList">Opslaan</x-button>
        </x-slot>
    </x-dialog-modal>
</div>
