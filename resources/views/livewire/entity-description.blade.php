<div>
  <div x-data="{ saved: false }" @description-saved.window="saved = true; setTimeout(() => saved = false, 2000)">
    <h5>Vast notitie</h5>
    @if($edit == false)
      <div class="flex bg-gray-100 p-2 rounded-3 mb-1">
        {{ $description }}
      </div>
      <x-button wire:click="$set('edit', true)">Aanpassen</x-button>
    @else
      <textarea rows="10" wire:model.live="description" class="peer h-full min-h-[100px] w-full resize-none rounded-[7px] border border-blue-gray-200 border-t-transparent bg-transparent px-3 py-2.5 font-sans text-sm font-normal text-blue-gray-700 outline outline-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 focus:border-2 focus:border-pink-500 focus:border-t-transparent focus:outline-0 disabled:resize-none disabled:border-0 disabled:bg-blue-gray-50"></textarea>
      <x-button wire:click="saveDescription">Notitie opslaan</x-button>
      <span x-show="saved" x-transition.opacity.out.duration.1000ms style="display: none; color: #2ccf20;" class="ml-2"><i class="fa-solid fa-check" style="color: #2ccf20;" class="mr-2"></i>&nbsp;&nbsp;Opgeslagen</span>
    @endif
  </div>
</div>
