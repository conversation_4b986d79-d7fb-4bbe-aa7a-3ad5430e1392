<div>
  <div class="flex justify-between">
    <h4>Notities</h4>
    <x-button wire:click="$set('showModal', true)"><i class="fa-solid fa-plus" style="color: #ffffff; margin-right: 4px;"></i> Notitie toevoegen</x-button>
  </div>
  @foreach($noteable->notes()->latest()->get() as $note)
    <div class="rounded bg-gray-200 p-4 mb-4 w-49 relative">
      <p>{{ $note->content }}</p>
      <small><i>{{ $note->user->name }} - {{ $note->created_at->format('d-m-Y') }}</i></small>
      <i wire:click="removeNote({{$note}})" class="fa-solid fa-trash-can absolute cursor-pointer" style="right: 10px; top: 10px;"></i>
    </div>
  @endforeach

  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Notitie toevoegen
    </x-slot>

    <x-slot name="content">
      <div class="mb-4">
        <x-label for="note" value="Notitie" />
        <textarea rows="4" wire:model.live="content" class="peer h-full min-h-[100px] w-full resize-none rounded-[7px] border border-blue-gray-200 border-t-transparent bg-transparent px-3 py-2.5 font-sans text-sm font-normal text-blue-gray-700 outline outline-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 focus:border-2 focus:border-pink-500 focus:border-t-transparent focus:outline-0 disabled:resize-none disabled:border-0 disabled:bg-blue-gray-50 mb-4"></textarea>
        @error('content') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>
      <div class="w-full mt-4">
        <x-label for="interactionNote" value="{{ __('Stuur notificatie') }}" />
        <x-checkbox name="notify" wire:model.live="notify"></x-checkbox>
        <span style="padding-left: 10px; padding-right: 10px;"> naar </span>
        <select wire:model.live="ownerId">
          <option value="" {{ empty($ownerId) ? 'selected' : '' }}>Kies gebruiker</option>
          @foreach($users as $user)
            <option value="{{ $user->id }}" {{ $ownerId == $user->id ? 'selected' : '' }}>{{ $user->name }}</option>
          @endforeach
        </select>
      </div>
    </x-slot>

    <x-slot name="footer">
      <x-button wire:click="save">Opslaan</x-button>
    </x-slot>
  </x-dialog-modal>
</div>
