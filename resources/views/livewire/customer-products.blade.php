<div>
    <div class="mb-4">
        <h3 class="text-lg font-medium text-gray-900">Producten</h3>
        <p class="mt-1 text-sm text-gray-600">Beheer de producten die deze klant afneemt.</p>
    </div>

    @if (session()->has('message'))
        <div class="mb-4 p-4 bg-green-100 text-green-700 rounded">
            {{ session('message') }}
        </div>
    @endif

    <!-- Add Product Form -->
    <div class="mb-6 p-4 bg-gray-50 rounded-lg">
        <h4 class="text-md font-medium mb-2">Product toevoegen</h4>

        <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
            <div class="relative">
                <label for="productSearch" class="block text-sm font-medium text-gray-700">Product</label>
                <input type="text" id="productSearch" wire:model.live.debounce.300ms="productSearch" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" placeholder="Zoek product...">

                @if($products->count() > 0)
                    <div class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-300 max-h-60 overflow-y-auto">
                        @foreach($products as $product)
                            <div wire:click="selectProduct({{ $product->id }})" class="p-2 hover:bg-gray-100 cursor-pointer">
                                <div class="font-medium">{{ $product->name }}</div>
                                <div class="text-xs text-gray-500">{{ $product->sku }}</div>
                            </div>
                        @endforeach
                    </div>
                @endif

                @if($selectedProduct)
                    <div class="mt-2 p-2 bg-blue-50 rounded text-sm">
                        Geselecteerd: <strong>{{ $selectedProduct->name }}</strong>
                    </div>
                @endif
            </div>

            <div>
                <label for="unitPrice" class="block text-sm font-medium text-gray-700">Prijs per eenheid</label>
                <div class="mt-1 relative rounded-md shadow-sm">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span class="text-gray-500 sm:text-sm">€</span>
                    </div>
                    <input type="number" step="0.01" id="unitPrice" wire:model="unitPrice" class="pl-7 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" placeholder="0.00">
                </div>
                @error('unitPrice') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
            </div>

            <div>
                <label for="unitType" class="block text-sm font-medium text-gray-700">Eenheid type</label>
                <select id="unitType" wire:model="unitType" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                    <option value="TON">TON</option>
                    <option value="M3">M3</option>
                    <option value="VOLLE VRACHT">VOLLE VRACHT</option>
                    <option value="HALVE VRACHT">HALVE VRACHT</option>
                    <option value="ZAK">ZAK</option>
                    <option value="STUK">STUK</option>
                </select>
                @error('unitType') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
            </div>

            <div>
                <label for="endDate" class="block text-sm font-medium text-gray-700">Einddatum (optioneel)</label>
                <input type="date" id="endDate" wire:model="endDate" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                @error('endDate') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
            </div>

            <div>
                <label for="defaultQty" class="block text-sm font-medium text-gray-700">Standaard hoeveelheid</label>
                <input type="number" min="1" id="defaultQty" wire:model="defaultQty" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" placeholder="Bijv. 25">
                @error('defaultQty') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
            </div>

            <div class="flex items-end">
                <button wire:click="addProduct" class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:border-indigo-900 focus:ring focus:ring-indigo-300 disabled:opacity-25 transition" @if(!$selectedProduct) disabled @endif>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Toevoegen
                </button>
            </div>
        </div>
    </div>

    <!-- Products Table -->
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Categorie</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Prijs per eenheid</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Eenheid type</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Einddatum</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Standaard qty</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Acties</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @forelse($customerProducts as $product)
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="font-medium text-gray-900">{{ $product['name'] }}</div>
                            <div class="text-sm text-gray-500">{{ $product['sku'] }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {{ $product['category']['name'] ?? 'Geen categorie' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            @if(isset($product['pivot']) && isset($product['pivot']['unit_price']))
                                €{{ number_format($product['pivot']['unit_price'], 2, ',', '.') }}
                            @else
                                -
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            @if(isset($product['pivot']) && isset($product['pivot']['unit_type']))
                                {{ $product['pivot']['unit_type'] }}
                            @else
                                TON
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            @if(isset($product['pivot']) && isset($product['pivot']['end_date']))
                                <span class="text-sm {{ \Carbon\Carbon::parse($product['pivot']['end_date'])->isPast() ? 'text-red-600' : 'text-gray-900' }}">
                                    {{ \Carbon\Carbon::parse($product['pivot']['end_date'])->format('d-m-Y') }}
                                </span>
                            @else
                                <span class="text-gray-500 text-sm">Onbeperkt</span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            @if(isset($product['pivot']) && isset($product['pivot']['default_qty']))
                                {{ $product['pivot']['default_qty'] }}
                            @else
                                <span class="text-gray-500">-</span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button wire:click="removeProduct({{ $product['id'] }})" class="text-red-600 hover:text-red-900">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                            </button>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="7" class="px-6 py-4 whitespace-nowrap text-center text-gray-500">
                            Geen producten gevonden voor deze klant.
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>
