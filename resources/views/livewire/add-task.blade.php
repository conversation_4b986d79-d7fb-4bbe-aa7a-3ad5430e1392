<div>
  <div class="flex justify-between">
    <h4>Taken</h4>
    <x-button wire:click="$set('showModal', true)"><i class="fa-solid fa-plus" style="color: #ffffff; margin-right: 4px;"></i> Taak toevoegen</x-button>
  </div>
  @foreach($taskable->tasks()->orderBy('due_date', 'asc')->get() as $task)
    <div class="rounded bg-gray-200 p-4 mb-4 w-49" wire:click="showModal({{ $task }})">
      <p>{{ $task->description }}</p>
      <small><i>Toegewezen aan: {{ $task->assignedTo->name }} <br> Datum: {{ $task->due_date }}</i></small>
    </div>
  @endforeach

  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Taak toevoegen
    </x-slot>
    <x-slot name="content">
      <div class="mb-4">
        <select id="type" class="form-input rounded-md shadow-sm mt-1 block w-full" wire:model.live="type">
          <option value="">Selecteer Type</option>
          @foreach($types as $taskType)
            <option value="{{ $taskType }}">{{ ucfirst($taskType) }}</option>
          @endforeach
        </select>
        @error('type') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>

      <div class="mb-4">
        <x-label for="assignedTo" value="Assigned to" />
        <select id="assignedTo" class="form-input rounded-md shadow-sm mt-1 block w-full" wire:model.live="assignedTo">
          @foreach($users as $user)
            <option value="{{ $user->id }}">{{ $user->name }}</option>
          @endforeach
        </select>
        @error('assignedTo') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>

      <div class="mb-4">
        <x-label for="due-date" value="Datum" />
        <x-input id="due-date" class="block mt-1 w-full" type="date" wire:model.live="dueDate" autofocus />
        @error('dueDate') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>
      <div class="mb-4">
        <x-label for="description" value="Omschrijving" />
        <textarea rows="4" wire:model.live="description" class="peer h-full min-h-[100px] w-full resize-none rounded-[7px] border border-blue-gray-200 border-t-transparent bg-transparent px-3 py-2.5 font-sans text-sm font-normal text-blue-gray-700 outline outline-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 focus:border-2 focus:border-pink-500 focus:border-t-transparent focus:outline-0 disabled:resize-none disabled:border-0 disabled:bg-blue-gray-50 mb-4"></textarea>
        @error('description') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>
    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="$set('showModal', false)" wire:loading.attr="disabled">
        Annuleren
      </x-secondary-button>

      <x-button class="ml-2" wire:click.prevent="saveTask" wire:loading.attr="disabled">
        Opslaan
      </x-button>
    </x-slot>
  </x-dialog-modal>

  <x-dialog-modal wire:model.live="isModalOpen">
    <x-slot name="title">
      Taak Details
    </x-slot>

    <x-slot name="content">
      @if($modalTask)
        <p>{{ ucfirst($modalTask->type) }}</p>
        <p>{{ $modalTask->description }}</p>
        <div class="mb-4">
          <x-label for="due-date" value="Datum" />
          <x-input id="due-date" class="block mt-1" style="width: 9rem;" type="date" wire:model.live="dueDateEdit" />
        </div>
        <div class="w-full mt-8">
          <x-label for="interactionNote" value="{{ __('Toegewezen aan') }}" />
          <select wire:model.live="ownerId">
            <option value="" {{ empty($ownerId) ? 'selected' : '' }}>Kies gebruiker</option>
            @foreach($users as $user)
              <option value="{{ $user->id }}" {{ $ownerId == $user->id ? 'selected' : '' }}>{{ $user->name }}</option>
            @endforeach
          </select>
        </div>
        <div>
          <x-label for="note" value="Notitie" />
          <textarea rows="4" wire:model.live="note" class="peer h-full min-h-[100px] w-full resize-none rounded-[7px] border border-blue-gray-200 border-t-transparent bg-transparent px-3 py-2.5 font-sans text-sm font-normal text-blue-gray-700 outline outline-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 focus:border-2 focus:border-pink-500 focus:border-t-transparent focus:outline-0 disabled:resize-none disabled:border-0 disabled:bg-blue-gray-50 mb-2"></textarea>
        </div>
      @endif

      <x-button wire:click="addTaskNote">Opslaan</x-button>
    </x-slot>

    <x-slot name="footer">
      <div class="w-full flex justify-between">
        <x-danger-button wire:click="removeTask">Verwijder</x-danger-button>
        <div>
          <x-secondary-button wire:click="$set('isModalOpen', false)" wire:loading.attr="disabled">
            Sluiten
          </x-secondary-button>
          <x-button wire:click="completeTask" class="ml-2">Taak afronden</x-button>
        </div>
      </div>
    </x-slot>
  </x-dialog-modal>
</div>
