<div>
  <div class="w-full mt-2 flex justify-between flex-wrap">
    @foreach($contactable->contactPersons as $contactPerson)
      <div class="rounded bg-gray-200 p-3 mb-4 relative w-full" wire:click="$set('showModal', true)">
        <i class="fa-solid fa-user"></i> {{ $contactPerson->name }}<br>
        <i class="fa-solid fa-at"></i> {{ $contactPerson->email }}<br>
        <i class="fa-solid fa-phone"></i> {{ $contactPerson->phone_number }}<br>
        <i class="fa-solid fa-mobile-screen"></i> {{ $contactPerson->mobile_phone_number }}<br>
        <i class="fa-solid fa-note-sticky"></i> {{ $contactPerson->note }}
{{--        <i class="fa-regular fa-pen-to-square absolute cursor-pointer" style="top: 10px; right: 10px;" wire:click="edit({{ $contactPerson->id }})"></i>--}}
      </div>
    @endforeach
  </div>
  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Contactpersoon
    </x-slot>
    <x-slot name="content"></x-slot>
    <x-slot name="footer"></x-slot>
  </x-dialog-modal>
</div>
