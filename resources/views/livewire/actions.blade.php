<div>
  <div class="flex justify-between">
    <h4>Acties</h4>
    <x-button wire:click="$set('showModal', true)"><i class="fa-solid fa-plus" style="color: #ffffff; margin-right: 4px;"></i> Actie toevoegen</x-button>
  </div>
  @foreach($actionable->actions()->withTrashed()->orderBy('created_at', 'desc')->get() as $action)
    <div class="rounded bg-gray-200 p-4 mb-4 w-49" wire:click="showEditModal({{ $action }})">
      <p>{{ $action->description }}</p>
      <small><i>Toegewezen aan: {{ $action->assignedTo->name }} <br> Datum: {{ $action->due_date }}</i></small><br>
      <small><i>Aangemaakt: {{ $action->created_at }}</i></small>
      @if($action->deleted_at)
        <br> <small><i>Afgerond op: {{ $action->deleted_at }}</i></small>
      @endif
    </div>
  @endforeach

  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Actie toevoegen
    </x-slot>
    <x-slot name="content">
      <div class="mb-4">
        <x-label for="assignedTo" value="Type" />
        <select id="type" class="form-input rounded-md shadow-sm mt-1 block w-full" wire:model.live="type">
          @foreach($types as $taskType)
            <option value="{{ $taskType }}">{{ ucfirst($taskType) }}</option>
          @endforeach
        </select>
        @error('type') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>

      <div class="mb-4">
        <x-label for="assignedTo" value="Toegewezen aan" />
        <select id="assignedTo" class="form-input rounded-md shadow-sm mt-1 block w-full" wire:model.live="assignedTo">
          @foreach($users as $user)
            <option value="{{ $user->id }}">{{ $user->name }}</option>
          @endforeach
        </select>
        @error('assignedTo') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>

      <div class="mb-4">
        <x-label for="due-date" value="Datum" />
        <x-input id="due-date" class="block mt-1 w-full" type="date" wire:model.live="dueDate" autofocus />
        @error('dueDate') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>
      <div class="mb-4">
        <x-label for="description" value="Omschrijving" />
        <textarea rows="4" wire:model.live="description" class="peer h-full min-h-[100px] w-full resize-none rounded-[7px] border border-blue-gray-200 border-t-transparent bg-transparent px-3 py-2.5 font-sans text-sm font-normal text-blue-gray-700 outline outline-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 focus:border-2 focus:border-pink-500 focus:border-t-transparent focus:outline-0 disabled:resize-none disabled:border-0 disabled:bg-blue-gray-50"></textarea>
        @error('description') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
        <small>*Gebruik @@-spatie om de datum in de tekst te krijgen.</small>
      </div>
    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="cancelModal" wire:loading.attr="disabled">
        Annuleren
      </x-secondary-button>

      <x-button class="ml-2" wire:click.prevent="saveOrUpdateAction" wire:loading.attr="disabled">
        Opslaan
      </x-button>
    </x-slot>
  </x-dialog-modal>
</div>
