<div>
  <div class="flex align-items-baseline mb-4">
    <div class="mr-3">Toon statistieken voor: </div>
    <div>
      <select wire:model.live="userId">
        <option value="" {{ empty($userId) ? 'selected' : '' }}>Ki<PERSON> gebruiker</option>
        @foreach($users as $user)
          <option value="{{ $user->id }}" {{ $userId == $user->id ? 'selected' : '' }}>{{ $user->name }}</option>
        @endforeach
      </select>
    </div>
    <div class="ml-3 mr-3">van </div>
    <div class="mr-3">
      <x-input id="startDate" class="block mt-1" style="width: 9rem;" type="date" wire:model.live="startDate" />
    </div>
    <div class="mr-3">tot </div>
    <div class="mr-3">
      <x-input id="endDate" class="block mt-1" style="width: 9rem;" type="date" wire:model.live="endDate" />
    </div>
    <x-button wire:click="showStats">Toon</x-button>
  </div>
  <div class="flex gap-4">
    <div class=" bg-gray-200 shadow-sm rounded p-4 w-25">
      <div class="flex flex-row">
        <div class="flex items-center justify-center flex-shrink-0 h-12 w-12 rounded-xl bg-blue-100 text-blue-500 ">
          <i class="fa-solid fa-check-double fa-xl"  style="color: #5edd27;"></i>
        </div>
        <div class="flex flex-col flex-grow ml-4">
          <div class="text-sm text-gray-500">Afgeronde taken</div>
          <div class="font-bold text-lg">{{ $completedTasks }}</div>
        </div>
      </div>
      <div class="text-sm text-gray-500" style="font-style: italic">Tussen {{ $showStartDate }} en {{ $showEndDate }}</div>
    </div>
    <div class="bg-gray-200 shadow-sm rounded p-4 w-25">
      <div class="flex flex-row">
        <div class="flex items-center justify-center flex-shrink-0 h-12 w-12 rounded-xl bg-blue-100 text-blue-500">
          <i class="fa-solid fa-clock-rotate-left fa-xl" style="color: #dd7f27;"></i>
        </div>
        <div class="flex flex-col flex-grow ml-4">
          <div class="text-sm text-gray-500">Te laat taken</div>
          <div class="font-bold text-lg">{{ $lateTasks }}</div>
        </div>
      </div>
      <div class="text-sm text-gray-500" style="font-style: italic">Vanaf begin deze week</div>
    </div>
    <div class="bg-gray-200 shadow-sm rounded p-4 w-25">
      <div class="flex flex-row">
        <div class="flex items-center justify-center flex-shrink-0 h-12 w-12 rounded-xl bg-blue-100 text-blue-500">
          <i class="fa-solid fa-phone fa-xl"></i>
        </div>
        <div class="flex flex-col flex-grow ml-4">
          <div class="text-sm text-gray-500">Communicaties</div>
          <div class="font-bold text-lg">{{ $communications }}</div>
        </div>
      </div>
      <div class="text-sm text-gray-500" style="font-style: italic">Tussen {{ $showStartDate }} en {{ $showEndDate }}</div>
    </div>
    <div class="bg-gray-200 shadow-sm rounded p-4 w-25">
      <div class="flex flex-row">
        <div class="flex items-center justify-center flex-shrink-0 h-12 w-12 rounded-xl bg-blue-100 text-blue-500">
          <i class="fa-solid fa-users fa-xl"></i>
        </div>
        <div class="flex flex-col flex-grow ml-4">
          <div class="text-sm text-gray-500">Klanten</div>
          <div class="font-bold text-lg">{{ $customers }}</div>
        </div>
      </div>
      <div class="text-sm text-gray-500" style="font-style: italic">Totaal eigenaar van</div>
    </div>
  </div>
</div>
