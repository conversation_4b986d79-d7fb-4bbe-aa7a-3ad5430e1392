<div>
    <h5>Acties tot eind van de week</h5>
    <div class="flex-column">
        @foreach ($actions as $action)
            <div class="rounded bg-gray-200 p-2 mb-2 hover:bg-gray-400">
                <div class="flex justify-between mb-1">
                    <div>{!! nl2br(e($action->description)) !!}</div>
                </div>
                <hr>
                Datum: {{ $action->due_date }} <br>
                @php
                    $routeName = '';
                    switch($action->actionable_type) {
                        case \App\Models\Customer::class:
                            $routeName = 'customer.edit';
                            $routeArg = 'customer';
                            break;
                        case \App\Models\Supplier::class:
                            $routeName = 'supplier.edit';
                            $routeArg = 'supplier';
                            break;
                        case \App\Models\Prospect::class:
                            $routeName = 'prospect.edit';
                            $routeArg = 'prospect';
                            break;
                        default:
                            break;
                    }
                @endphp

                @if($routeName)
                    <a href="{{ route($routeName, [$routeArg => $action->actionable->id]) }}">
                        {{ $action->actionable->company_name }}
                    </a><br>
                @endif
            </div>
        @endforeach
    </div>
</div>
