<div>
  <div class="@if(!empty($selectedSupplier)) hidden @endif">
    <input
            type="text"
            wire:model.live.debounce.500ms="supplierSearch"
            placeholder="<PERSON><PERSON> leverancier..."
    />
    @unless($suppliers->isEmpty())
      <div class="bg-gray-100" style="max-height: 400px; overflow-x: scroll; border: 1px solid #4b5563">
        @foreach($suppliers as $supplier)
          <p
                  wire:click="selectSupplier({{ $supplier->id }})"
                  class="cursor-pointer hover:bg-gray-50 py-2 px-2"
                  style="margin-bottom: 0px !important;"
          >
            {{ $supplier->company_name }} ({{$supplier->city}})
          </p>
        @endforeach
      </div>
    @endunless
  </div>
  @if(!empty($selectedSupplier))
    <h5>{{ $selectedSupplier->company_name }} ({{$selectedSupplier->city}})</h5>
  @endif
  <div>
    @if($priceAgreements->isNotEmpty())
      <table class="table" style="width: 50%;">
        <thead>
        <tr>
          <th>Van</th>
          <th>Tot</th>
          <th>Prijs</th>
          <th></th>
        </tr>
        </thead>
        <tbody>
        @foreach($priceAgreements as $priceAgreement)
          <tr>
            <td>{{ $priceAgreement->min_qty }}</td>
            <td>{{ $priceAgreement->max_qty }}</td>
            <td>{{ $priceAgreement->price }}</td>
            <td>
              <i class="fa-regular fa-pen-to-square cursor-pointer mr-3"></i>
              <i class="fa-solid fa-trash-can cursor-pointer"></i>
            </td>
          </tr>
        @endforeach
        </tbody>
      </table>
    @endif
    @if(!empty($selectedSupplier))
      <x-secondary-button wire:click="$set('showModal', true)">Nieuwe staffel</x-secondary-button>
      @endif
  </div>
  <x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
      Staffel toevoegen
    </x-slot>
    <x-slot name="content">
      <div class="mb-4">
        <x-label for="from" value="Van" />
        <x-input id="from" class="block mt-1 w-1/4" type="text" wire:model="tierFrom" />
      </div>
      <div class="mb-4">
        <x-label for="to" value="Tot" />
        <x-input id="to" class="block mt-1 w-1/4" type="text" wire:model="tierTo" />
      </div>
      <div class="mb-4">
        <x-label for="price" value="Prijs" />
        <x-input id="price" class="block mt-1 w-1/4" type="text" wire:model="tierPrice" />
      </div>
      @if (session()->has('error'))
        <p class="text-red-500">{{ session('error') }}</p>
      @endif
    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="$set('showModal', false)" wire:loading.attr="disabled">
        Annuleren
      </x-secondary-button>

      <x-button class="ml-2" wire:click.prevent="saveTier" wire:loading.attr="disabled">
        Opslaan
      </x-button>
    </x-slot>
  </x-dialog-modal>
</div>
