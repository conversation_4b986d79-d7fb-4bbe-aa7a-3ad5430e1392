@php use Illuminate\Support\Facades\Auth; @endphp
<div>
  <div class="flex justify-between">
    <x-button wire:click="$set('showNewTaskModal', true)"><i class="fa-solid fa-plus"
                                                             style="color: #ffffff; margin-right: 4px;"></i> <PERSON><PERSON>
      toevoegen
    </x-button>
    <div>Tot Datum: <input type="date" wire:model.live="showTo"></div>
  </div>
  <div class="w-full mt-4">
    @foreach ($tasks as $task)
      <div class="rounded w-full bg-gray-200 p-2 mb-4 @if($task->assignedToUser->id == Auth::id()) border-2 border-info @endif">
        <div class="flex">
          <div class="w-25">
            <div>Datum: {{ $task->due_date }}</div>
            <div>{{ $task->plannable->company_name }}</div>
            <div><i class="fa-solid fa-user"></i> {{ $task->assignedToUser->name }}</div>
          </div>
          <div style="border-left: 1px solid #ccc;" class="pl-3 ml-3 w-75">
            {{ $task->description }}
          </div>
        </div>
        <div class="mt-2 pt-2 flex gap-2" style="border-top: 1px solid #ccc;">
          <x-secondary-button wire:click="openContactInfo({{$task->id}})">Toon contactinfo</x-secondary-button>
          <x-secondary-button wire:click="openRegisterContact({{$task->id}})">Contact registreren</x-secondary-button>
          <x-secondary-button wire:click="showHistory({{$task->id}})">Toon historie</x-secondary-button>
          <x-secondary-button wire:click="openPlanTask({{$task->id}})">Aanpassen</x-secondary-button>
          <x-danger-button wire:click="openDeleteTask({{$task->id}})">Verwijder</x-danger-button>
        </div>
      </div>
    @endforeach
  </div>
  <div class="flex justify-between">
    <x-secondary-button wire:click="$set('changeNameModal', true)">Naam wijzigen</x-secondary-button>
    <x-danger-button wire:click="removePlanlist">Verwijder lijst</x-danger-button>
  </div>

  <x-dialog-modal wire:model.live="contactInfoModal">
    <x-slot name="title">
      Contact Informatie
    </x-slot>

    <x-slot name="content">
      <div class="mb-2 flex justify-between">
        @if($relation)
          <div>
            <strong>{{ $relation->company_name }}</strong><br>
            {{ $relation->street_name }} {{ $relation->housenumber }}<br>
            {{ $relation->postal_code }} {{ $relation->city }}<br><br>
            <strong>Opmerking</strong><br>
            {{ $relation->description }}
          </div>
          <div>
            <x-button wire:click="editEntity()">Bewerken</x-button>
          </div>
        @endif
      </div>
      <div class="flex gap-2 flex-wrap">
        @if ($contactPersons)
          @foreach ($contactPersons as $contactPerson)
            <div class="rounded bg-gray-200 p-4 w-49 relative">
              <i class="fa-solid fa-user"></i> {{ $contactPerson->name }}<br>
              <i class="fa-solid fa-at"></i> {{ $contactPerson->email }}<br>
              <i class="fa-solid fa-phone"></i> {{ $contactPerson->phone_number }}<br>
              <i class="fa-solid fa-mobile-screen"></i> {{ $contactPerson->mobile_phone_number }}<br>
              <i class="fa-solid fa-note-sticky"></i> {{ $contactPerson->note }}
              <i class="fa-regular fa-pen-to-square absolute cursor-pointer" style="top: 10px; right: 10px;"
                 wire:click="editContactPerson({{ $contactPerson->id }})"></i>
            </div>
          @endforeach
        @endif
      </div>
    </x-slot>
    <x-slot name="footer">
      <x-secondary-button wire:click="$set('contactInfoModal', false)" wire:loading.attr="disabled">
        Sluiten
      </x-secondary-button>
    </x-slot>
  </x-dialog-modal>

  <x-dialog-modal wire:model.live="showEditEntityModal">
    <x-slot name="title">
      Relatie bewerken
    </x-slot>

    <x-slot name="content">
      @if($relation)
        <div class="mb-4">
          <x-label for="company_name" value="Bedrijfsnaam"/>
          <x-input id="company_name" class="block mt-1 w-full" type="text" wire:model.live="companyName" autofocus/>
        </div>
        <div class="mb-4">
          <x-label for="name" value="Primair contactpersoon"/>
          <x-input id="name" class="block mt-1 w-full" type="text" wire:model.live="name"/>
        </div>
        <div class="mb-4">
          <x-label for="telephone" value="Telefoonnummer"/>
          <x-input id="telephone" class="block mt-1 w-full" type="text" wire:model.live="telephone"/>
        </div>
        <div class="mb-4">
          <x-label for="street" value="Straat + huisnummer"/>
          <div class="flex gap-2">
            <x-input id="street" class="block mt-1 w-2/3" type="text" wire:model.live="street"/>
            <x-input id="housenumber" class="block mt-1 w-1/3" type="text" wire:model.live="housenumber"/>
          </div>
        </div>
        <div class="mb-4">
          <x-label for="street" value="Postcode + plaats"/>
          <div class="flex gap-2">
            <x-input id="postcode" class="block mt-1 w-1/3" type="text" wire:model.live="postalCode"/>
            <x-input id="plaats" class="block mt-1 w-2/3" type="text" wire:model.live="city"/>
          </div>
        </div>
        <div class="mb-4">
          <x-label for="notitie" value="Notitie"/>
          <textarea rows="10" wire:model.live="entityDescription"
                    class="peer h-full min-h-[100px] w-full resize-none rounded-[7px] border border-blue-gray-200 border-t-transparent bg-transparent px-3 py-2.5 font-sans text-sm font-normal text-blue-gray-700 outline outline-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 focus:border-2 focus:border-pink-500 focus:border-t-transparent focus:outline-0 disabled:resize-none disabled:border-0 disabled:bg-blue-gray-50"></textarea>
        </div>
      @endif
    </x-slot>
    <x-slot name="footer">
      <x-secondary-button wire:click="$set('showEditEntityModal', false)" wire:loading.attr="disabled">
        Sluiten
      </x-secondary-button>
      <x-button wire:click="saveEntity" class="ml-2">Opslaan</x-button>
    </x-slot>
  </x-dialog-modal>

  <x-dialog-modal wire:model.live="showEditContactPerson">
    <x-slot name="title">
      Relatie bewerken
    </x-slot>

    <x-slot name="content">
      @if($selectedContactPerson)
        <div class="mb-4">
          <x-label for="contact_name" value="Naam"/>
          <x-input id="contact_name" class="block mt-1 w-full" type="text" wire:model.live="contactName" autofocus/>
        </div>
        <div class="mb-4">
          <x-label for="contact_email" value="Email"/>
          <x-input id="contact_email" class="block mt-1 w-full" type="text" wire:model.live="contactEmail"/>
        </div>
        <div class="mb-4">
          <x-label for="contact_telephone" value="Telefoon"/>
          <x-input id="contact_telephone" class="block mt-1 w-full" type="text" wire:model.live="contactTelephone"/>
        </div>
        <div class="mb-4">
          <x-label for="contact_mobile" value="Mobiel"/>
          <x-input id="contact_mobile" class="block mt-1 w-full" type="text" wire:model.live="contactMobile"/>
        </div>
        <div class="mb-4">
          <x-label for="notitie" value="Notitie"/>
          <textarea rows="5" wire:model.live="contactNote"
                    class="peer h-full min-h-[100px] w-full resize-none rounded-[7px] border border-blue-gray-200 border-t-transparent bg-transparent px-3 py-2.5 font-sans text-sm font-normal text-blue-gray-700 outline outline-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 focus:border-2 focus:border-pink-500 focus:border-t-transparent focus:outline-0 disabled:resize-none disabled:border-0 disabled:bg-blue-gray-50"></textarea>
        </div>
      @endif
    </x-slot>
    <x-slot name="footer">
      <x-secondary-button wire:click="$set('showEditContactPerson', false)" wire:loading.attr="disabled">
        Sluiten
      </x-secondary-button>
      <x-button wire:click="saveContact" class="ml-2">Opslaan</x-button>
    </x-slot>
  </x-dialog-modal>

  <x-dialog-modal wire:model.live="communicationModal">
    <x-slot name="title">
      Contact registreren
    </x-slot>
    <x-slot name="content">
      <div class="mb-4">
        <x-label for="description" value="Omschrijving"/>
        <textarea rows="4" wire:model.live="description"
                  class="peer h-full min-h-[100px] w-full resize-none rounded-[7px] border border-blue-gray-200 border-t-transparent bg-transparent px-3 py-2.5 font-sans text-sm font-normal text-blue-gray-700 outline outline-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 focus:border-2 focus:border-pink-500 focus:border-t-transparent focus:outline-0 disabled:resize-none disabled:border-0 disabled:bg-blue-gray-50"></textarea>
        @error('description') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>
      <div class="mb-4">
        <x-label for="due-date" value="Taak verplaatsen naar"/>
        <x-input id="due-date" class="block mt-1 w-full" type="date" wire:model.live="dueDate" autofocus/>
        @error('dueDate') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>
    </x-slot>
    <x-slot name="footer">
      <x-button wire:click="updatePlantask">Opslaan</x-button>
    </x-slot>
  </x-dialog-modal>

  <x-dialog-modal wire:model.live="updateTaskModal">
    <x-slot name="title">
      Taak aanpassen
    </x-slot>
    <x-slot name="content">
      <div class="mb-4">
        <x-label for="assignedTo" value="Toewijzen aan"/>
        <select id="assignedTo" class="form-input rounded-md shadow-sm mt-1 block w-full" wire:model.live="updateAssignedTo">
          @foreach($users as $user)
            <option value="{{ $user->id }}">{{ $user->name }}</option>
          @endforeach
        </select>
        @error('assignedTo') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>

      <div class="mb-4">
        <x-label for="due-date" value="Datum"/>
        <x-input id="due-date" class="block mt-1 w-full" type="date" wire:model.live="updateDueDate" autofocus/>
        @error('dueDate') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>
      <div class="mb-4">
        <x-label for="description" value="Omschrijving"/>
        <textarea rows="4" wire:model.live="updateDescription"
                  class="peer h-full min-h-[100px] w-full resize-none rounded-[7px] border border-blue-gray-200 border-t-transparent bg-transparent px-3 py-2.5 font-sans text-sm font-normal text-blue-gray-700 outline outline-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 focus:border-2 focus:border-pink-500 focus:border-t-transparent focus:outline-0 disabled:resize-none disabled:border-0 disabled:bg-blue-gray-50"></textarea>
        <small>* Type @@ en deze wordt vervangen door de huidige datum.</small>
        @error('description') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>
      <div class="mb-4">
        <x-label for="moveTo" value="Verplaatsen naar"/>
        <select id="moveTo" class="form-input rounded-md shadow-sm mt-1 block w-full" wire:model.live="moveTo">
          <option value="0">Nvt</option>
          <option value="personal">Persoonlijke planlijst</option>
          @foreach($myPlanLists as $planlist)
            <option value="{{ $planlist->id }}">{{ $planlist->name }}</option>
          @endforeach
        </select>
        @error('assignedTo') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>
    </x-slot>
    <x-slot name="footer">
      <x-button wire:click="saveUpdated">Opslaan</x-button>
    </x-slot>
  </x-dialog-modal>

  <x-dialog-modal wire:model.live="showHistoryModal">
    <x-slot name="title"></x-slot>
    <x-slot name="content">
      @if($lastCommunications)
        <div class="mb-4">
          <h5>Laatste 5 acties</h5>
          @foreach($lastCommunications as $communication)
            <div class="flex bg-gray-100 p-2 rounded-3 mb-1">
              <div class="w-25" style="border-right: 1px solid #ccc; padding-right: 5px; margin-right: 5px;">
                <small>{{$communication->createdBy->name}}<br/> {{ $communication->created_at}}</small></div>
              <div class="w-75">{{ $communication->description }}</div>
            </div>
          @endforeach
        </div>
      @endif

    </x-slot>
    <x-slot name="footer">
      <x-secondary-button wire:click="$set('showHistoryModal', false)">Sluiten</x-secondary-button>
    </x-slot>
  </x-dialog-modal>

  <x-dialog-modal wire:model.live="confirmDelete">
    <x-slot name="title">Verwijder taak</x-slot>
    <x-slot name="content">Weet je zeker dat je deze taak wil verwijderen? Dit kan niet ongedaan worden.</x-slot>
    <x-slot name="footer">
      <x-secondary-button wire:click="$set('confirmDelete', false)">Annuleer</x-secondary-button>
      <x-danger-button wire:click="deleteTask">Ja, verwijder</x-danger-button>
    </x-slot>
  </x-dialog-modal>

  <x-dialog-modal wire:model.live="showNewTaskModal">
    <x-slot name="title">
      Taak toevoegen
    </x-slot>
    <x-slot name="content">
      <div class="mb-4">
        <x-label for="assignedTo" value="Relatie"/>
        <input type="text" wire:model.live.debounce.300ms="search" placeholder="Relatie..." class="input-style">

        @if(!empty($results))
          <div class="absolute z-10 mt-2 w-2/3 bg-white border border-gray-300 rounded shadow-md">
            <ul>
              @foreach($results as $result)
                <li wire:click="selectEntity({{$result['id']}}, '{{$result['classname']}}')"
                    class="px-4 py-2 hover:bg-gray-200 cursor-pointer">{{ $result['exact_id'] }} - {{ $result['company_name'] }}</li>
              @endforeach
            </ul>
          </div>
        @endif
      </div>
      <div class="mb-4">
        <x-label for="assignedTo" value="Toewijzen aan"/>
        <select id="assignedTo" class="form-input rounded-md shadow-sm mt-1 block w-full" wire:model.live="assignedTo">
          <option value="{{ $planlist->creator->id }}">{{ $planlist->creator->name }}</option>
          @foreach($planlist->users as $user)
            <option value="{{ $user->id }}">{{ $user->name }}</option>
          @endforeach
        </select>
        @error('assignedTo') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>

      <div class="mb-4">
        <x-label for="due-date" value="Datum"/>
        <x-input id="due-date" class="block mt-1 w-full" type="date" wire:model.live="dueDate" autofocus/>
        @error('dueDate') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>
      <div class="mb-4">
        <x-label for="description" value="Omschrijving"/>
        <textarea rows="4" wire:model.live="description"
                  class="peer h-full min-h-[100px] w-full resize-none rounded-[7px] border border-blue-gray-200 border-t-transparent bg-transparent px-3 py-2.5 font-sans text-sm font-normal text-blue-gray-700 outline outline-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 focus:border-2 focus:border-pink-500 focus:border-t-transparent focus:outline-0 disabled:resize-none disabled:border-0 disabled:bg-blue-gray-50"></textarea>
        @error('description') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
      </div>
      @if($errorMessage)
        <div class="bg-red-100 p-4">
          {{ $errorMessage }}
        </div>
      @endif
    </x-slot>

    <x-slot name="footer">
      <x-secondary-button wire:click="$set('showNewTaskModal', false)" wire:loading.attr="disabled">
        Annuleren
      </x-secondary-button>

      <x-button class="ml-2" wire:click.prevent="saveTask" wire:loading.attr="disabled">
        Opslaan
      </x-button>
    </x-slot>
  </x-dialog-modal>

  <x-dialog-modal wire:model.live="changeNameModal">
    <x-slot name="title">Naam wijzigen</x-slot>
    <x-slot name="content">
      <div class="mb-4">
        <x-label for="name" value="Naam"/>
        <x-input id="name" class="block mt-1 w-full" type="text" wire:model.live="planListName"/>
      </div>
    </x-slot>
    <x-slot name="footer">
      <x-button wire:click="updateName">Opslaan</x-button>
    </x-slot>
  </x-dialog-modal>
</div>
