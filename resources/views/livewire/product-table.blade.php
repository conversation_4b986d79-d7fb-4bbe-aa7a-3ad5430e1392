<div>
  <table id="customers-table" class="table">
    <thead>
    <tr>
      <th>ID</th>
      <th>SKU</th>
      <th>Naam</th>
    </tr>
    </thead>
  </table>

  @push('scripts')
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <script src="https://cdn.datatables.net/1.10.23/js/jquery.dataTables.min.js"></script>
    <script>
      document.addEventListener('livewire:init', function () {
        $('#customers-table').DataTable({
          stateSave: true,
          processing: true,
          serverSide: true,
          ajax: '{{ route('livewire.product-table.get-products') }}',
          columns: [
            { data: 'id', name: 'id' },
            { data: 'sku', name: 'sku' },
            { data: 'name', name: 'name' },
          ],
          rowCallback: function(row, data) {
            $(row).on('click', function() {
              window.location.href = '/products/edit/' + data.id;
            });
          }
        });
      });
      document.addEventListener('refresh', function() {
        window.location.reload();
      });
    </script>
  @endpush
</div>
