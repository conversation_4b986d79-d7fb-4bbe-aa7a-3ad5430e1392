<x-app-layout>
    <x-slot name="header">
        <div class="justify-between flex">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Truck Locations') }}
            </h2>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <div class="mb-4">
                    <h3 class="text-lg font-semibold mb-2">Live Truck Locations</h3>
                    <p class="text-sm text-gray-600 mb-4">Locations update automatically every 5 minutes. Last updated: <span id="last-updated">Loading...</span></p>

                    <div class="flex space-x-4 mb-4">
                        <button id="refresh-btn" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition">
                            Refresh Now
                        </button>
{{--                        <select id="history-hours" class="rounded border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">--}}
{{--                            <option value="1">Last 1 hour</option>--}}
{{--                            <option value="3">Last 3 hours</option>--}}
{{--                            <option value="6">Last 6 hours</option>--}}
{{--                            <option value="12">Last 12 hours</option>--}}
{{--                            <option value="24">Last 24 hours</option>--}}
{{--                        </select>--}}
                        <select id="truck-selector" class="rounded border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            <option value="all">All Trucks</option>
                            <!-- Will be populated dynamically -->
                        </select>
                    </div>
                </div>

                <!-- Map container -->
                <div id="map" class="w-full h-[600px] rounded-lg border border-gray-300"></div>

                <!-- Truck info panel -->
                <div id="truck-info" class="mt-4 p-4 bg-gray-50 rounded-lg hidden">
                    <h4 class="font-semibold text-lg mb-2">Truck Information</h4>
                    <div id="truck-details">
                        <!-- Will be populated dynamically -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <x-slot name="styles">
        <!-- Leaflet CSS -->
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />

        <!-- FontAwesome for truck icons -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />

        <style>
            #map {
                height: 600px;
                width: 100%;
                z-index: 1;
            }
            .truck-icon i {
                font-size: 24px;
                color: #2563eb;
            }
        </style>
    </x-slot>

    @push('scripts')
    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

    <script>
        // Wait for the page to be fully loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing map...');

            // Initialize map with a try-catch to handle errors
            let map;
            try {
                // Default to Amsterdam coordinates
                map = L.map('map', {
                    center: [52.3676, 4.9041],
                    zoom: 7,
                    minZoom: 3,
                    maxZoom: 18
                });

                console.log('Map initialized');

                // Add OpenStreetMap tile layer
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                }).addTo(map);

                console.log('Tile layer added');
            } catch (error) {
                console.error('Error initializing map:', error);
                document.getElementById('map').innerHTML = '<div class="p-4 bg-red-100 text-red-700 rounded">Error loading map. Please check console for details.</div>';
                return;
            }

            // Store markers and paths
            const markers = {};
            const paths = {};
            let selectedTruck = 'all';
            let historyHours = 1;

            // Function to format date
            function formatDate(timestamp) {
                const date = new Date(parseInt(timestamp));
                return date.toLocaleString();
            }

            // Function to update last updated time
            function updateLastUpdated() {
                document.getElementById('last-updated').textContent = new Date().toLocaleString();
            }

            // Function to create or update a marker
            function updateMarker(location) {
                // Use number_plate as primary identifier, fallback to user_id or device_id
                const id = location.number_plate || location.user_id || location.device_id;
                const truckName = location.number_plate || (location.user ? location.user.name : `Device ${location.device_id}`);
                const displayName = location.number_plate ? location.number_plate : truckName;

                // Function to get state badge HTML
                function getStateBadge(state) {
                    if (!state) return '';

                    const stateColors = {
                        'driving': 'bg-green-100 text-green-800',
                        'resting': 'bg-blue-100 text-blue-800',
                        'loading': 'bg-yellow-100 text-yellow-800',
                        'unloading': 'bg-orange-100 text-orange-800',
                        'waiting': 'bg-gray-100 text-gray-800',
                        'maintenance': 'bg-red-100 text-red-800',
                        'break': 'bg-purple-100 text-purple-800',
                        'off_duty': 'bg-gray-100 text-gray-600',
                        'on_duty': 'bg-green-100 text-green-700'
                    };

                    const colorClass = stateColors[state] || 'bg-gray-100 text-gray-800';
                    const displayState = state.replace('_', ' ').toUpperCase();

                    return `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${colorClass}">${displayState}</span>`;
                }

                // Create popup content
                const popupContent = `
                    <div class="p-2">
                        <h3 class="font-bold">${displayName}</h3>
                        ${location.user && location.user.name ? `<p><strong>Driver:</strong> ${location.user.name}</p>` : ''}
                        ${location.device_id && !location.number_plate ? `<p><strong>Device:</strong> ${location.device_id}</p>` : ''}
                        ${location.current_state ? `<p><strong>Status:</strong> ${getStateBadge(location.current_state)}</p>` : ''}
                        <p>Last updated: ${formatDate(location.timestamp)}</p>
                        <p>Speed: ${location.speed ? (location.speed * 3.6).toFixed(1) + ' km/h' : 'N/A'}</p>
                        <p>Heading: ${location.heading ? location.heading.toFixed(1) + '°' : 'N/A'}</p>
                        <button class="px-2 py-1 bg-blue-500 text-white text-xs rounded mt-2" onclick="window.loadTruckHistory('${id}'); return false;">
                            Show History
                        </button>
                    </div>
                `;

                console.log('Created popup for truck ID:', id);

                // Function to get marker icon based on state
                function getMarkerIcon(state) {
                    const stateIcons = {
                        'driving': '🚛',
                        'resting': '😴',
                        'loading': '📦',
                        'unloading': '📤',
                        'waiting': '⏳',
                        'maintenance': '🔧',
                        'break': '☕',
                        'off_duty': '🏠',
                        'on_duty': '✅'
                    };

                    const stateColors = {
                        'driving': '#10b981',
                        'resting': '#3b82f6',
                        'loading': '#f59e0b',
                        'unloading': '#f97316',
                        'waiting': '#6b7280',
                        'maintenance': '#ef4444',
                        'break': '#8b5cf6',
                        'off_duty': '#6b7280',
                        'on_duty': '#10b981'
                    };

                    const emoji = stateIcons[state] || '🚛';
                    const color = stateColors[state] || '#6b7280';

                    return L.divIcon({
                        html: `<div style="background-color: ${color}; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3); font-size: 16px;">${emoji}</div>`,
                        className: 'custom-truck-icon',
                        iconSize: [30, 30],
                        iconAnchor: [15, 15]
                    });
                }

                // Create or update marker
                if (markers[id]) {
                    markers[id].setLatLng([location.latitude, location.longitude]);
                    markers[id].getPopup().setContent(popupContent);
                    markers[id].setIcon(getMarkerIcon(location.current_state));
                } else {
                    // Create marker
                    markers[id] = L.marker([location.latitude, location.longitude], {
                        icon: getMarkerIcon(location.current_state),
                        title: displayName
                    }).addTo(map);

                    // Add popup
                    markers[id].bindPopup(popupContent);

                    // Add to truck selector
                    const option = document.createElement('option');
                    option.value = id;
                    option.textContent = displayName;
                    document.getElementById('truck-selector').appendChild(option);
                }

                // Button click is handled via onclick attribute in popup content
            }

            // Function to load latest truck locations
            function loadLatestLocations() {
                fetch('{{ route("truck-locations.latest") }}')
                    .then(response => response.json())
                    .then(data => {
                        // Update markers
                        data.forEach(location => {
                            updateMarker(location);
                        });

                        // Update last updated time
                        updateLastUpdated();

                        // If no data, show message
                        if (data.length === 0) {
                            alert('No truck locations found.');
                        }
                    })
                    .catch(error => {
                        console.error('Error loading truck locations:', error);
                    });
            }

            // Function to load truck history (make it global so onclick can access it)
            window.loadTruckHistory = function(truckId) {
                console.log('Loading truck history for:', truckId);

                // Clear existing path
                if (paths[truckId]) {
                    map.removeLayer(paths[truckId]);
                }

                // Determine if we're using number_plate, user_id, or device_id
                const isUserId = truckId.toString().match(/^\d+$/);
                const isNumberPlate = truckId.toString().match(/^[A-Z0-9\-]+$/i) && !isUserId;
                const params = new URLSearchParams();
                params.append('hours', historyHours);

                if (isNumberPlate) {
                    params.append('number_plate', truckId);
                } else if (isUserId) {
                    params.append('user_id', truckId);
                } else {
                    params.append('device_id', truckId);
                }

                // Fetch history
                fetch(`{{ route("truck-locations.history") }}?${params.toString()}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.length === 0) {
                            alert('No history found for this truck in the selected time period.');
                            return;
                        }

                        // Create path coordinates
                        const coordinates = data.map(loc => [loc.latitude, loc.longitude]);

                        // Create path
                        paths[truckId] = L.polyline(coordinates, {
                            color: 'blue',
                            weight: 3,
                            opacity: 0.7
                        }).addTo(map);

                        // Fit map to path
                        map.fitBounds(paths[truckId].getBounds());

                        // Show truck info
                        const truckInfo = document.getElementById('truck-info');
                        const truckDetails = document.getElementById('truck-details');
                        const truckName = markers[truckId].options.title;

                        // Create details HTML
                        let detailsHtml = `
                            <p><strong>Truck:</strong> ${truckName}</p>
                            <p><strong>Time Period:</strong> Last ${historyHours} hour(s)</p>
                            <p><strong>Data Points:</strong> ${data.length}</p>
                            <p><strong>First Location:</strong> ${formatDate(data[0].timestamp)}</p>
                            <p><strong>Last Location:</strong> ${formatDate(data[data.length - 1].timestamp)}</p>
                        `;

                        // Add average speed if available
                        const speedData = data.filter(loc => loc.speed !== null);
                        if (speedData.length > 0) {
                            const avgSpeed = speedData.reduce((sum, loc) => sum + loc.speed, 0) / speedData.length;
                            detailsHtml += `<p><strong>Average Speed:</strong> ${(avgSpeed * 3.6).toFixed(1)} km/h</p>`;
                        }

                        truckDetails.innerHTML = detailsHtml;
                        truckInfo.classList.remove('hidden');
                    })
                    .catch(error => {
                        console.error('Error loading truck history:', error);
                    });
            };

            // Load initial data
            loadLatestLocations();

            // Set up auto-refresh every 5 minutes
            setInterval(loadLatestLocations, 5 * 60 * 1000);

            // Set up refresh button
            document.getElementById('refresh-btn').addEventListener('click', loadLatestLocations);

            // Set up history hours selector
            document.getElementById('history-hours').addEventListener('change', function() {
                historyHours = parseInt(this.value);
                if (selectedTruck !== 'all') {
                    window.loadTruckHistory(selectedTruck);
                }
            });

            // Set up truck selector
            document.getElementById('truck-selector').addEventListener('change', function() {
                selectedTruck = this.value;

                // Clear all paths
                Object.values(paths).forEach(path => {
                    map.removeLayer(path);
                });

                // Hide truck info
                document.getElementById('truck-info').classList.add('hidden');

                // If a specific truck is selected, load its history
                if (selectedTruck !== 'all') {
                    window.loadTruckHistory(selectedTruck);
                } else {
                    // Fit map to all markers
                    const bounds = [];
                    Object.values(markers).forEach(marker => {
                        bounds.push(marker.getLatLng());
                    });

                    if (bounds.length > 0) {
                        map.fitBounds(bounds);
                    }
                }
            });

            // Popup buttons use onclick handlers, no event delegation needed
        });
    </script>
    @endpush
</x-app-layout>
