<x-app-layout>
    <x-slot name="header">
        <div class="justify-between flex">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Simple Truck Map') }}
            </h2>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <div class="mb-4">
                    <h3 class="text-lg font-semibold mb-2">Truck Locations Map</h3>
                    <p class="text-sm text-gray-600 mb-4">This is a simplified map view for testing.</p>
                </div>

                <!-- Map container with inline styles for guaranteed height -->
                <div id="map" style="width: 100%; height: 500px; border: 1px solid #ccc;"></div>

                <div class="mt-4">
                    <button id="refresh-btn" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition">
                        Refresh Map
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Include Leaflet CSS and JS directly in the page -->
    <x-slot name="styles">
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
        <style>
            #map { height: 500px; }
        </style>
    </x-slot>

    @push('scripts')
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script type="text/javascript">
        // Simple map initialization
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Initializing simple map...');

            // Create a map centered on the Netherlands
            const map = L.map('map').setView([52.3676, 4.9041], 7);

            // Add the OpenStreetMap tiles
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                maxZoom: 19,
                attribution: '&copy; <a href="https://openstreetmap.org/copyright">OpenStreetMap contributors</a>'
            }).addTo(map);

            // Add a marker for testing
            const marker = L.marker([52.3676, 4.9041]).addTo(map);
            marker.bindPopup("<b>Test Truck</b><br>Amsterdam").openPopup();

            // Add a refresh button handler
            document.getElementById('refresh-btn').addEventListener('click', function() {
                // Simulate fetching data
                fetch('{{ route("truck-locations.latest") }}')
                    .then(response => response.json())
                    .then(data => {
                        console.log('Received data:', data);
                        if (data.length === 0) {
                            alert('No truck locations found.');
                            return;
                        }

                        // Clear existing markers
                        map.eachLayer(function(layer) {
                            if (layer instanceof L.Marker) {
                                map.removeLayer(layer);
                            }
                        });

                        // Add new markers
                        data.forEach(location => {
                            const truckName = location.user ? location.user.name : `Truck ${location.id}`;
                            const speed = location.speed ? `<br>Speed: ${(location.speed * 3.6).toFixed(1)} km/h` : '';
                            const marker = L.marker([location.latitude, location.longitude]).addTo(map);
                            marker.bindPopup(`<b>${truckName}</b><br>Last update: ${new Date(parseInt(location.timestamp)).toLocaleString()}${speed}`);
                        });
                    })
                    .catch(error => {
                        console.error('Error fetching truck locations:', error);
                        alert('Error fetching truck locations. See console for details.');
                    });
            });
        });
    </script>
    @endpush
</x-app-layout>
