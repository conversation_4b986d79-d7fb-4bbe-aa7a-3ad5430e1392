<x-app-layout>
    <x-slot name="header">
        <div class="justify-between flex">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Basic Truck Map') }}
            </h2>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <div class="mb-4">
                    <h3 class="text-lg font-semibold mb-2">Basic Map View</h3>
                    <p class="text-sm text-gray-600 mb-4">This is a basic map view without using @push directives.</p>
                </div>
                
                <!-- Map container with inline styles for guaranteed height -->
                <div id="map" style="width: 100%; height: 500px; border: 1px solid #ccc;"></div>
                
                <div class="mt-4">
                    <button id="refresh-btn" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition">
                        Refresh Map
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Include Leaflet CSS directly in the page -->
    <x-slot name="styles">
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
        <style>
            #map { height: 500px; }
        </style>
    </x-slot>

    <!-- Inline script at the end of the page -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Wait for the page to be fully loaded
        window.onload = function() {
            console.log('Window loaded, initializing map...');
            
            // Create a map centered on the Netherlands
            var map = L.map('map').setView([52.3676, 4.9041], 7);
            
            // Add the OpenStreetMap tiles
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                maxZoom: 19,
                attribution: '&copy; <a href="https://openstreetmap.org/copyright">OpenStreetMap contributors</a>'
            }).addTo(map);
            
            // Add a marker for testing
            var marker = L.marker([52.3676, 4.9041]).addTo(map);
            marker.bindPopup("<b>Test Truck</b><br>Amsterdam").openPopup();
            
            // Add a refresh button handler
            document.getElementById('refresh-btn').addEventListener('click', function() {
                alert('Map refreshed!');
                
                // Add a new random marker
                var lat = 52.3676 + (Math.random() - 0.5) * 2;
                var lng = 4.9041 + (Math.random() - 0.5) * 2;
                var newMarker = L.marker([lat, lng]).addTo(map);
                newMarker.bindPopup("<b>New Marker</b><br>Random Location").openPopup();
            });
        };
    </script>
</x-app-layout>
