<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Transport Informatie</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        .header {
            background-color: #f8f9fa;
            padding: 15px;
            margin-bottom: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        .content {
            padding: 15px;
        }
        .footer {
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #dee2e6;
            font-size: 12px;
            color: #6c757d;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table, th, td {
            border: 1px solid #dee2e6;
        }
        th, td {
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
    @if(app()->environment('local', 'testing', 'development', 'production'))
    <div style="background-color: #ffeb3b; padding: 10px; margin-bottom: 15px; border: 2px solid #f57f17; border-radius: 5px;">
        <h3 style="color: #f57f17; margin: 0;">⚠️ TEST MODE ⚠️</h3>
        <p style="margin: 5px 0 0 0;">This email is in test mode and has been redirected to you.</p>
        <p style="margin: 5px 0 0 0;">It was originally intended for another recipient.</p>
    </div>
    @endif

        <div class="header">
            <h2>Transport Informatie - Rit #{{ $purchase->purchase_number }}</h2>
        </div>

        <div class="content">
            @if(isset($messageText))
                {!! nl2br(e($messageText)) !!}
            @else
                <p>Geen bericht.</p>
            @endif

            <h3>Rit Details</h3>
            <table>
                <tr>
                    <th>Rit Nummer</th>
                    <td>{{ $purchase->purchase_number }}</td>
                </tr>
                <tr>
                    <th>Product</th>
                    <td>{{ $purchase->product->name }}</td>
                </tr>
                <tr>
                    <th>Hoeveelheid</th>
                    <td>{{ $purchase->quantity }} {{ $purchase->qty_type }}</td>
                </tr>
                <tr>
                    <th>Laaddatum</th>
                    <td>{{ \Carbon\Carbon::parse($purchase->load_date)->format('d-m-Y') }}</td>
                </tr>
                @if($purchase->delivery_date)
                <tr>
                    <th>Losdatum</th>
                    <td>{{ \Carbon\Carbon::parse($purchase->delivery_date)->format('d-m-Y') }}</td>
                </tr>
                @endif
            </table>

            <h3>Laadadres</h3>
            <p>
                {{ $purchase->supplier->company_name }}<br>
                @if($purchase->custom_supplier_address)
                    {{ $purchase->supplier_street }} {{ $purchase->supplier_housenumber }}<br>
                    {{ $purchase->supplier_postal_code }} {{ $purchase->supplier_city }}<br>
                    {{ $purchase->supplier_country }}
                @else
                    {{ $purchase->supplier->street_name }} {{ $purchase->supplier->housenumber }}<br>
                    {{ $purchase->supplier->postal_code }} {{ $purchase->supplier->city }}<br>
                    {{ $purchase->supplier->country }}
                @endif
                @if($purchase->supplier->loading_times)
                <br><br>
                <strong>Laadtijden:</strong><br>
                {!! nl2br(e($purchase->supplier->loading_times)) !!}
                @endif
            </p>

            @if(count($purchase->sales) > 0)
            <h3>Losadres(sen)</h3>
            @foreach($purchase->sales as $sale)
            <p>
                {{ $sale->customer->company_name }}<br>
                {{ $sale->address->street }} {{ $sale->address->housenumber }}<br>
                {{ $sale->address->postal_code }} {{ $sale->address->city }}<br>
                {{ $sale->address->country }}
                @if($sale->customer->unloading_times)
                <br><br>
                <strong>Lostijden:</strong><br>
                {!! nl2br(e($sale->customer->unloading_times)) !!}
                @endif
                @if($sale->address->unloading_times)
                <br><br>
                <strong>Lostijden (specifiek voor dit adres):</strong><br>
                {!! nl2br(e($sale->address->unloading_times)) !!}
                @endif
            </p>
            @if(!$loop->last)<hr>@endif
            @endforeach
            @endif
        </div>

        <div class="footer">
            <p>Dit is een automatisch gegenereerde e-mail. Reageer niet op dit bericht.</p>
        </div>
    </div>
</body>
</html>
