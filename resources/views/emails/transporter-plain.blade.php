@if(app()->environment('local', 'testing', 'development', 'production'))
⚠️ TEST MODE ⚠️
This email is in test mode and has been redirected to you.
It was originally intended for another recipient.

@endif
Transport Informatie - Rit #{{ $purchase->purchase_number }}

{{ $messageText }}

Rit Details:
Rit Nummer: {{ $purchase->purchase_number }}
Product: {{ $purchase->product->name }}
Hoeveelheid: {{ $purchase->quantity }} {{ $purchase->qty_type }}
Laaddatum: {{ \Carbon\Carbon::parse($purchase->load_date)->format('d-m-Y') }}
@if($purchase->delivery_date)
Losdatum: {{ \Carbon\Carbon::parse($purchase->delivery_date)->format('d-m-Y') }}
@endif

Laadadres:
{{ $purchase->supplier->company_name }}
@if($purchase->custom_supplier_address)
{{ $purchase->supplier_street }} {{ $purchase->supplier_housenumber }}
{{ $purchase->supplier_postal_code }} {{ $purchase->supplier_city }}
{{ $purchase->supplier_country }}
@else
{{ $purchase->supplier->street_name }} {{ $purchase->supplier->housenumber }}
{{ $purchase->supplier->postal_code }} {{ $purchase->supplier->city }}
{{ $purchase->supplier->country }}
@endif
@if($purchase->supplier->loading_times)

Laadtijden:
{{ $purchase->supplier->loading_times }}
@endif

@if(count($purchase->sales) > 0)
Losadres(sen):
@foreach($purchase->sales as $sale)
{{ $sale->customer->company_name }}
{{ $sale->address->street }} {{ $sale->address->housenumber }}
{{ $sale->address->postal_code }} {{ $sale->address->city }}
{{ $sale->address->country }}
@if($sale->customer->unloading_times)

Lostijden:
{{ $sale->customer->unloading_times }}
@endif
@if($sale->address->unloading_times)

Lostijden (specifiek voor dit adres):
{{ $sale->address->unloading_times }}
@endif

@endforeach
@endif
