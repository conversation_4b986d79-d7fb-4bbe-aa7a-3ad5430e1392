<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Sales Summary</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .summary-stats {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .stat-item {
            text-align: center;
            padding: 10px;
            background-color: white;
            border-radius: 5px;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #1976d2;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        .purchase-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        .purchase-header {
            background-color: #f5f5f5;
            padding: 15px;
            border-bottom: 1px solid #ddd;
        }
        .purchase-details {
            padding: 15px;
        }
        .sales-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .sales-table th,
        .sales-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .sales-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .no-data {
            text-align: center;
            padding: 40px;
            color: #666;
            font-style: italic;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Daily Sales Summary</h1>
        <p>{{ $summaryDate->format('l, d F Y') }}</p>
        <p>Hallo {{ $user->name }},</p>
    </div>

    @if($hasData)
        <div class="summary-stats">
            <h2>Samenvatting</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">{{ $totalSales }}</div>
                    <div class="stat-label">Totaal Verkopen</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">€{{ number_format($totalRevenue, 2, ',', '.') }}</div>
                    <div class="stat-label">Totale Omzet</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">€{{ number_format($totalTransportCosts, 2, ',', '.') }}</div>
                    <div class="stat-label">Transport Kosten</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">€{{ number_format($totalRevenue + $totalTransportCosts, 2, ',', '.') }}</div>
                    <div class="stat-label">Totaal Incl. Transport</div>
                </div>
            </div>
        </div>

        <h2>Voltooide Inkooporders</h2>
        
        @foreach($completedPurchases as $purchase)
            <div class="purchase-card">
                <div class="purchase-header">
                    <h3>Inkooporder #{{ $purchase->purchase_number }}</h3>
                    <p><strong>Leverancier:</strong> {{ $purchase->supplier->company_name ?? 'Onbekend' }}</p>
                </div>
                
                <div class="purchase-details">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 15px;">
                        <div>
                            <strong>Product:</strong><br>
                            {{ $purchase->product->name ?? 'Onbekend' }} ({{ $purchase->product->sku ?? 'N/A' }})
                        </div>
                        <div>
                            <strong>Hoeveelheid:</strong><br>
                            {{ $purchase->quantity }} {{ $purchase->qty_type }}
                        </div>
                        <div>
                            <strong>Prijs per eenheid:</strong><br>
                            €{{ number_format($purchase->price, 2, ',', '.') }}
                        </div>
                        <div>
                            <strong>Leverdatum:</strong><br>
                            {{ $purchase->delivery_date ? \Carbon\Carbon::parse($purchase->delivery_date)->format('d-m-Y') : 'Niet opgegeven' }}
                        </div>
                    </div>

                    @if($purchase->userSales->count() > 0)
                        <h4>Jouw Verkopen voor deze Inkoop:</h4>
                        <table class="sales-table">
                            <thead>
                                <tr>
                                    <th>Klant</th>
                                    <th>Hoeveelheid</th>
                                    <th>Prijs</th>
                                    <th>Transport</th>
                                    <th>Totaal</th>
                                    <th>Afleveradres</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($purchase->userSales as $sale)
                                    <tr>
                                        <td>{{ $sale->customer->company_name ?? 'Onbekende klant' }}</td>
                                        <td>{{ $sale->quantity }} {{ $sale->sale_qty_type }}</td>
                                        <td>€{{ number_format($sale->price, 2, ',', '.') }}</td>
                                        <td>€{{ number_format($sale->transport_cost, 2, ',', '.') }}</td>
                                        <td>€{{ number_format($sale->total_price + $sale->transport_cost, 2, ',', '.') }}</td>
                                        <td>
                                            @if($sale->shippingAddress)
                                                {{ $sale->shippingAddress->company_name }}<br>
                                                {{ $sale->shippingAddress->street }} {{ $sale->shippingAddress->housenumber }}<br>
                                                {{ $sale->shippingAddress->postal_code }} {{ $sale->shippingAddress->city }}
                                            @else
                                                Geen afleveradres
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    @endif
                </div>
            </div>
        @endforeach
    @else
        <div class="no-data">
            <h2>Geen verkopen voltooid gisteren</h2>
            <p>Er zijn geen inkooporders voltooid op {{ $summaryDate->format('d-m-Y') }} waarbij jij verkopen hebt aangemaakt.</p>
        </div>
    @endif

    <div class="footer">
        <p>Dit is een automatisch gegenereerde email van het verkoop systeem.</p>
        <p>Gegenereerd op {{ now()->format('d-m-Y H:i:s') }}</p>
    </div>
</body>
</html>
