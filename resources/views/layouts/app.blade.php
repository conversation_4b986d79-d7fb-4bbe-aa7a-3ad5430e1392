<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Laravel') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        <script src="https://kit.fontawesome.com/101263882b.js" crossorigin="anonymous"></script>
        @vite(['resources/js/app.js'])

        <!-- Styles -->
        @livewireStyles

        <!-- Custom page styles -->
        @if (isset($styles))
            {{ $styles }}
        @endif

        <style>
            body {
                background: url({{ public_path('images/cmr-rood.png') }});
                background-size: 1024px 722px;
                background-repeat: no-repeat;
            }
        </style>
        <x-rich-text::styles theme="richtextlaravel" data-turbo-track="false" />
    </head>
    <body class="font-sans antialiased">
        <x-banner />

        <div class="min-h-screen bg-gray-100">
            @livewire('navigation-menu')

            <!-- Page Heading -->


            <!-- Page Content -->
            <main class="w-full">
                {{ $slot }}
            </main>
        </div>

        @stack('modals')

        @livewireScripts
        @filepondScripts

        <!-- Page Scripts -->
        @stack('scripts')
    </body>
</html>
