@php use App\Models\Supplier; @endphp
<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ $supplier->company_name }}
            </h2>
        </div>
    </x-slot>

    <div class="py-3">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <div class="flex">
                    <div>
                        <form method="POST" action="{{ route('supplier.update', ['supplier' => $supplier->id]) }}"
                              class="w-full">
                            @csrf
                            <div class="flex items-center mb-6">
                                <div class="w-1/3">
                                    <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4"
                                             for="exact_id" value="{{ __('Exact ID') }}"/>
                                </div>
                                <div class="w-2/3">
                                    <x-input id="exact_id" class="block mt-1 w-full" type="text" name="exact_id"
                                             value="{{ $supplier->exact_id }}"/>
                                    @error('exact_id')
                                    <span class="text-red-500 text-sm">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="flex items-center mb-6">
                                <div class="w-1/3">
                                    <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4"
                                             for="company_name" value="{{ __('Bedrijfsnaam') }}"/>
                                </div>
                                <div class="w-2/3">
                                    <x-input id="company_name" class="block mt-1 w-full" type="text" name="company_name"
                                             value="{{ $supplier->company_name }}"/>
                                    @error('company_name')
                                    <span class="text-red-500 text-sm">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="flex items-center mb-6">
                                <div class="w-1/3">
                                    <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4"
                                             for="name" value="{{ __('Contactpersoon') }}"/>
                                </div>
                                <div class="w-2/3">
                                    <x-input id="name" class="block mt-1 w-full" type="text" name="name"
                                             value="{{ $supplier->name }}"/>
                                    @error('name')
                                    <span class="text-red-500 text-sm">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="flex items-center mb-6">
                                <div class="w-1/3">
                                    <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4"
                                             for="telephone" value="{{ __('Telefoon nummer') }}"/>
                                </div>
                                <div class="w-2/3">
                                    <x-input id="telephone" class="block mt-1 w-full" type="text" name="telephone"
                                             value="{{ $supplier->telephone }}"/>
                                    @error('telephone')
                                    <span class="text-red-500 text-sm">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="flex items-center mb-6">
                                <div class="w-1/3">
                                    <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4"
                                             for="street" value="{{ __('Straat + huisnummer') }}"/>
                                </div>
                                <div class="w-2/3 flex">
                                    <x-input id="street" class="block mt-1 w-full mr-2" type="text" name="street"
                                             value="{{ $supplier->street_name }}"/>
                                    @error('street_name')
                                    <span class="text-red-500 text-sm">{{ $message }}</span>
                                    @enderror
                                    <x-input id="housenumber" class="block mt-1 w-25" type="text" name="housenumber"
                                             value="{{ $supplier->housenumber }}"/>
                                    @error('housenumber')
                                    <span class="text-red-500 text-sm">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="flex items-center mb-6">
                                <div class="w-1/3">
                                    <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4"
                                             for="postal_code" value="{{ __('Postcode + Plaats') }}"/>
                                </div>
                                <div class="w-2/3 flex">
                                    <x-input id="postal_code" class="block mt-1 w-45 mr-2" type="text"
                                             name="postal_code" value="{{ $supplier->postal_code }}"/>
                                    @error('postal_code')
                                    <span class="text-red-500 text-sm">{{ $message }}</span>
                                    @enderror
                                    <x-input id="city" class="block mt-1 w-full" type="text" name="city"
                                             value="{{ $supplier->city }}"/>
                                    @error('city')
                                    <span class="text-red-500 text-sm">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="flex items-center mb-6">
                                <div class="w-1/3">
                                </div>
                                <div class="w-2/3">
                                    <x-button>
                                        {{ __('Opslaan') }}
                                    </x-button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <x-section-border/>
                <div class="flex flex-col items-center justify-center mt-3 w-full">
                    <div class="w-full">
                        <input type="checkbox" name="panel" id="panel-1" class="hidden">
                        <label for="panel-1" class="relative block bg-gray-400 text-white p-3 shadow border-b border-grey">Contactpersonen</label>
                        <div class="accordion__content overflow-hidden bg-grey-lighter">
                            @livewire('mobile-contact-persons', ['contactable' => $supplier])
                        </div>
                    </div>

                    <div class="w-full">
                        <input type="checkbox" name="panel" id="panel-2" class="hidden">
                        <label for="panel-2" class="relative block bg-gray-400 text-white p-3 shadow border-b border-grey">Acties</label>
                        <div class="accordion__content overflow-hidden bg-grey-lighter">
                            @livewire('mobile-actions', ['model' => 'supplier', 'modelId' => $supplier->id, 'actionable' => $supplier])
                        </div>
                    </div>

                    <div class="w-full">
                        <input type="checkbox" name="panel" id="panel-3" class="hidden">
                        <label for="panel-3" class="relative block bg-gray-400 text-white p-3 shadow border-b border-grey">Producten</label>
                        <div class="accordion__content overflow-hidden bg-grey-lighter">
                            <livewire:supplier-products :supplier="$supplier" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
