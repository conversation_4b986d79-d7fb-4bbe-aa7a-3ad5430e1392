@php use App\Models\Customer; @endphp
<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ $product->name }}
            </h2>

        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <div class="flex">

                        <form
                              class="w-full max-w-2xl" style="padding-right: 150px;">
                            @csrf
                            <div class="flex items-center mb-6">
                                <div class="w-1/3">
                                    <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4"
                                             for="exact_id" value="{{ __('SKU') }}"/>
                                </div>
                                <div class="w-2/3">
                                    <x-input id="exact_id" class="block mt-1 w-full" type="text" name="exact_id"
                                             value="{{ $product->sku }}"/>
                                    @error('exact_id')
                                    <span class="text-red-500 text-sm">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="flex items-center mb-6">
                                <div class="w-1/3">
                                    <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4"
                                             for="company_name" value="{{ __('Naam') }}"/>
                                </div>
                                <div class="w-2/3">
                                    <x-input id="company_name" class="block mt-1 w-full" type="text" name="company_name"
                                             value="{{ $product->name }}"/>
                                    @error('company_name')
                                    <span class="text-red-500 text-sm">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </form>
                </div>
                <x-section-border/>
                <div x-data="{ tab: 'first'}">
                    <nav>
                        <div class="flex-row">
                            <button :class="{ 'tabActive bg-gray-400 button-tab-active': tab === 'first', 'bg-gray-200': tab !== 'first' }"
                                    class="rounded px-4 py-2 mr-2 relative  button-tab" @click="tab = 'first'">
                                Prijsafspraken
                            </button>

                        </div>
                    </nav>
                    <x-section-border/>
                    <div>
                        <div x-show="tab === 'first'">
{{--                            @livewire('actions', ['model' => 'customer', 'modelId' => $customer->id, 'actionable' => $customer])--}}
                            <livewire:product-price-agreement :productid="$product->id" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
