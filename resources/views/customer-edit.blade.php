@php use App\Models\Customer; @endphp
<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ $customer->company_name }}
            </h2>
            <div>
                @livewire('change-owner', ['model' => Customer::class, 'ownerId' => $customer->ownedBy->id ?? null, 'entityId' => $customer->id])
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <div class="flex">
                    <div>
                        <form method="POST" action="{{ route('customer.update', ['customer' => $customer->id]) }}"
                              class="w-full max-w-2xl" style="padding-right: 150px;">
                            @csrf
                            <div class="flex items-center mb-6">
                                <div class="w-1/3">
                                    <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4"
                                             for="exact_id" value="{{ __('Exact ID') }}"/>
                                </div>
                                <div class="w-2/3">
                                    <x-input id="exact_id" class="block mt-1 w-full" type="text" name="exact_id"
                                             value="{{ $customer->exact_id }}"/>
                                    @error('exact_id')
                                    <span class="text-red-500 text-sm">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="flex items-center mb-6">
                                <div class="w-1/3">
                                    <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4"
                                             for="company_name" value="{{ __('Bedrijfsnaam') }}"/>
                                </div>
                                <div class="w-2/3">
                                    <x-input id="company_name" class="block mt-1 w-full" type="text" name="company_name"
                                             value="{{ $customer->company_name }}"/>
                                    @error('company_name')
                                    <span class="text-red-500 text-sm">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="flex items-center mb-6">
                                <div class="w-1/3">
                                    <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4"
                                             for="name" value="{{ __('Contactpersoon') }}"/>
                                </div>
                                <div class="w-2/3">
                                    <x-input id="name" class="block mt-1 w-full" type="text" name="name"
                                             value="{{ $customer->name }}"/>
                                    @error('name')
                                    <span class="text-red-500 text-sm">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="flex items-center mb-6">
                                <div class="w-1/3">
                                    <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4"
                                             for="telephone" value="{{ __('Telefoon nummer') }}"/>
                                </div>
                                <div class="w-2/3">
                                    <x-input id="telephone" class="block mt-1 w-full" type="text" name="telephone"
                                             value="{{ $customer->telephone }}"/>
                                    @error('telephone')
                                    <span class="text-red-500 text-sm">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="flex items-center mb-6">
                                <div class="w-1/3">
                                    <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4"
                                             for="street" value="{{ __('Straat + huisnummer') }}"/>
                                </div>
                                <div class="w-2/3 flex">
                                    <x-input id="street" class="block mt-1 w-full mr-2" type="text" name="street"
                                             value="{{ $customer->street_name }}"/>
                                    @error('street_name')
                                    <span class="text-red-500 text-sm">{{ $message }}</span>
                                    @enderror
                                    <x-input id="housenumber" class="block mt-1 w-25" type="text" name="housenumber"
                                             value="{{ $customer->housenumber }}"/>
                                    @error('housenumber')
                                    <span class="text-red-500 text-sm">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="flex items-center mb-6">
                                <div class="w-1/3">
                                    <x-label class="block text-gray-500 font-bold md:text-right mb-1 md:mb-0 pr-4"
                                             for="postal_code" value="{{ __('Postcode + Plaats') }}"/>
                                </div>
                                <div class="w-2/3 flex">
                                    <x-input id="postal_code" class="block mt-1 w-45 mr-2" type="text"
                                             name="postal_code" value="{{ $customer->postal_code }}"/>
                                    @error('postal_code')
                                    <span class="text-red-500 text-sm">{{ $message }}</span>
                                    @enderror
                                    <x-input id="city" class="block mt-1 w-full" type="text" name="city"
                                             value="{{ $customer->city }}"/>
                                    @error('city')
                                    <span class="text-red-500 text-sm">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>

                            <div class="flex items-center mb-6">
                                <div class="w-1/3">
                                </div>
                                <div class="w-2/3">
                                    <x-button>
                                        {{ __('Opslaan') }}
                                    </x-button>
                                </div>
                            </div>
                        </form>
                        @livewire('product-group-selector', ['currentSelectedGroups' => old('product_groups', $customer->productGroups->pluck('id')->toArray()), 'entity' => $customer])

                    </div>
                    <div class="w-45">
                        @livewire('last-five', ['model' => $customer])
                        @livewire('entity-description', ['model' => 'customer', 'modelId' => $customer->id])
                    </div>
                </div>
                <div>
                    <small><a href="{{ route('customer.activities', $customer) }}">Toon Wijzigingen Log</a></small>
                </div>
                <x-section-border/>
                <div x-data="{ tab: '{{ request()->input('taskcompleted') == '1' ? 'second' : 'first' }}' }">
                    <nav>
                        <div class="flex-row">
                            <button :class="{ 'tabActive bg-gray-400 button-tab-active': tab === 'first', 'bg-gray-200': tab !== 'first' }"
                                    class="rounded px-4 py-2 mr-2 relative  button-tab" @click="tab = 'first'">
                                Acties
                            </button>
                            <button :class="{ 'tabActive bg-gray-400 button-tab-active': tab === 'second', 'bg-gray-200': tab !== 'second' }"
                                    class="rounded px-4 py-2 mr-2 relative  button-tab" @click="tab = 'second'">
                                Contactpersonen
                            </button>
                            <button :class="{ 'tabActive bg-gray-400 button-tab-active': tab === 'fifth', 'bg-gray-200': tab !== 'fifth' }"
                                    class="rounded px-4 py-2 mr-2 relative  button-tab" @click="tab = 'fifth'">Bijlagen
                            </button>
                            <button :class="{ 'tabActive bg-gray-400 button-tab-active': tab === 'sixth', 'bg-gray-200': tab !== 'sixth' }"
                                    class="rounded px-4 py-2 mr-2 relative  button-tab" @click="tab = 'sixth'">Taken (oud)
                            </button>
                            <button :class="{ 'tabActive bg-gray-400 button-tab-active': tab === 'seventh', 'bg-gray-200': tab !== 'seventh' }"
                                    class="rounded px-4 py-2 mr-2 relative  button-tab" @click="tab = 'seventh'">Producten
                            </button>
                        </div>
                    </nav>
                    <x-section-border/>
                    <div>
                        <div x-show="tab === 'first'">
                            @livewire('actions', ['model' => 'customer', 'modelId' => $customer->id, 'actionable' => $customer])
                        </div>

                        <div x-show="tab === 'second'">
                            @livewire('contact-persons', ['contactable' => $customer])
                        </div>

                        <div x-show="tab === 'fifth'">
                            @livewire('file-upload', ['model' => $customer])
                        </div>

                        <div x-show="tab === 'sixth'">
                            @livewire('add-task', ['model' => 'customer', 'modelId' => $customer->id, 'taskable' => $customer])
                        </div>

                        <div x-show="tab === 'seventh'">
                            <livewire:customer-products :customer="$customer" />
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
