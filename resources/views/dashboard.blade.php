<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Dashboard') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 flex justify-between">
                <div style="width: 45%">
                    <div x-data="{ tab: 'first' }">
                        <nav>
                            <div class="flex-row">
                                <button :class="{ 'tabActive bg-gray-400 button-tab-active': tab === 'first', 'bg-gray-200': tab !== 'first' }"
                                        class="rounded px-4 py-2 mr-2 relative  button-tab" @click="tab = 'first'">
                                    Voor mij
                                </button>
                                <button :class="{ 'tabActive bg-gray-400 button-tab-active': tab === 'second', 'bg-gray-200': tab !== 'second' }"
                                        class="rounded px-4 py-2 mr-2 relative  button-tab" @click="tab = 'second'">
                                    Voor anderen
                                </button>
                                <button :class="{ 'tabActive bg-gray-400 button-tab-active': tab === 'fourth', 'bg-gray-200': tab !== 'fourth' }"
                                        class="rounded px-4 py-2 mr-2 relative  button-tab" @click="tab = 'fourth'">
                                    Fourage
                                </button>
                                <button :class="{ 'tabActive bg-gray-400 button-tab-active': tab === 'third', 'bg-gray-200': tab !== 'third' }"
                                        class="rounded px-4 py-2 mr-2 relative  button-tab" @click="tab = 'third'">
                                    <small>Taken (oud)</small>
                                </button>
                            </div>
                        </nav>
                        <x-section-border/>
                        <div>
                            <div x-show="tab === 'first'">
                                <livewire:overdue-tasks />
                                <livewire:my-week-tasks />
                            </div>

                            <div x-show="tab === 'second'">
                                <livewire:my-created-tasks />
                            </div>
                            <div x-show="tab === 'fourth'">
                                <livewire:fourage-actions />
                            </div>
                            <div x-show="tab === 'third'">
                                <livewire:deprecated-tasks />
                            </div>


                        </div>
                    </div>
                </div>
                <div style="width: 45%">
                    <livewire:notifications />
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
