@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

[x-cloak] {
  display: none;
}
// Fonts
@import url('https://fonts.bunny.net/css?family=Nunito');

// Variables

// Bootstrap
@import '../../node_modules/bootstrap/scss/bootstrap';

// DataTables
@import '../../node_modules/bootstrap-icons/font/bootstrap-icons.css';
@import '../../node_modules/datatables.net-bs5/css/dataTables.bootstrap5.min.css';
@import '../../node_modules/datatables.net-buttons-bs5/css/buttons.bootstrap5.min.css';
@import '../../node_modules/datatables.net-select-bs5/css/select.bootstrap5.css';

.bg-green-300 {
  background-color: #86efac;
}

.bg-green-400 {
  background-color: #4ade80;
}

.bg-indigo-300 {
  background-color: #a5b4fc;
}

.bg-indigo-400 {
  background-color: #818cf8;
}

.bg-gray-400 {
  background-color: #94a3b8;
}

.button-tab::before {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid #94a3b8;
  display: none;
}
.button-tab-active::before {
  display: block;
}

.w-third {
  width: 33.3%;
}

.w-45 {
  width: 45%;
}

.w-49 {
  width: 49%;
}

.dropdown-content {
  max-height: 80vh; /* 80% of the viewport height */
  overflow-y: auto; /* Allow vertical scrolling */
}

.toggle {
  cursor: pointer;
  display: inline-block;
}

.toggle-switch {
  display: inline-block;
  background: #ccc;
  border-radius: 16px;
  width: 58px;
  height: 32px;
  position: relative;
  vertical-align: middle;
  transition: background 0.25s;
}
.toggle-switch:before, .toggle-switch:after {
  content: "";
}
.toggle-switch:before {
  display: block;
  background: linear-gradient(to bottom, #fff 0%, #eee 100%);
  border-radius: 50%;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.25);
  width: 24px;
  height: 24px;
  position: absolute;
  top: 4px;
  left: 4px;
  transition: left 0.25s;
}
.toggle:hover .toggle-switch:before {
  background: linear-gradient(to bottom, #fff 0%, #fff 100%);
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.5);
}
.toggle-checkbox:checked + .toggle-switch {
  background: #56c080;
}
.toggle-checkbox:checked + .toggle-switch:before {
  left: 30px;
}

.toggle-checkbox {
  position: absolute;
  visibility: hidden;
}

.toggle-label {
  margin-left: 5px;
  position: relative;
  top: 2px;
}

.accordion {
  background-color: rgba(73, 71, 71, 0.28);
}

label:after {
  content: '+';
  position: absolute;
  right: 1em;
  color: #fff;
}

input:checked + label:after {
  content: '-';
  line-height: .8em;
}

.accordion__content{
  max-height: 0em;
  transition: all 0.4s cubic-bezier(0.865, 0.14, 0.095, 0.87);
}
input[name='panel']:checked ~ .accordion__content {
  /* Get this as close to what height you expect */
  max-height: 50em;
}

.pricelist-tab {
  position: absolute;
  top: -64px;
}

.tabActive {
  color: #000;
}

.pricelist-button-tab {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.rotated-text {
  transform: rotate(-90deg);
  transform-origin: left top; /* Adjust as needed */
  white-space: nowrap; /* Prevent text from wrapping */
  display: inline-block; /* Ensure the element is treated as a block-level element */
}
