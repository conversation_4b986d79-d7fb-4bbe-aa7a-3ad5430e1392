<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multiple File Upload Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto bg-white p-6 rounded-lg shadow-md">
        <h1 class="text-2xl font-bold mb-4">Multiple File Upload</h1>
        
        <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
                Select Multiple Files
            </label>
            <input 
                type="file" 
                multiple 
                accept="image/*,.pdf,.doc,.docx,.txt" 
                id="file-input"
                class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
            >
            <p class="text-xs text-gray-500 mt-1">
                Accepts: Images, PDF, Word documents, Text files
            </p>
        </div>
        
        <div class="mb-6">
            <button 
                id="upload-btn" 
                class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition disabled:bg-gray-400"
                disabled
            >
                Upload Files
            </button>
            <button 
                id="clear-btn" 
                class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition ml-2"
            >
                Clear Selection
            </button>
        </div>
        
        <!-- File Preview Section -->
        <div id="file-preview" class="mb-6 hidden">
            <h3 class="text-lg font-semibold mb-3">Selected Files</h3>
            <div id="file-list" class="space-y-2">
                <!-- Files will be listed here -->
            </div>
        </div>
        
        <!-- Upload Progress -->
        <div id="upload-progress" class="mb-6 hidden">
            <h3 class="text-lg font-semibold mb-3">Upload Progress</h3>
            <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div id="progress-bar" class="bg-blue-600 h-2.5 rounded-full transition-all duration-300" style="width: 0%"></div>
            </div>
            <p id="progress-text" class="text-sm text-gray-600 mt-2">0% complete</p>
        </div>
        
        <!-- Results Section -->
        <div id="results" class="hidden">
            <h3 class="text-lg font-semibold mb-3">Upload Results</h3>
            <div id="results-content" class="space-y-2">
                <!-- Results will be shown here -->
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.getElementById('file-input');
            const uploadBtn = document.getElementById('upload-btn');
            const clearBtn = document.getElementById('clear-btn');
            const filePreview = document.getElementById('file-preview');
            const fileList = document.getElementById('file-list');
            const uploadProgress = document.getElementById('upload-progress');
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');
            const results = document.getElementById('results');
            const resultsContent = document.getElementById('results-content');
            
            let selectedFiles = [];
            
            // Handle file selection
            fileInput.addEventListener('change', function(e) {
                selectedFiles = Array.from(e.target.files);
                updateFilePreview();
                uploadBtn.disabled = selectedFiles.length === 0;
            });
            
            // Clear selection
            clearBtn.addEventListener('click', function() {
                fileInput.value = '';
                selectedFiles = [];
                updateFilePreview();
                uploadBtn.disabled = true;
                hideProgress();
                hideResults();
            });
            
            // Upload files
            uploadBtn.addEventListener('click', function() {
                if (selectedFiles.length === 0) return;
                uploadFiles();
            });
            
            function updateFilePreview() {
                if (selectedFiles.length === 0) {
                    filePreview.classList.add('hidden');
                    return;
                }
                
                filePreview.classList.remove('hidden');
                fileList.innerHTML = '';
                
                selectedFiles.forEach((file, index) => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'flex items-center justify-between p-3 bg-gray-50 rounded border';
                    
                    const fileInfo = document.createElement('div');
                    fileInfo.className = 'flex items-center space-x-3';
                    
                    const fileIcon = getFileIcon(file.type);
                    const fileName = document.createElement('span');
                    fileName.className = 'font-medium';
                    fileName.textContent = file.name;
                    
                    const fileSize = document.createElement('span');
                    fileSize.className = 'text-sm text-gray-500';
                    fileSize.textContent = formatFileSize(file.size);
                    
                    fileInfo.appendChild(fileIcon);
                    fileInfo.appendChild(fileName);
                    fileInfo.appendChild(fileSize);
                    
                    const removeBtn = document.createElement('button');
                    removeBtn.className = 'text-red-500 hover:text-red-700 text-sm';
                    removeBtn.textContent = 'Remove';
                    removeBtn.onclick = () => removeFile(index);
                    
                    fileItem.appendChild(fileInfo);
                    fileItem.appendChild(removeBtn);
                    fileList.appendChild(fileItem);
                });
            }
            
            function removeFile(index) {
                selectedFiles.splice(index, 1);
                updateFilePreview();
                uploadBtn.disabled = selectedFiles.length === 0;
                
                // Update file input
                const dt = new DataTransfer();
                selectedFiles.forEach(file => dt.items.add(file));
                fileInput.files = dt.files;
            }
            
            function getFileIcon(mimeType) {
                const icon = document.createElement('div');
                icon.className = 'w-8 h-8 flex items-center justify-center rounded text-white text-xs font-bold';
                
                if (mimeType.startsWith('image/')) {
                    icon.className += ' bg-green-500';
                    icon.textContent = 'IMG';
                } else if (mimeType === 'application/pdf') {
                    icon.className += ' bg-red-500';
                    icon.textContent = 'PDF';
                } else if (mimeType.includes('word') || mimeType.includes('document')) {
                    icon.className += ' bg-blue-500';
                    icon.textContent = 'DOC';
                } else {
                    icon.className += ' bg-gray-500';
                    icon.textContent = 'FILE';
                }
                
                return icon;
            }
            
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
            
            function uploadFiles() {
                showProgress();
                hideResults();
                
                // Simulate file upload with progress
                let uploadedCount = 0;
                const totalFiles = selectedFiles.length;
                
                selectedFiles.forEach((file, index) => {
                    // Simulate upload delay
                    setTimeout(() => {
                        uploadedCount++;
                        const progress = (uploadedCount / totalFiles) * 100;
                        updateProgress(progress);
                        
                        if (uploadedCount === totalFiles) {
                            setTimeout(() => {
                                hideProgress();
                                showResults();
                            }, 500);
                        }
                    }, (index + 1) * 1000); // Stagger uploads
                });
            }
            
            function showProgress() {
                uploadProgress.classList.remove('hidden');
                updateProgress(0);
            }
            
            function hideProgress() {
                uploadProgress.classList.add('hidden');
            }
            
            function updateProgress(percent) {
                progressBar.style.width = percent + '%';
                progressText.textContent = Math.round(percent) + '% complete';
            }
            
            function showResults() {
                results.classList.remove('hidden');
                resultsContent.innerHTML = '';
                
                selectedFiles.forEach(file => {
                    const resultItem = document.createElement('div');
                    resultItem.className = 'flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded';
                    
                    const fileInfo = document.createElement('span');
                    fileInfo.textContent = file.name;
                    
                    const status = document.createElement('span');
                    status.className = 'text-green-600 font-medium';
                    status.textContent = 'Uploaded Successfully';
                    
                    resultItem.appendChild(fileInfo);
                    resultItem.appendChild(status);
                    resultsContent.appendChild(resultItem);
                });
            }
            
            function hideResults() {
                results.classList.add('hidden');
            }
        });
    </script>
</body>
</html>
