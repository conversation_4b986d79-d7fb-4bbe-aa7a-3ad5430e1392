<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Livewire File Upload Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto bg-white p-6 rounded-lg shadow-md">
        <h1 class="text-2xl font-bold mb-4">Livewire File Upload Debug</h1>
        
        <div class="mb-6">
            <h2 class="text-lg font-semibold mb-3">Test File Upload</h2>
            
            <!-- Simple file input -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Select File</label>
                <input type="file" id="test-file" class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
            </div>
            
            <button id="test-upload" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                Test Upload to Purchase
            </button>
        </div>
        
        <div id="results" class="mt-6">
            <!-- Results will appear here -->
        </div>
    </div>
    
    <script>
        document.getElementById('test-upload').addEventListener('click', function() {
            const fileInput = document.getElementById('test-file');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('Please select a file first');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', file);
            formData.append('purchase_id', '1'); // Use a test purchase ID
            
            // Test the upload
            fetch('/api/test-upload', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('results').innerHTML = `
                    <div class="p-4 ${data.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'} rounded">
                        <h3 class="font-semibold">Result:</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            })
            .catch(error => {
                document.getElementById('results').innerHTML = `
                    <div class="p-4 bg-red-100 text-red-800 rounded">
                        <h3 class="font-semibold">Error:</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            });
        });
    </script>
</body>
</html>
