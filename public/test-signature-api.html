<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Signature API Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto bg-white p-6 rounded-lg shadow-md">
        <h1 class="text-2xl font-bold mb-4">Purchase Signature API Test</h1>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Signature Drawing Section -->
            <div>
                <h2 class="text-lg font-semibold mb-4">Draw Signature</h2>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Purchase Number</label>
                    <input type="text" id="purchase-number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Enter purchase number" value="P001">
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Signature Canvas</label>
                    <canvas id="signature-canvas" width="400" height="200" class="border border-gray-300 rounded cursor-crosshair bg-white"></canvas>
                </div>
                
                <div class="flex space-x-2 mb-4">
                    <button id="clear-btn" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition">Clear</button>
                    <button id="submit-btn" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition">Submit Signature</button>
                </div>
                
                <div id="signature-preview" class="hidden">
                    <h3 class="text-sm font-medium text-gray-700 mb-2">Signature Preview:</h3>
                    <img id="preview-image" class="border border-gray-300 rounded max-w-full" alt="Signature preview">
                </div>
            </div>
            
            <!-- Response Section -->
            <div>
                <h2 class="text-lg font-semibold mb-4">API Response</h2>
                <div id="loading" class="hidden text-blue-600 mb-4">Submitting signature...</div>
                <pre id="response" class="bg-gray-100 p-4 rounded overflow-auto max-h-96 text-sm whitespace-pre-wrap"></pre>
            </div>
        </div>
        
        <!-- Test with Sample Signature -->
        <div class="mt-8 border-t pt-6">
            <h2 class="text-lg font-semibold mb-4">Test with Sample Signature</h2>
            <div class="flex space-x-4 items-end">
                <div class="flex-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Purchase Number</label>
                    <input type="text" id="sample-purchase-number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Enter purchase number" value="P002">
                </div>
                <button id="submit-sample-btn" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition">Submit Sample Signature</button>
            </div>

            <!-- Get Signature Section -->
            <div class="mt-4 flex space-x-4 items-end">
                <div class="flex-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Get Signature for Purchase Number</label>
                    <input type="text" id="get-purchase-number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Enter purchase number" value="P001">
                </div>
                <button id="get-signature-btn" class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition">Get Signature</button>
            </div>

            <div id="signature-display" class="mt-4 hidden">
                <h3 class="text-sm font-medium text-gray-700 mb-2">Retrieved Signature:</h3>
                <div class="border border-gray-300 rounded-lg p-4 bg-gray-50">
                    <img id="retrieved-signature" class="max-w-full h-auto border border-gray-200 rounded" alt="Retrieved signature">
                    <p id="signature-info" class="text-sm text-gray-600 mt-2"></p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const canvas = document.getElementById('signature-canvas');
            const ctx = canvas.getContext('2d');
            const clearBtn = document.getElementById('clear-btn');
            const submitBtn = document.getElementById('submit-btn');
            const submitSampleBtn = document.getElementById('submit-sample-btn');
            const getSignatureBtn = document.getElementById('get-signature-btn');
            const purchaseNumberInput = document.getElementById('purchase-number');
            const samplePurchaseNumberInput = document.getElementById('sample-purchase-number');
            const getPurchaseNumberInput = document.getElementById('get-purchase-number');
            const responseEl = document.getElementById('response');
            const loadingEl = document.getElementById('loading');
            const previewEl = document.getElementById('signature-preview');
            const previewImage = document.getElementById('preview-image');
            
            let isDrawing = false;
            let hasSignature = false;
            
            // Set up canvas
            ctx.strokeStyle = '#000000';
            ctx.lineWidth = 2;
            ctx.lineCap = 'round';
            
            // Drawing functions
            function startDrawing(e) {
                isDrawing = true;
                hasSignature = true;
                const rect = canvas.getBoundingClientRect();
                ctx.beginPath();
                ctx.moveTo(e.clientX - rect.left, e.clientY - rect.top);
            }
            
            function draw(e) {
                if (!isDrawing) return;
                const rect = canvas.getBoundingClientRect();
                ctx.lineTo(e.clientX - rect.left, e.clientY - rect.top);
                ctx.stroke();
            }
            
            function stopDrawing() {
                if (isDrawing) {
                    isDrawing = false;
                    updatePreview();
                }
            }
            
            function updatePreview() {
                const dataURL = canvas.toDataURL('image/png');
                previewImage.src = dataURL;
                previewEl.classList.remove('hidden');
            }
            
            // Event listeners for drawing
            canvas.addEventListener('mousedown', startDrawing);
            canvas.addEventListener('mousemove', draw);
            canvas.addEventListener('mouseup', stopDrawing);
            canvas.addEventListener('mouseout', stopDrawing);
            
            // Touch events for mobile
            canvas.addEventListener('touchstart', function(e) {
                e.preventDefault();
                const touch = e.touches[0];
                const mouseEvent = new MouseEvent('mousedown', {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                });
                canvas.dispatchEvent(mouseEvent);
            });
            
            canvas.addEventListener('touchmove', function(e) {
                e.preventDefault();
                const touch = e.touches[0];
                const mouseEvent = new MouseEvent('mousemove', {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                });
                canvas.dispatchEvent(mouseEvent);
            });
            
            canvas.addEventListener('touchend', function(e) {
                e.preventDefault();
                const mouseEvent = new MouseEvent('mouseup', {});
                canvas.dispatchEvent(mouseEvent);
            });
            
            // Clear button
            clearBtn.addEventListener('click', function() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                hasSignature = false;
                previewEl.classList.add('hidden');
                responseEl.textContent = '';
            });
            
            // Submit signature
            submitBtn.addEventListener('click', function() {
                if (!hasSignature) {
                    alert('Please draw a signature first.');
                    return;
                }
                
                const purchaseNumber = purchaseNumberInput.value.trim();
                if (!purchaseNumber) {
                    alert('Please enter a purchase number.');
                    return;
                }
                
                const signatureData = canvas.toDataURL('image/png');
                submitSignature(purchaseNumber, signatureData);
            });
            
            // Submit sample signature
            submitSampleBtn.addEventListener('click', function() {
                const purchaseNumber = samplePurchaseNumberInput.value.trim();
                if (!purchaseNumber) {
                    alert('Please enter a purchase number.');
                    return;
                }
                
                // Sample base64 signature (small PNG)
                const sampleSignature = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
                submitSignature(purchaseNumber, sampleSignature);
            });

            // Get signature button
            getSignatureBtn.addEventListener('click', function() {
                const purchaseNumber = getPurchaseNumberInput.value.trim();
                if (!purchaseNumber) {
                    alert('Please enter a purchase number.');
                    return;
                }

                getSignature(purchaseNumber);
            });

            // Function to get signature from API
            async function getSignature(purchaseNumber) {
                loadingEl.classList.remove('hidden');
                responseEl.textContent = '';
                document.getElementById('signature-display').classList.add('hidden');

                try {
                    const response = await fetch(`/api/purchase-signature?purchase_number=${encodeURIComponent(purchaseNumber)}`, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json'
                        }
                    });

                    const result = await response.json();

                    loadingEl.classList.add('hidden');

                    // Display response
                    responseEl.textContent = JSON.stringify(result, null, 2);

                    if (result.success && result.purchase.signature) {
                        responseEl.className = 'bg-green-100 p-4 rounded overflow-auto max-h-96 text-sm whitespace-pre-wrap';

                        // Display the signature
                        const retrievedSignature = document.getElementById('retrieved-signature');
                        const signatureInfo = document.getElementById('signature-info');

                        retrievedSignature.src = result.purchase.signature;
                        signatureInfo.textContent = `Received: ${result.purchase.signature_received_at} | Size: ${result.purchase.signature_size} bytes`;

                        document.getElementById('signature-display').classList.remove('hidden');
                    } else {
                        responseEl.className = 'bg-red-100 p-4 rounded overflow-auto max-h-96 text-sm whitespace-pre-wrap';
                    }

                } catch (error) {
                    loadingEl.classList.add('hidden');
                    responseEl.textContent = 'Error: ' + error.message;
                    responseEl.className = 'bg-red-100 p-4 rounded overflow-auto max-h-96 text-sm whitespace-pre-wrap';
                }
            }

            // Function to submit signature to API
            async function submitSignature(purchaseNumber, signatureData) {
                loadingEl.classList.remove('hidden');
                responseEl.textContent = '';
                
                try {
                    const response = await fetch('/api/purchase-signature', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify({
                            purchase_number: purchaseNumber,
                            signature: signatureData
                        })
                    });
                    
                    const result = await response.json();
                    
                    loadingEl.classList.add('hidden');
                    
                    // Display response
                    responseEl.textContent = JSON.stringify(result, null, 2);
                    
                    if (result.success) {
                        responseEl.className = 'bg-green-100 p-4 rounded overflow-auto max-h-96 text-sm whitespace-pre-wrap';
                    } else {
                        responseEl.className = 'bg-red-100 p-4 rounded overflow-auto max-h-96 text-sm whitespace-pre-wrap';
                    }
                    
                } catch (error) {
                    loadingEl.classList.add('hidden');
                    responseEl.textContent = 'Error: ' + error.message;
                    responseEl.className = 'bg-red-100 p-4 rounded overflow-auto max-h-96 text-sm whitespace-pre-wrap';
                }
            }
        });
    </script>
</body>
</html>
