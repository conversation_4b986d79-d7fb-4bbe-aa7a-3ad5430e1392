<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Truck Location Updates</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto bg-white p-6 rounded-lg shadow-md">
        <h1 class="text-2xl font-bold mb-4">Test Truck Location Updates</h1>
        
        <div class="mb-6">
            <h2 class="text-lg font-semibold mb-2">Send Location Update</h2>
            <form id="location-form" class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Latitude</label>
                        <input type="number" id="latitude" step="0.0000001" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" value="52.3676">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Longitude</label>
                        <input type="number" id="longitude" step="0.0000001" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" value="4.9041">
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700">Device ID</label>
                    <input type="text" id="device-id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" value="test-truck-1">
                </div>
                
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Accuracy (meters)</label>
                        <input type="number" id="accuracy" step="0.1" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" value="10.5">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Altitude (meters)</label>
                        <input type="number" id="altitude" step="0.1" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" value="42.3">
                    </div>
                </div>
                
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Speed (m/s)</label>
                        <input type="number" id="speed" step="0.1" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" value="5.2">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Heading (degrees)</label>
                        <input type="number" id="heading" step="0.1" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" value="90.5">
                    </div>
                </div>
                
                <div class="flex space-x-4">
                    <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition">Send Update</button>
                    <button type="button" id="simulate-btn" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition">Simulate Movement</button>
                    <button type="button" id="stop-btn" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition hidden">Stop Simulation</button>
                </div>
            </form>
        </div>
        
        <div class="border-t pt-4">
            <h2 class="text-lg font-semibold mb-2">Response</h2>
            <pre id="response" class="bg-gray-100 p-4 rounded overflow-auto max-h-60 text-sm"></pre>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('location-form');
            const responseEl = document.getElementById('response');
            const simulateBtn = document.getElementById('simulate-btn');
            const stopBtn = document.getElementById('stop-btn');
            let simulationInterval = null;
            
            // Function to send location update
            async function sendLocationUpdate() {
                const data = {
                    latitude: parseFloat(document.getElementById('latitude').value),
                    longitude: parseFloat(document.getElementById('longitude').value),
                    timestamp: Date.now(),
                    accuracy: parseFloat(document.getElementById('accuracy').value),
                    altitude: parseFloat(document.getElementById('altitude').value),
                    speed: parseFloat(document.getElementById('speed').value),
                    heading: parseFloat(document.getElementById('heading').value),
                    device_id: document.getElementById('device-id').value
                };
                
                try {
                    const response = await fetch('/api/truck-location', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(data)
                    });
                    
                    const result = await response.json();
                    responseEl.textContent = JSON.stringify(result, null, 2);
                    
                    return response.ok;
                } catch (error) {
                    responseEl.textContent = 'Error: ' + error.message;
                    return false;
                }
            }
            
            // Handle form submission
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                sendLocationUpdate();
            });
            
            // Simulate movement
            simulateBtn.addEventListener('click', function() {
                if (simulationInterval) return;
                
                simulateBtn.classList.add('hidden');
                stopBtn.classList.remove('hidden');
                
                // Send initial update
                sendLocationUpdate();
                
                // Set up interval to simulate movement
                simulationInterval = setInterval(() => {
                    // Get current values
                    let lat = parseFloat(document.getElementById('latitude').value);
                    let lng = parseFloat(document.getElementById('longitude').value);
                    let heading = parseFloat(document.getElementById('heading').value);
                    let speed = parseFloat(document.getElementById('speed').value);
                    
                    // Calculate new position based on heading and speed
                    // This is a simplified calculation that doesn't account for Earth's curvature
                    // For more accuracy, you'd use the Haversine formula
                    const latChange = Math.cos(heading * Math.PI / 180) * speed * 0.00001;
                    const lngChange = Math.sin(heading * Math.PI / 180) * speed * 0.00001;
                    
                    // Update position
                    lat += latChange;
                    lng += lngChange;
                    
                    // Slightly vary heading and speed
                    heading += (Math.random() - 0.5) * 10;
                    if (heading < 0) heading += 360;
                    if (heading >= 360) heading -= 360;
                    
                    speed += (Math.random() - 0.5) * 0.5;
                    if (speed < 0) speed = 0;
                    
                    // Update form values
                    document.getElementById('latitude').value = lat;
                    document.getElementById('longitude').value = lng;
                    document.getElementById('heading').value = heading;
                    document.getElementById('speed').value = speed;
                    
                    // Send update
                    sendLocationUpdate();
                }, 5000); // Update every 5 seconds for testing
            });
            
            // Stop simulation
            stopBtn.addEventListener('click', function() {
                if (simulationInterval) {
                    clearInterval(simulationInterval);
                    simulationInterval = null;
                    
                    simulateBtn.classList.remove('hidden');
                    stopBtn.classList.add('hidden');
                }
            });
        });
    </script>
</body>
</html>
