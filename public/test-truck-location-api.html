<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Truck Location API Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto bg-white p-6 rounded-lg shadow-md">
        <h1 class="text-2xl font-bold mb-4">Truck Location API Test</h1>
        
        <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p class="text-sm text-blue-800">
                <strong>Enhanced API:</strong> Now uses number plate as the primary truck identifier. Number plate is required for proper truck tracking on the map.
            </p>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Submit Location Section -->
            <div>
                <h2 class="text-lg font-semibold mb-4">Submit Truck Location</h2>
                
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Latitude</label>
                            <input type="number" id="latitude" step="0.000001" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="52.3676" value="52.3676">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Longitude</label>
                            <input type="number" id="longitude" step="0.000001" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="4.9041" value="4.9041">
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Device ID</label>
                            <input type="text" id="device-id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="TRUCK001" value="TRUCK001">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Number Plate</label>
                            <input type="text" id="number-plate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="AB-123-CD" value="AB-123-CD">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Current State</label>
                            <select id="current-state" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">Select state...</option>
                                <option value="driving">Driving</option>
                                <option value="resting">Resting</option>
                                <option value="loading">Loading</option>
                                <option value="unloading">Unloading</option>
                                <option value="waiting">Waiting</option>
                                <option value="maintenance">Maintenance</option>
                                <option value="break">Break</option>
                                <option value="off_duty">Off Duty</option>
                                <option value="on_duty">On Duty</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Speed (m/s)</label>
                            <input type="number" id="speed" step="0.1" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="15.5" value="15.5">
                            <p class="text-xs text-gray-500 mt-1">Will be displayed as km/h on map</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Heading (°)</label>
                            <input type="number" id="heading" step="0.1" min="0" max="360" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="90" value="90">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Accuracy (m)</label>
                            <input type="number" id="accuracy" step="0.1" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="5.0" value="5.0">
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Altitude (m)</label>
                        <input type="number" id="altitude" step="0.1" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="10.5" value="10.5">
                    </div>
                    
                    <div class="flex space-x-2">
                        <button id="submit-location-btn" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition">Submit Location</button>
                        <button id="random-location-btn" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition">Random Location</button>
                    </div>
                </div>
            </div>
            
            <!-- Get Locations Section -->
            <div>
                <h2 class="text-lg font-semibold mb-4">Get Latest Locations</h2>
                
                <div class="mb-4">
                    <button id="get-locations-btn" class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition">Get All Locations</button>
                </div>
                
                <div id="locations-display" class="space-y-2 max-h-96 overflow-y-auto">
                    <!-- Locations will be displayed here -->
                </div>
            </div>
        </div>
        
        <!-- Response Section -->
        <div class="mt-8">
            <h2 class="text-lg font-semibold mb-4">API Response</h2>
            <div id="loading" class="hidden text-blue-600 mb-4">Processing...</div>
            <pre id="response" class="bg-gray-100 p-4 rounded overflow-auto max-h-96 text-sm whitespace-pre-wrap"></pre>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const submitBtn = document.getElementById('submit-location-btn');
            const randomBtn = document.getElementById('random-location-btn');
            const getLocationsBtn = document.getElementById('get-locations-btn');
            const loadingEl = document.getElementById('loading');
            const responseEl = document.getElementById('response');
            const locationsDisplay = document.getElementById('locations-display');
            
            // Submit location
            submitBtn.addEventListener('click', function() {
                const locationData = {
                    latitude: parseFloat(document.getElementById('latitude').value),
                    longitude: parseFloat(document.getElementById('longitude').value),
                    timestamp: Date.now(),
                    device_id: document.getElementById('device-id').value,
                    number_plate: document.getElementById('number-plate').value,
                    current_state: document.getElementById('current-state').value || null,
                    speed: parseFloat(document.getElementById('speed').value) || null,
                    heading: parseFloat(document.getElementById('heading').value) || null,
                    accuracy: parseFloat(document.getElementById('accuracy').value) || null,
                    altitude: parseFloat(document.getElementById('altitude').value) || null
                };
                
                submitLocation(locationData);
            });
            
            // Random location
            randomBtn.addEventListener('click', function() {
                // Generate random location in Netherlands area
                const lat = 52.3676 + (Math.random() - 0.5) * 2;
                const lng = 4.9041 + (Math.random() - 0.5) * 2;

                document.getElementById('latitude').value = lat.toFixed(6);
                document.getElementById('longitude').value = lng.toFixed(6);
                document.getElementById('speed').value = (Math.random() * 30).toFixed(1);
                document.getElementById('heading').value = (Math.random() * 360).toFixed(1);

                // Update speed display
                updateSpeedDisplay();
            });

            // Add speed conversion display
            const speedInput = document.getElementById('speed');
            speedInput.addEventListener('input', updateSpeedDisplay);

            function updateSpeedDisplay() {
                const speedMs = parseFloat(speedInput.value) || 0;
                const speedKmh = (speedMs * 3.6).toFixed(1);
                const existingDisplay = document.getElementById('speed-kmh-display');

                if (existingDisplay) {
                    existingDisplay.textContent = `≈ ${speedKmh} km/h`;
                } else {
                    const display = document.createElement('p');
                    display.id = 'speed-kmh-display';
                    display.className = 'text-xs text-blue-600 mt-1';
                    display.textContent = `≈ ${speedKmh} km/h`;
                    speedInput.parentNode.appendChild(display);
                }
            }

            // Initialize speed display
            updateSpeedDisplay();
            
            // Get locations
            getLocationsBtn.addEventListener('click', function() {
                getLatestLocations();
            });
            
            // Function to submit location
            async function submitLocation(data) {
                loadingEl.classList.remove('hidden');
                responseEl.textContent = '';
                
                try {
                    const response = await fetch('/api/truck-location', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(data)
                    });
                    
                    const result = await response.json();
                    
                    loadingEl.classList.add('hidden');
                    responseEl.textContent = JSON.stringify(result, null, 2);
                    
                    if (result.success || response.ok) {
                        responseEl.className = 'bg-green-100 p-4 rounded overflow-auto max-h-96 text-sm whitespace-pre-wrap';
                        // Auto-refresh locations after successful submission
                        setTimeout(getLatestLocations, 1000);
                    } else {
                        responseEl.className = 'bg-red-100 p-4 rounded overflow-auto max-h-96 text-sm whitespace-pre-wrap';
                    }
                    
                } catch (error) {
                    loadingEl.classList.add('hidden');
                    responseEl.textContent = 'Error: ' + error.message;
                    responseEl.className = 'bg-red-100 p-4 rounded overflow-auto max-h-96 text-sm whitespace-pre-wrap';
                }
            }
            
            // Function to get latest locations
            async function getLatestLocations() {
                loadingEl.classList.remove('hidden');
                
                try {
                    const response = await fetch('/api/truck-locations/latest');
                    const locations = await response.json();
                    
                    loadingEl.classList.add('hidden');
                    
                    // Display locations in the sidebar
                    locationsDisplay.innerHTML = '';
                    
                    if (locations.length === 0) {
                        locationsDisplay.innerHTML = '<p class="text-gray-500 text-sm">No locations found</p>';
                    } else {
                        locations.forEach(location => {
                            const locationCard = document.createElement('div');
                            locationCard.className = 'p-3 border border-gray-200 rounded bg-gray-50';
                            
                            // Use number plate as primary identifier
                            const primaryId = location.number_plate || (location.user ? location.user.name : `Device ${location.device_id}`);
                            const secondaryInfo = location.number_plate ?
                                (location.user ? `Driver: ${location.user.name}` : `Device: ${location.device_id}`) :
                                '';

                            // Function to get state badge
                            function getStateBadge(state) {
                                if (!state) return '';

                                const stateColors = {
                                    'driving': 'bg-green-100 text-green-800',
                                    'resting': 'bg-blue-100 text-blue-800',
                                    'loading': 'bg-yellow-100 text-yellow-800',
                                    'unloading': 'bg-orange-100 text-orange-800',
                                    'waiting': 'bg-gray-100 text-gray-800',
                                    'maintenance': 'bg-red-100 text-red-800',
                                    'break': 'bg-purple-100 text-purple-800',
                                    'off_duty': 'bg-gray-100 text-gray-600',
                                    'on_duty': 'bg-green-100 text-green-700'
                                };

                                const colorClass = stateColors[state] || 'bg-gray-100 text-gray-800';
                                const displayState = state.replace('_', ' ').toUpperCase();

                                return `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${colorClass}">${displayState}</span>`;
                            }

                            locationCard.innerHTML = `
                                <h4 class="font-semibold text-sm">${primaryId}</h4>
                                ${secondaryInfo ? `<p class="text-xs text-gray-600">${secondaryInfo}</p>` : ''}
                                ${location.current_state ? `<div class="mb-1">${getStateBadge(location.current_state)}</div>` : ''}
                                <p class="text-xs text-gray-600">Lat: ${location.latitude.toFixed(6)}</p>
                                <p class="text-xs text-gray-600">Lng: ${location.longitude.toFixed(6)}</p>
                                <p class="text-xs text-gray-600">Updated: ${new Date(parseInt(location.timestamp)).toLocaleString()}</p>
                                ${location.speed ? `<p class="text-xs text-gray-600">Speed: ${(location.speed * 3.6).toFixed(1)} km/h</p>` : ''}
                            `;
                            
                            locationsDisplay.appendChild(locationCard);
                        });
                    }
                    
                    // Also show in response area
                    responseEl.textContent = JSON.stringify(locations, null, 2);
                    responseEl.className = 'bg-blue-100 p-4 rounded overflow-auto max-h-96 text-sm whitespace-pre-wrap';
                    
                } catch (error) {
                    loadingEl.classList.add('hidden');
                    responseEl.textContent = 'Error: ' + error.message;
                    responseEl.className = 'bg-red-100 p-4 rounded overflow-auto max-h-96 text-sm whitespace-pre-wrap';
                }
            }
            
            // Load initial locations
            getLatestLocations();
        });
    </script>
</body>
</html>
