<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Leaflet Map Test</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        
        #container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        h1 {
            color: #333;
        }
        
        #map {
            width: 100%;
            height: 500px;
            border: 1px solid #ccc;
            margin-bottom: 20px;
        }
        
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div id="container">
        <h1>Leaflet Map Test</h1>
        <p>This is a standalone test page for the Leaflet map.</p>
        
        <div id="map"></div>
        
        <button id="add-marker">Add Random Marker</button>
    </div>
    
    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <script>
        // Initialize the map
        const map = L.map('map').setView([52.3676, 4.9041], 7);
        
        // Add the OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 19,
            attribution: '&copy; <a href="https://openstreetmap.org/copyright">OpenStreetMap contributors</a>'
        }).addTo(map);
        
        // Add a default marker
        L.marker([52.3676, 4.9041])
            .addTo(map)
            .bindPopup('Amsterdam')
            .openPopup();
        
        // Add random marker button functionality
        document.getElementById('add-marker').addEventListener('click', function() {
            // Generate random coordinates near the Netherlands
            const lat = 52.3676 + (Math.random() - 0.5) * 2;
            const lng = 4.9041 + (Math.random() - 0.5) * 2;
            
            // Add a new marker
            L.marker([lat, lng])
                .addTo(map)
                .bindPopup(`Random Marker<br>Lat: ${lat.toFixed(4)}, Lng: ${lng.toFixed(4)}`)
                .openPopup();
        });
    </script>
</body>
</html>
