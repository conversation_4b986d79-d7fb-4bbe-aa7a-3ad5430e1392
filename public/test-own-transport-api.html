<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Own Transport API Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto bg-white p-6 rounded-lg shadow-md">
        <h1 class="text-2xl font-bold mb-4">Own Transport Purchases API Test</h1>

        <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p class="text-sm text-blue-800">
                <strong>Note:</strong> This API returns purchases with own transport that don't have a signature yet (signature IS NULL).
            </p>
        </div>

        <div class="mb-6">
            <button id="fetch-btn" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition">
                Fetch Own Transport Purchases (No Signature)
            </button>
            <span id="loading" class="ml-4 text-gray-600 hidden">Loading...</span>
        </div>
        
        <div id="results" class="space-y-4">
            <!-- Results will be displayed here -->
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const fetchBtn = document.getElementById('fetch-btn');
            const loading = document.getElementById('loading');
            const results = document.getElementById('results');
            
            fetchBtn.addEventListener('click', async function() {
                loading.classList.remove('hidden');
                results.innerHTML = '';
                
                try {
                    const response = await fetch('/api/own-transport-purchases');
                    const data = await response.json();
                    
                    loading.classList.add('hidden');
                    
                    if (data.success) {
                        displayResults(data);
                    } else {
                        results.innerHTML = '<div class="p-4 bg-red-100 text-red-700 rounded">Error: ' + (data.message || 'Unknown error') + '</div>';
                    }
                } catch (error) {
                    loading.classList.add('hidden');
                    results.innerHTML = '<div class="p-4 bg-red-100 text-red-700 rounded">Error: ' + error.message + '</div>';
                }
            });
            
            function displayResults(data) {
                const summary = `
                    <div class="p-4 bg-blue-100 text-blue-800 rounded mb-4">
                        <h2 class="font-bold text-lg">Summary</h2>
                        <p>Total purchases with own transport (no signature): ${data.count}</p>
                        <p>API Response: ${data.message}</p>
                        <p class="text-sm mt-2">These purchases are awaiting digital signatures from drivers.</p>
                    </div>
                `;
                
                results.innerHTML = summary;
                
                if (data.data.length === 0) {
                    results.innerHTML += '<div class="p-4 bg-yellow-100 text-yellow-700 rounded">No purchases with own transport found.</div>';
                    return;
                }
                
                data.data.forEach(purchase => {
                    const purchaseHtml = `
                        <div class="border border-gray-300 rounded-lg p-4 mb-4">
                            <div class="flex justify-between items-start mb-3">
                                <h3 class="text-lg font-semibold">Purchase #${purchase.purchase_number || purchase.id}</h3>
                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Own Transport</span>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <h4 class="font-medium text-gray-700">Purchase Details</h4>
                                    <p><strong>Supplier:</strong> ${purchase.supplier.company_name} (${purchase.supplier.city})</p>
                                    <p><strong>Product:</strong> ${purchase.product.name}</p>
                                    <p><strong>Quantity:</strong> ${purchase.quantity} ${purchase.qty_type}</p>
                                    <p><strong>Price:</strong> €${purchase.price}</p>
                                    <p><strong>Total:</strong> €${purchase.total}</p>
                                    <p><strong>Load Date:</strong> ${purchase.load_date}</p>
                                    <p><strong>Delivery Date:</strong> ${purchase.delivery_date || 'Not set'}</p>
                                    ${purchase.load_weight ? `<p><strong>Load Weight:</strong> ${purchase.load_weight} kg</p>` : ''}
                                    ${purchase.unload_weight ? `<p><strong>Unload Weight:</strong> ${purchase.unload_weight} kg</p>` : ''}
                                    <p><strong>Created by:</strong> ${purchase.created_by || 'Unknown'}</p>
                                </div>

                                <div>
                                    <h4 class="font-medium text-gray-700">Loading Address</h4>
                                    <div class="bg-gray-50 p-3 rounded border">
                                        <p><strong>Address:</strong> ${purchase.supplier.loading_address.street} ${purchase.supplier.loading_address.housenumber}</p>
                                        <p><strong>City:</strong> ${purchase.supplier.loading_address.postal_code} ${purchase.supplier.loading_address.city}</p>
                                        <p><strong>Country:</strong> ${purchase.supplier.loading_address.country}</p>
                                        ${purchase.supplier.loading_address.loading_times ? `<p><strong>Loading Times:</strong> ${purchase.supplier.loading_address.loading_times}</p>` : ''}
                                        ${purchase.supplier.loading_address.is_custom_address ? '<p class="text-orange-600 text-sm"><em>Custom address</em></p>' : '<p class="text-green-600 text-sm"><em>Default supplier address</em></p>'}
                                        ${purchase.supplier.loading_address.latitude && purchase.supplier.loading_address.longitude ?
                                            `<p class="text-xs text-gray-500">Coordinates: ${purchase.supplier.loading_address.latitude}, ${purchase.supplier.loading_address.longitude}</p>` :
                                            '<p class="text-xs text-gray-500">No coordinates available</p>'
                                        }
                                    </div>
                                </div>
                                
                                <div>
                                    <h4 class="font-medium text-gray-700">Status</h4>
                                    <p><strong>Ready for Planning:</strong> ${purchase.ready_for_planning ? 'Yes' : 'No'}</p>
                                    <p><strong>Planned:</strong> ${purchase.planned ? 'Yes' : 'No'}</p>
                                    <p><strong>Completed:</strong> ${purchase.completed ? 'Yes' : 'No'}</p>
                                    <p><strong>Sales Count:</strong> ${purchase.sales_count}</p>
                                    <p><strong>Total Sales Quantity:</strong> ${purchase.total_sales_quantity}</p>
                                    <p><strong>Total Sales Value:</strong> €${purchase.total_sales_value}</p>
                                </div>
                            </div>
                            
                            ${purchase.sales.length > 0 ? `
                                <div>
                                    <h4 class="font-medium text-gray-700 mb-2">Sales (${purchase.sales.length})</h4>
                                    <div class="overflow-x-auto">
                                        <table class="min-w-full bg-gray-50 rounded">
                                            <thead>
                                                <tr class="bg-gray-200">
                                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase">Customer</th>
                                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase">Delivery Address</th>
                                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase">Quantity</th>
                                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase">Price</th>
                                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase">Total</th>
                                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase">Delivery Date</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${purchase.sales.map(sale => `
                                                    <tr class="border-b border-gray-200">
                                                        <td class="px-3 py-2 text-sm">${sale.customer.company_name}<br><small class="text-gray-500">${sale.customer.city}</small></td>
                                                        <td class="px-3 py-2 text-sm">
                                                            ${sale.shipping_address ? `
                                                                ${sale.shipping_address.name || 'Address'}<br>
                                                                <small class="text-gray-500">
                                                                    ${sale.shipping_address.street} ${sale.shipping_address.housenumber}<br>
                                                                    ${sale.shipping_address.postal_code} ${sale.shipping_address.city}
                                                                    ${sale.shipping_address.unloading_times ? '<br>Unloading: ' + sale.shipping_address.unloading_times : ''}
                                                                </small>
                                                            ` : 'No address'}
                                                        </td>
                                                        <td class="px-3 py-2 text-sm">
                                                            ${sale.actual_quantity && sale.actual_quantity !== sale.quantity ? 
                                                                `<span class="line-through text-gray-500">${sale.quantity}</span><br><strong>${sale.actual_quantity}</strong>` : 
                                                                sale.quantity
                                                            } ${sale.sale_qty_type}
                                                        </td>
                                                        <td class="px-3 py-2 text-sm">€${sale.price}</td>
                                                        <td class="px-3 py-2 text-sm">€${sale.total_price}</td>
                                                        <td class="px-3 py-2 text-sm">${sale.delivery_date || 'Not set'}</td>
                                                    </tr>
                                                `).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            ` : '<p class="text-gray-500">No sales for this purchase.</p>'}
                        </div>
                    `;
                    
                    results.innerHTML += purchaseHtml;
                });
            }
        });
    </script>
</body>
</html>
