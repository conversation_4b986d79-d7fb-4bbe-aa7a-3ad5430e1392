# Daily Sales Summary Email System

## Overview

The Daily Sales Summary system automatically sends personalized email reports to each system user containing their sales data from completed purchases. The emails are sent daily at a configurable time and include detailed information about purchases and associated sales.

## Features

- **Automated Daily Emails**: Sent at 8:00 AM by default (configurable)
- **Personalized Content**: Each user receives only their own sales data
- **Comprehensive Data**: Includes purchase details, sales information, and shipping addresses
- **Summary Statistics**: Total sales count, revenue, and transport costs
- **Error Handling**: Robust error handling with detailed logging
- **Flexible Scheduling**: Configurable send time and timezone

## Email Content

Each daily summary email includes:

### Header Section
- Personalized greeting with user's name
- Summary date (previous day)
- Overall statistics (total sales, revenue, transport costs)

### Purchase Details
For each completed purchase with user's sales:
- Purchase order number
- Supplier information
- Product details (name, SKU, quantity, price)
- Delivery date

### Sales Information
For each sale created by the user:
- Customer name
- Sale quantity and price
- Transport costs
- Total amount (including transport)
- Shipping address details

### No Data Handling
If a user has no sales for the day, they receive a friendly "No sales completed yesterday" message.

## Configuration

### Environment Variables

Add these to your `.env` file to customize the system:

```env
# Email send time (24-hour format)
DAILY_SALES_SUMMARY_TIME=08:00

# Timezone for scheduling
DAILY_SALES_SUMMARY_TIMEZONE=Europe/Amsterdam

# Email settings
DAILY_SALES_SUMMARY_FROM_NAME="Sales System"
DAILY_SALES_SUMMARY_FROM_ADDRESS=<EMAIL>
DAILY_SALES_SUMMARY_SUBJECT_PREFIX="Daily Sales Summary"

# Logging
DAILY_SALES_SUMMARY_LOGGING=true
```

### Configuration File

The system uses `config/daily-sales-summary.php` for detailed configuration options.

## Manual Execution

### Run for Yesterday (Default)
```bash
php artisan email:daily-sales-summary
```

### Run for Specific Date
```bash
php artisan email:daily-sales-summary --date=2024-01-15
```

### Test with Specific Purchase (Testing Mode)
```bash
php artisan email:daily-sales-summary --purchase=P001
```

This testing mode:
- Finds the purchase by purchase number
- Sends emails to all users who created sales for that purchase
- Uses today's date as the summary date
- Provides detailed output about the purchase and users
- Perfect for testing email templates and functionality

## Scheduling

The system is automatically scheduled to run daily via Laravel's task scheduler. Ensure your server has a cron job set up:

```bash
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

## User Filtering

Emails are only sent to users who:
- Have a valid email address
- Have created at least one sale in the system
- Are active in the system

## Logging

The system provides comprehensive logging:

### Success Logs
- User email sent successfully
- Summary statistics
- Process completion summary

### Error Logs
- Failed email sends with error details
- User processing errors
- Full stack traces for debugging

### Log Locations
- Application logs: `storage/logs/laravel.log`
- Email logs: Check your mail driver logs

## Database Requirements

The system requires these relationships:
- `users.id` → `sales.user_id`
- `purchases.completed = 1` for completed purchases
- `purchases.updated_at` used as completion timestamp

## Testing

Run the test suite to verify functionality:

```bash
php artisan test --filter=DailySalesSummaryTest
```

## Troubleshooting

### No Emails Being Sent

1. Check if the scheduler is running:
   ```bash
   php artisan schedule:list
   ```

2. Verify mail configuration:
   ```bash
   php artisan config:cache
   ```

3. Check logs for errors:
   ```bash
   tail -f storage/logs/laravel.log
   ```

### Users Not Receiving Emails

1. Verify user has valid email address
2. Check if user has created sales
3. Confirm purchases were completed yesterday
4. Review error logs for specific user issues

### Email Content Issues

1. Check if relationships are properly loaded
2. Verify data exists for the specified date
3. Test with manual command execution

### Testing Mode Issues

1. **Purchase not found**: Verify the purchase number exists in the database
2. **No sales for purchase**: Check if the purchase has associated sales
3. **Users without emails**: Ensure users who created sales have valid email addresses
4. **Email template errors**: Check the Blade template syntax and data structure

## Customization

### Email Template

Modify `resources/views/emails/daily-sales-summary.blade.php` to customize the email appearance and content.

### Command Logic

Update `app/Console/Commands/SendDailySalesSummary.php` to modify the data selection or processing logic.

### Mailable Class

Customize `app/Mail/DailySalesSummary.php` to change email structure or add additional data.

## Security Considerations

- Emails contain sensitive business data
- Ensure secure email transport (TLS/SSL)
- Verify recipient email addresses are correct
- Consider data retention policies for email logs
