# Purchase Logging System

This document describes the comprehensive logging system implemented for the Purchase model to track all user manipulations.

## Overview

Every change made to a Purchase record is automatically logged with:
- Timestamp (Dutch format: dd-mm-yyyy HH:mm:ss)
- User name who made the change
- Field that was changed (in Dutch)
- Old and new values (formatted appropriately)

## Implementation

### Model Observer
The system uses a Laravel Model Observer (`PurchaseObserver`) that automatically captures:
- **Created**: When a new purchase is created
- **Updated**: When any field is modified
- **Deleted**: When a purchase is deleted

### Automatic Field Tracking
The observer tracks changes to all relevant fields including:

#### Basic Purchase Data
- `quantity` → "Hoeveelheid"
- `price` → "Prijs" 
- `total` → "Totaal"
- `load_date` → "Laaddatum"
- `delivery_date` → "Losdatum"
- `description` → "Beschrijving"
- `qty_type` → "Eenheid"

#### Supplier Information
- `supplier_id` → "Leverancier"
- `supplier_purchase_number` → "Leverancier inkoopnummer"
- `supplier_street` → "Leverancier straat"
- `supplier_housenumber` → "Leverancier huisnummer"
- `supplier_postal_code` → "Leverancier postcode"
- `supplier_city` → "Leverancier plaats"
- `supplier_country` → "Leverancier land"
- `custom_supplier_address` → "Aangepast leverancier adres"

#### Weight and Transport
- `load_weight` → "Laadgewicht"
- `unload_weight` → "Losgewicht"
- `shipper_id` → "Vervoerder"

#### Status Fields
- `ready_for_planning` → "Klaar voor planning"
- `completed` → "Voltooid"
- `sale_exact` → "Verkoop exact"
- `exact` → "Exact"

#### Digital Signatures
- `signature` → "Handtekening"
- `signature_received_at` → "Handtekening ontvangen op"

#### Administrative
- `administration` → "Administratie"
- `comments` → "Opmerkingen"

### Value Formatting
Values are automatically formatted for readability:

- **Dates**: `15-01-2025` format
- **Timestamps**: `15-01-2025 14:30` format
- **Money**: `€25,50` format
- **Weights**: `10.500,50 kg` format
- **Quantities**: `10.5 TON` format
- **Booleans**: `ja`/`nee` format
- **Relations**: Shows company/product names instead of IDs
- **Empty values**: Shows `leeg`

## Log Format

Each log entry is formatted as an HTML list item for easy display:
```html
<li>15-01-2025 14:30 - Jan Jansen: Hoeveelheid gewijzigd van '10.5 TON' naar '15.0 TON'</li>
```

For creation, additional details are included:
```html
<li>15-01-2025 14:30 - Jan Jansen: Inkoop aangemaakt (purchase_number: 250001, supplier: Test Leverancier, product: Test Product, quantity: 10.5 TON, price: €25,50, total: €267,75)</li>
```

This format allows the log to be displayed directly in HTML views using `{!! $purchase->log !!}` within a `<ul>` container.

## Livewire Integration

### RefreshesLog Trait
A trait is provided for Livewire components to easily refresh the log display:

```php
use App\Traits\RefreshesLog;

class EditPurchase extends Component
{
    use RefreshesLog;
    
    public function updatedQuantity()
    {
        $this->purchase->quantity = $this->quantity;
        $this->savePurchaseAndRefreshLog(); // Saves and refreshes log
    }
}
```

### Updated Components
The following Livewire components have been updated to use automatic logging:
- `EditPurchase` - All field updates
- `CompletedPurchase` - Date changes
- `Aanbod` - Bulk operations
- `Plannable` - Date updates

## Commands

### Initialize Logs for Existing Purchases
```bash
# See what purchases need log initialization
php artisan purchase:init-logs --dry-run

# Initialize logs for purchases without them
php artisan purchase:init-logs
```

## Testing

Comprehensive tests verify the logging system:
```bash
php artisan test tests/Feature/PurchaseLoggingTest.php
```

Tests cover:
- Purchase creation logging
- Field change logging
- Value formatting
- User attribution
- Timestamp accuracy

## Benefits

### Complete Audit Trail
- Every change is tracked with user and timestamp
- No manual logging code needed in components
- Consistent format across all changes

### User-Friendly Display
- Dutch field names for better user understanding
- Properly formatted values (dates, money, weights)
- Clear before/after change indication

### Developer-Friendly
- Automatic - no need to remember to add logging code
- Extensible - easy to add new fields
- Testable - comprehensive test coverage

### Business Value
- Full accountability for all changes
- Compliance with audit requirements
- Easy troubleshooting of data changes
- Historical tracking of purchase modifications

## Migration from Manual Logging

The system replaces the previous manual logging approach where developers had to remember to add logging code like:

```php
// OLD WAY - Manual logging (removed)
$this->log .= '<li>' . Carbon::now()->format('d-m-Y H:i') . ' - ' . auth()->user()->name . ': Laaddatum gewijzigd naar ' . $date . '</li>';
$this->purchase->log = $this->log;
$this->purchase->save();

// NEW WAY - Automatic logging
$this->purchase->load_date = $date;
$this->purchase->save(); // Observer handles logging automatically
```

This ensures no changes go unlogged and maintains consistency across the application.
