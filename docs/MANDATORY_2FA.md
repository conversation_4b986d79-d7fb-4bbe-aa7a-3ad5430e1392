# Mandatory Two-Factor Authentication (2FA)

This application now requires all authenticated users to have Two-Factor Authentication (2FA) enabled to access protected resources.

## How It Works

### Middleware Implementation
- A new middleware `RequireTwoFactorAuth` has been added to enforce 2FA requirements
- The middleware is applied to all authenticated routes in both web and API contexts
- Users without 2FA enabled are redirected to their profile page to set it up

### Protected Routes
All routes in the authenticated middleware group require 2FA:
- Web routes: Dashboard, customers, suppliers, purchases, etc.
- API routes: User data, file uploads, orders, etc.

### Excluded Routes
The following routes are excluded from 2FA requirements to allow users to set up 2FA:
- Two-factor authentication setup and management routes
- Profile page (`profile.show`)
- Password confirmation
- Logout
- Profile update routes

### Public Routes
Some API endpoints remain public for external integrations:
- Truck location tracking (`/api/truck-location`)
- Digital signature submission (`/api/purchase-signature`)
- External data imports (`/api/data`, `/api/products`, etc.)

## User Experience

### For Users Without 2FA
1. User logs in successfully
2. When accessing any protected route, they are redirected to their profile page
3. A prominent red alert message explains that 2FA is required
4. User must enable 2FA in the "Two Factor Authentication" section
5. Once 2FA is confirmed, user can access all application features

### For Users With 2FA
1. User logs in with username/password
2. User enters 2FA code from their authenticator app
3. User has full access to all application features

## API Behavior

### Protected API Endpoints
- Return HTTP 403 with JSON error message when 2FA is not enabled
- Include redirect URL to profile page in the response

### Public API Endpoints
- Continue to work without authentication for external integrations
- Truck tracking, signature submission, and data imports remain accessible

## Administration

### Check 2FA Status
Use the artisan command to check how many users have 2FA enabled:

```bash
# Basic status report
php artisan auth:check-2fa

# Show detailed list of users without 2FA
php artisan auth:check-2fa --show-users
```

### Migration Strategy
1. Deploy the 2FA middleware
2. Notify all users that 2FA will be mandatory
3. Run the status check command to identify users without 2FA
4. Provide support to help users enable 2FA
5. Monitor the status until all users have 2FA enabled

## Technical Details

### Files Modified/Added
- `app/Http/Middleware/RequireTwoFactorAuth.php` - Main middleware
- `app/Http/Kernel.php` - Middleware registration
- `routes/web.php` - Applied to authenticated routes
- `routes/api.php` - Applied to protected API routes
- `resources/views/profile/show.blade.php` - Alert message
- `tests/Feature/RequireTwoFactorAuthMiddlewareTest.php` - Tests
- `app/Console/Commands/CheckTwoFactorStatus.php` - Admin command

### Dependencies
- Laravel Fortify (already configured)
- Laravel Jetstream (already configured)
- Two-factor authentication feature enabled in Fortify config

## Testing

Run the test suite to verify the middleware works correctly:

```bash
php artisan test tests/Feature/RequireTwoFactorAuthMiddlewareTest.php
```

The tests verify:
- Users without 2FA are redirected to profile page
- Users with 2FA can access protected routes
- Profile page is accessible without 2FA
- API endpoints return proper error responses
- Public API endpoints remain accessible
