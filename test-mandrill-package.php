<?php

require __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';

$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

// Send a test email
try {
    // Get the from address from config
    $fromAddress = config('mail.from.address');
    $fromName = config('mail.from.name');
    
    echo "Sending test email via Mandrill package...\n";
    echo "From: {$fromAddress} ({$fromName})\n";
    echo "Mailer: " . config('mail.default') . "\n";
    echo "Mandrill Key: " . substr(config('mail.mailers.mandrill.secret'), 0, 5) . "...\n\n";
    
    Illuminate\Support\Facades\Mail::raw('Test email from Mandrill package', function ($message) {
        $message->to('<EMAIL>')
                ->subject('Test Mandrill Package Email');
    });
    
    echo "Email sent successfully!\n";
} catch (Exception $e) {
    echo "Error sending email: " . $e->getMessage() . "\n";
    
    // Print more detailed information
    echo "\nDetailed error information:\n";
    echo "Class: " . get_class($e) . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "Trace:\n" . $e->getTraceAsString() . "\n";
}
