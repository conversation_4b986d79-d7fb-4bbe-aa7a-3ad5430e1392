id: 51359
name: Planbord
environments:
  production:
    storage: buscrm-attachments-bucket
    memory: 1024
    timeout: 300
    cli-memory: 512
    runtime: 'php-8.2:al2'
    database: busdb
    build:
      - 'composer install --no-dev'
      - 'php artisan event:cache'
      # - 'npm ci && npm run build && rm -rf node_modules'
    deploy:
      - 'php artisan migrate --force'
    environment:
      # Mail Configuration
      MAIL_MAILER: mandrill
      MAIL_FROM_ADDRESS: '${MAIL_FROM_ADDRESS}'
      MAIL_FROM_NAME: '${MAIL_FROM_NAME}'
      # Mandrill API Key
      MANDRILL_KEY: '${MANDRILL_KEY}'