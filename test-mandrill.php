<?php

require __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';

$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

// Send a test email
try {
    // Get the from address from config
    $fromAddress = config('mail.from.address');
    $fromName = config('mail.from.name');

    Illuminate\Support\Facades\Mail::raw('Test email from Mandrill via SMTP', function ($message) use ($fromAddress, $fromName) {
        $message->to('<EMAIL>')
                ->from($fromAddress, $fromName)
                ->subject('Test Mandrill SMTP Email');
    });

    echo "Email sent successfully!\n";
    echo "From: {$fromAddress} ({$fromName})\n";
} catch (Exception $e) {
    echo "Error sending email: " . $e->getMessage() . "\n";

    // Print more detailed information
    echo "\nDetailed error information:\n";
    echo "Class: " . get_class($e) . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "Trace:\n" . $e->getTraceAsString() . "\n";
}
